2025-08-07 17:41:43,242 - INFO - ========== 字幕 #64 处理开始 ==========
2025-08-07 17:41:43,242 - INFO - 字幕内容: 原来，杀手只是为了拖延时间，真正的幕后黑手已经控制了爷爷，逼他签下股份转让协议。
2025-08-07 17:41:43,242 - INFO - 字幕序号: [2990, 2992]
2025-08-07 17:41:43,242 - INFO - 音频文件详情:
2025-08-07 17:41:43,242 - INFO -   - 路径: output\64.wav
2025-08-07 17:41:43,242 - INFO -   - 时长: 5.12秒
2025-08-07 17:41:43,242 - INFO -   - 验证音频时长: 5.12秒
2025-08-07 17:41:43,242 - INFO - 字幕时间戳信息:
2025-08-07 17:41:43,242 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:43,242 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:43,242 - INFO -   - 根据生成的音频时长(5.12秒)已调整字幕时间戳
2025-08-07 17:41:43,242 - INFO - ========== 新模式：为字幕 #64 生成4套场景方案 ==========
2025-08-07 17:41:43,242 - INFO - 字幕序号列表: [2990, 2992]
2025-08-07 17:41:43,242 - INFO - 
--- 生成方案 #1：基于字幕序号 #2990 ---
2025-08-07 17:41:43,242 - INFO - 开始为单个字幕序号 #2990 匹配场景，目标时长: 5.12秒
2025-08-07 17:41:43,242 - INFO - 开始查找字幕序号 [2990] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:43,244 - INFO - 找到related_overlap场景: scene_id=3268, 字幕#2990
2025-08-07 17:41:43,244 - INFO - 字幕 #2990 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:43,244 - INFO - 字幕序号 #2990 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:43,245 - INFO - 选择第一个overlap场景作为起点: scene_id=3268
2025-08-07 17:41:43,245 - INFO - 添加起点场景: scene_id=3268, 时长=2.68秒, 累计时长=2.68秒
2025-08-07 17:41:43,245 - INFO - 起点场景时长不足，需要延伸填充 2.44秒
2025-08-07 17:41:43,245 - INFO - 起点场景在原始列表中的索引: 3267
2025-08-07 17:41:43,245 - INFO - 延伸添加场景: scene_id=3269 (裁剪至 2.44秒)
2025-08-07 17:41:43,245 - INFO - 累计时长: 5.12秒
2025-08-07 17:41:43,245 - INFO - 字幕序号 #2990 场景匹配完成，共选择 2 个场景，总时长: 5.12秒
2025-08-07 17:41:43,245 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-08-07 17:41:43,245 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-08-07 17:41:43,245 - INFO - 
--- 生成方案 #2：基于字幕序号 #2992 ---
2025-08-07 17:41:43,245 - INFO - 开始为单个字幕序号 #2992 匹配场景，目标时长: 5.12秒
2025-08-07 17:41:43,245 - INFO - 开始查找字幕序号 [2992] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:43,245 - INFO - 找到related_overlap场景: scene_id=3269, 字幕#2992
2025-08-07 17:41:43,246 - INFO - 字幕 #2992 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:43,246 - INFO - 字幕序号 #2992 找到 0 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:43,246 - ERROR - 字幕序号 #2992 没有找到任何可用的匹配场景
2025-08-07 17:41:43,246 - WARNING - 方案 #2 生成失败，未找到合适的场景
2025-08-07 17:41:43,246 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-08-07 17:41:43,246 - INFO - ========== 当前模式：为字幕 #64 生成 1 套场景方案 ==========
2025-08-07 17:41:43,246 - INFO - 开始查找字幕序号 [2990, 2992] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:43,247 - INFO - 找到related_overlap场景: scene_id=3268, 字幕#2990
2025-08-07 17:41:43,247 - INFO - 找到related_overlap场景: scene_id=3269, 字幕#2992
2025-08-07 17:41:43,247 - INFO - 字幕 #2990 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:43,247 - INFO - 字幕 #2992 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:43,247 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-08-07 17:41:43,247 - INFO - 开始生成方案 #1
2025-08-07 17:41:43,247 - INFO - 方案 #1: 为字幕#2990选择初始化overlap场景id=3268
2025-08-07 17:41:43,247 - INFO - 方案 #1: 为字幕#2992选择初始化overlap场景id=3269
2025-08-07 17:41:43,247 - INFO - 方案 #1: 初始选择后，当前总时长=8.24秒
2025-08-07 17:41:43,247 - INFO - 方案 #1: 额外between选择后，当前总时长=8.24秒
2025-08-07 17:41:43,247 - INFO - 方案 #1: 场景总时长(8.24秒)大于音频时长(5.12秒)，需要裁剪
2025-08-07 17:41:43,247 - INFO - 调整前总时长: 8.24秒, 目标时长: 5.12秒
2025-08-07 17:41:43,247 - INFO - 需要裁剪 3.12秒
2025-08-07 17:41:43,247 - INFO - 裁剪最长场景ID=3269：从5.56秒裁剪至2.44秒
2025-08-07 17:41:43,247 - INFO - 调整后总时长: 5.12秒，与目标时长差异: 0.00秒
2025-08-07 17:41:43,247 - INFO - 方案 #1 调整/填充后最终总时长: 5.12秒
2025-08-07 17:41:43,247 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:43,247 - INFO - ========== 当前模式：字幕 #64 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:43,247 - INFO - 方案 #2 (传统模式) 生成成功
2025-08-07 17:41:43,247 - INFO - ========== 新模式：字幕 #64 共生成 2 套有效场景方案 ==========
2025-08-07 17:41:43,247 - INFO - 
----- 处理字幕 #64 的方案 #1 -----
2025-08-07 17:41:43,247 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\64_1.mp4
2025-08-07 17:41:43,248 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp2k72r30o
2025-08-07 17:41:43,248 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3268.mp4 (确认存在: True)
2025-08-07 17:41:43,248 - INFO - 添加场景ID=3268，时长=2.68秒，累计时长=2.68秒
2025-08-07 17:41:43,248 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3269.mp4 (确认存在: True)
2025-08-07 17:41:43,248 - INFO - 添加场景ID=3269，时长=5.56秒，累计时长=8.24秒
2025-08-07 17:41:43,248 - INFO - 场景总时长(8.24秒)已达到音频时长(5.12秒)的1.5倍，停止添加场景
2025-08-07 17:41:43,249 - INFO - 准备合并 2 个场景文件，总时长约 8.24秒
2025-08-07 17:41:43,249 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3268.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3269.mp4'

2025-08-07 17:41:43,249 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp2k72r30o\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp2k72r30o\temp_combined.mp4
2025-08-07 17:41:43,393 - INFO - 合并后的视频时长: 8.29秒，目标音频时长: 5.12秒
2025-08-07 17:41:43,393 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp2k72r30o\temp_combined.mp4 -ss 0 -to 5.118 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\64_1.mp4
2025-08-07 17:41:43,678 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:43,678 - INFO - 目标音频时长: 5.12秒
2025-08-07 17:41:43,678 - INFO - 实际视频时长: 5.14秒
2025-08-07 17:41:43,678 - INFO - 时长差异: 0.02秒 (0.49%)
2025-08-07 17:41:43,678 - INFO - ==========================================
2025-08-07 17:41:43,678 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:43,678 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\64_1.mp4
2025-08-07 17:41:43,679 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp2k72r30o
2025-08-07 17:41:43,723 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:43,723 - INFO -   - 音频时长: 5.12秒
2025-08-07 17:41:43,723 - INFO -   - 视频时长: 5.14秒
2025-08-07 17:41:43,723 - INFO -   - 时长差异: 0.02秒 (0.49%)
2025-08-07 17:41:43,723 - INFO - 
----- 处理字幕 #64 的方案 #2 -----
2025-08-07 17:41:43,723 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\64_2.mp4
2025-08-07 17:41:43,723 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpwfri_gax
2025-08-07 17:41:43,723 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3268.mp4 (确认存在: True)
2025-08-07 17:41:43,723 - INFO - 添加场景ID=3268，时长=2.68秒，累计时长=2.68秒
2025-08-07 17:41:43,723 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3269.mp4 (确认存在: True)
2025-08-07 17:41:43,723 - INFO - 添加场景ID=3269，时长=5.56秒，累计时长=8.24秒
2025-08-07 17:41:43,723 - INFO - 场景总时长(8.24秒)已达到音频时长(5.12秒)的1.5倍，停止添加场景
2025-08-07 17:41:43,723 - INFO - 准备合并 2 个场景文件，总时长约 8.24秒
2025-08-07 17:41:43,723 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3268.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3269.mp4'

2025-08-07 17:41:43,725 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpwfri_gax\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpwfri_gax\temp_combined.mp4
2025-08-07 17:41:43,851 - INFO - 合并后的视频时长: 8.29秒，目标音频时长: 5.12秒
2025-08-07 17:41:43,851 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpwfri_gax\temp_combined.mp4 -ss 0 -to 5.118 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\64_2.mp4
2025-08-07 17:41:44,138 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:44,138 - INFO - 目标音频时长: 5.12秒
2025-08-07 17:41:44,138 - INFO - 实际视频时长: 5.14秒
2025-08-07 17:41:44,138 - INFO - 时长差异: 0.02秒 (0.49%)
2025-08-07 17:41:44,138 - INFO - ==========================================
2025-08-07 17:41:44,138 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:44,138 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\64_2.mp4
2025-08-07 17:41:44,139 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpwfri_gax
2025-08-07 17:41:44,182 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:44,182 - INFO -   - 音频时长: 5.12秒
2025-08-07 17:41:44,182 - INFO -   - 视频时长: 5.14秒
2025-08-07 17:41:44,182 - INFO -   - 时长差异: 0.02秒 (0.49%)
2025-08-07 17:41:44,182 - INFO - 
字幕 #64 处理完成，成功生成 2/2 套方案
2025-08-07 17:41:44,182 - INFO - 生成的视频文件:
2025-08-07 17:41:44,182 - INFO -   1. F:/github/aicut_auto/newcut_ai\64_1.mp4
2025-08-07 17:41:44,182 - INFO -   2. F:/github/aicut_auto/newcut_ai\64_2.mp4
2025-08-07 17:41:44,182 - INFO - ========== 字幕 #64 处理结束 ==========

