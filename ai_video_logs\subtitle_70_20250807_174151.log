2025-08-07 17:41:51,784 - INFO - ========== 字幕 #70 处理开始 ==========
2025-08-07 17:41:51,784 - INFO - 字幕内容: 危急关头，女孩施展六合封禁之术，瞬间化解了危机，将这个罪魁祸首彻底制服。
2025-08-07 17:41:51,784 - INFO - 字幕序号: [3091, 3093]
2025-08-07 17:41:51,784 - INFO - 音频文件详情:
2025-08-07 17:41:51,784 - INFO -   - 路径: output\70.wav
2025-08-07 17:41:51,784 - INFO -   - 时长: 4.63秒
2025-08-07 17:41:51,784 - INFO -   - 验证音频时长: 4.63秒
2025-08-07 17:41:51,785 - INFO - 字幕时间戳信息:
2025-08-07 17:41:51,785 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:51,785 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:51,785 - INFO -   - 根据生成的音频时长(4.63秒)已调整字幕时间戳
2025-08-07 17:41:51,785 - INFO - ========== 新模式：为字幕 #70 生成4套场景方案 ==========
2025-08-07 17:41:51,785 - INFO - 字幕序号列表: [3091, 3093]
2025-08-07 17:41:51,785 - INFO - 
--- 生成方案 #1：基于字幕序号 #3091 ---
2025-08-07 17:41:51,785 - INFO - 开始为单个字幕序号 #3091 匹配场景，目标时长: 4.63秒
2025-08-07 17:41:51,785 - INFO - 开始查找字幕序号 [3091] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:51,785 - INFO - 找到related_overlap场景: scene_id=3432, 字幕#3091
2025-08-07 17:41:51,785 - INFO - 找到related_overlap场景: scene_id=3433, 字幕#3091
2025-08-07 17:41:51,786 - INFO - 字幕 #3091 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:51,786 - INFO - 字幕序号 #3091 找到 2 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:51,786 - INFO - 选择第一个overlap场景作为起点: scene_id=3432
2025-08-07 17:41:51,786 - INFO - 添加起点场景: scene_id=3432, 时长=3.68秒, 累计时长=3.68秒
2025-08-07 17:41:51,786 - INFO - 起点场景时长不足，需要延伸填充 0.95秒
2025-08-07 17:41:51,786 - INFO - 起点场景在原始列表中的索引: 3431
2025-08-07 17:41:51,786 - INFO - 延伸添加场景: scene_id=3433 (裁剪至 0.95秒)
2025-08-07 17:41:51,786 - INFO - 累计时长: 4.63秒
2025-08-07 17:41:51,786 - INFO - 字幕序号 #3091 场景匹配完成，共选择 2 个场景，总时长: 4.63秒
2025-08-07 17:41:51,786 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-08-07 17:41:51,786 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-08-07 17:41:51,786 - INFO - 
--- 生成方案 #2：基于字幕序号 #3093 ---
2025-08-07 17:41:51,786 - INFO - 开始为单个字幕序号 #3093 匹配场景，目标时长: 4.63秒
2025-08-07 17:41:51,786 - INFO - 开始查找字幕序号 [3093] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:51,787 - INFO - 找到related_overlap场景: scene_id=3434, 字幕#3093
2025-08-07 17:41:51,787 - INFO - 找到related_between场景: scene_id=3435, 字幕#3093
2025-08-07 17:41:51,787 - INFO - 找到related_between场景: scene_id=3436, 字幕#3093
2025-08-07 17:41:51,787 - INFO - 字幕 #3093 找到 1 个overlap场景, 2 个between场景
2025-08-07 17:41:51,787 - INFO - 字幕序号 #3093 找到 1 个可用overlap场景, 2 个可用between场景
2025-08-07 17:41:51,787 - INFO - 选择第一个overlap场景作为起点: scene_id=3434
2025-08-07 17:41:51,787 - INFO - 添加起点场景: scene_id=3434, 时长=1.16秒, 累计时长=1.16秒
2025-08-07 17:41:51,787 - INFO - 起点场景时长不足，需要延伸填充 3.47秒
2025-08-07 17:41:51,788 - INFO - 起点场景在原始列表中的索引: 3433
2025-08-07 17:41:51,788 - INFO - 延伸添加场景: scene_id=3435 (完整时长 0.88秒)
2025-08-07 17:41:51,788 - INFO - 累计时长: 2.04秒
2025-08-07 17:41:51,788 - INFO - 延伸添加场景: scene_id=3436 (完整时长 0.88秒)
2025-08-07 17:41:51,788 - INFO - 累计时长: 2.92秒
2025-08-07 17:41:51,788 - INFO - 延伸添加场景: scene_id=3437 (裁剪至 1.71秒)
2025-08-07 17:41:51,788 - INFO - 累计时长: 4.63秒
2025-08-07 17:41:51,788 - INFO - 字幕序号 #3093 场景匹配完成，共选择 4 个场景，总时长: 4.63秒
2025-08-07 17:41:51,788 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-08-07 17:41:51,788 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:51,788 - INFO - ========== 当前模式：为字幕 #70 生成 1 套场景方案 ==========
2025-08-07 17:41:51,788 - INFO - 开始查找字幕序号 [3091, 3093] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:51,788 - INFO - 找到related_overlap场景: scene_id=3432, 字幕#3091
2025-08-07 17:41:51,788 - INFO - 找到related_overlap场景: scene_id=3433, 字幕#3091
2025-08-07 17:41:51,788 - INFO - 找到related_overlap场景: scene_id=3434, 字幕#3093
2025-08-07 17:41:51,789 - INFO - 找到related_between场景: scene_id=3435, 字幕#3093
2025-08-07 17:41:51,789 - INFO - 找到related_between场景: scene_id=3436, 字幕#3093
2025-08-07 17:41:51,789 - INFO - 字幕 #3091 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:51,789 - INFO - 字幕 #3093 找到 1 个overlap场景, 2 个between场景
2025-08-07 17:41:51,789 - INFO - 共收集 3 个未使用的overlap场景和 2 个未使用的between场景
2025-08-07 17:41:51,789 - INFO - 开始生成方案 #1
2025-08-07 17:41:51,789 - INFO - 方案 #1: 为字幕#3091选择初始化overlap场景id=3433
2025-08-07 17:41:51,789 - INFO - 方案 #1: 为字幕#3093选择初始化overlap场景id=3434
2025-08-07 17:41:51,789 - INFO - 方案 #1: 初始选择后，当前总时长=2.12秒
2025-08-07 17:41:51,789 - INFO - 方案 #1: 额外添加overlap场景id=3432, 当前总时长=5.80秒
2025-08-07 17:41:51,789 - INFO - 方案 #1: 额外between选择后，当前总时长=5.80秒
2025-08-07 17:41:51,789 - INFO - 方案 #1: 场景总时长(5.80秒)大于音频时长(4.63秒)，需要裁剪
2025-08-07 17:41:51,789 - INFO - 调整前总时长: 5.80秒, 目标时长: 4.63秒
2025-08-07 17:41:51,789 - INFO - 需要裁剪 1.17秒
2025-08-07 17:41:51,789 - INFO - 裁剪最长场景ID=3432：从3.68秒裁剪至2.51秒
2025-08-07 17:41:51,789 - INFO - 调整后总时长: 4.63秒，与目标时长差异: 0.00秒
2025-08-07 17:41:51,789 - INFO - 方案 #1 调整/填充后最终总时长: 4.63秒
2025-08-07 17:41:51,789 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:51,789 - INFO - ========== 当前模式：字幕 #70 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:51,789 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:51,789 - INFO - ========== 新模式：字幕 #70 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:51,789 - INFO - 
----- 处理字幕 #70 的方案 #1 -----
2025-08-07 17:41:51,789 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\70_1.mp4
2025-08-07 17:41:51,790 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpuwex2an_
2025-08-07 17:41:51,790 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3432.mp4 (确认存在: True)
2025-08-07 17:41:51,790 - INFO - 添加场景ID=3432，时长=3.68秒，累计时长=3.68秒
2025-08-07 17:41:51,790 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3433.mp4 (确认存在: True)
2025-08-07 17:41:51,790 - INFO - 添加场景ID=3433，时长=0.96秒，累计时长=4.64秒
2025-08-07 17:41:51,790 - INFO - 准备合并 2 个场景文件，总时长约 4.64秒
2025-08-07 17:41:51,791 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3432.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3433.mp4'

2025-08-07 17:41:51,791 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpuwex2an_\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpuwex2an_\temp_combined.mp4
2025-08-07 17:41:51,911 - INFO - 合并后的视频时长: 4.69秒，目标音频时长: 4.63秒
2025-08-07 17:41:51,911 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpuwex2an_\temp_combined.mp4 -ss 0 -to 4.628 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\70_1.mp4
2025-08-07 17:41:52,273 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:52,273 - INFO - 目标音频时长: 4.63秒
2025-08-07 17:41:52,273 - INFO - 实际视频时长: 4.66秒
2025-08-07 17:41:52,273 - INFO - 时长差异: 0.04秒 (0.76%)
2025-08-07 17:41:52,273 - INFO - ==========================================
2025-08-07 17:41:52,273 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:52,273 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\70_1.mp4
2025-08-07 17:41:52,275 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpuwex2an_
2025-08-07 17:41:52,321 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:52,321 - INFO -   - 音频时长: 4.63秒
2025-08-07 17:41:52,321 - INFO -   - 视频时长: 4.66秒
2025-08-07 17:41:52,321 - INFO -   - 时长差异: 0.04秒 (0.76%)
2025-08-07 17:41:52,321 - INFO - 
----- 处理字幕 #70 的方案 #2 -----
2025-08-07 17:41:52,321 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\70_2.mp4
2025-08-07 17:41:52,322 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp49wdluv5
2025-08-07 17:41:52,322 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3434.mp4 (确认存在: True)
2025-08-07 17:41:52,322 - INFO - 添加场景ID=3434，时长=1.16秒，累计时长=1.16秒
2025-08-07 17:41:52,322 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3435.mp4 (确认存在: True)
2025-08-07 17:41:52,322 - INFO - 添加场景ID=3435，时长=0.88秒，累计时长=2.04秒
2025-08-07 17:41:52,322 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3436.mp4 (确认存在: True)
2025-08-07 17:41:52,322 - INFO - 添加场景ID=3436，时长=0.88秒，累计时长=2.92秒
2025-08-07 17:41:52,322 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3437.mp4 (确认存在: True)
2025-08-07 17:41:52,322 - INFO - 添加场景ID=3437，时长=3.12秒，累计时长=6.04秒
2025-08-07 17:41:52,322 - INFO - 准备合并 4 个场景文件，总时长约 6.04秒
2025-08-07 17:41:52,323 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3434.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3435.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3436.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3437.mp4'

2025-08-07 17:41:52,323 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp49wdluv5\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp49wdluv5\temp_combined.mp4
2025-08-07 17:41:52,495 - INFO - 合并后的视频时长: 6.13秒，目标音频时长: 4.63秒
2025-08-07 17:41:52,495 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp49wdluv5\temp_combined.mp4 -ss 0 -to 4.628 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\70_2.mp4
2025-08-07 17:41:52,800 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:52,800 - INFO - 目标音频时长: 4.63秒
2025-08-07 17:41:52,800 - INFO - 实际视频时长: 4.66秒
2025-08-07 17:41:52,800 - INFO - 时长差异: 0.04秒 (0.76%)
2025-08-07 17:41:52,800 - INFO - ==========================================
2025-08-07 17:41:52,800 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:52,800 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\70_2.mp4
2025-08-07 17:41:52,801 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp49wdluv5
2025-08-07 17:41:52,846 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:52,846 - INFO -   - 音频时长: 4.63秒
2025-08-07 17:41:52,846 - INFO -   - 视频时长: 4.66秒
2025-08-07 17:41:52,846 - INFO -   - 时长差异: 0.04秒 (0.76%)
2025-08-07 17:41:52,846 - INFO - 
----- 处理字幕 #70 的方案 #3 -----
2025-08-07 17:41:52,846 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\70_3.mp4
2025-08-07 17:41:52,847 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpyu4qlpz5
2025-08-07 17:41:52,847 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3433.mp4 (确认存在: True)
2025-08-07 17:41:52,847 - INFO - 添加场景ID=3433，时长=0.96秒，累计时长=0.96秒
2025-08-07 17:41:52,848 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3434.mp4 (确认存在: True)
2025-08-07 17:41:52,848 - INFO - 添加场景ID=3434，时长=1.16秒，累计时长=2.12秒
2025-08-07 17:41:52,848 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3432.mp4 (确认存在: True)
2025-08-07 17:41:52,848 - INFO - 添加场景ID=3432，时长=3.68秒，累计时长=5.80秒
2025-08-07 17:41:52,848 - INFO - 准备合并 3 个场景文件，总时长约 5.80秒
2025-08-07 17:41:52,848 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3433.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3434.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3432.mp4'

2025-08-07 17:41:52,848 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpyu4qlpz5\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpyu4qlpz5\temp_combined.mp4
2025-08-07 17:41:53,013 - INFO - 合并后的视频时长: 5.87秒，目标音频时长: 4.63秒
2025-08-07 17:41:53,013 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpyu4qlpz5\temp_combined.mp4 -ss 0 -to 4.628 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\70_3.mp4
2025-08-07 17:41:53,322 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:53,322 - INFO - 目标音频时长: 4.63秒
2025-08-07 17:41:53,322 - INFO - 实际视频时长: 4.66秒
2025-08-07 17:41:53,322 - INFO - 时长差异: 0.04秒 (0.76%)
2025-08-07 17:41:53,322 - INFO - ==========================================
2025-08-07 17:41:53,322 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:53,322 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\70_3.mp4
2025-08-07 17:41:53,323 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpyu4qlpz5
2025-08-07 17:41:53,367 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:53,367 - INFO -   - 音频时长: 4.63秒
2025-08-07 17:41:53,367 - INFO -   - 视频时长: 4.66秒
2025-08-07 17:41:53,367 - INFO -   - 时长差异: 0.04秒 (0.76%)
2025-08-07 17:41:53,367 - INFO - 
字幕 #70 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:53,367 - INFO - 生成的视频文件:
2025-08-07 17:41:53,367 - INFO -   1. F:/github/aicut_auto/newcut_ai\70_1.mp4
2025-08-07 17:41:53,367 - INFO -   2. F:/github/aicut_auto/newcut_ai\70_2.mp4
2025-08-07 17:41:53,367 - INFO -   3. F:/github/aicut_auto/newcut_ai\70_3.mp4
2025-08-07 17:41:53,367 - INFO - ========== 字幕 #70 处理结束 ==========

