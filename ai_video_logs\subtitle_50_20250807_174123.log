2025-08-07 17:41:23,625 - INFO - ========== 字幕 #50 处理开始 ==========
2025-08-07 17:41:23,625 - INFO - 字幕内容: 她拿出信物白龙玉佩，并决定当场治好会长的绝症，以证身份，这玉佩正是她当年亲手送给楚家爷爷的。
2025-08-07 17:41:23,625 - INFO - 字幕序号: [2190, 2203]
2025-08-07 17:41:23,625 - INFO - 音频文件详情:
2025-08-07 17:41:23,625 - INFO -   - 路径: output\50.wav
2025-08-07 17:41:23,625 - INFO -   - 时长: 7.26秒
2025-08-07 17:41:23,626 - INFO -   - 验证音频时长: 7.26秒
2025-08-07 17:41:23,626 - INFO - 字幕时间戳信息:
2025-08-07 17:41:23,626 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:23,626 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:23,626 - INFO -   - 根据生成的音频时长(7.26秒)已调整字幕时间戳
2025-08-07 17:41:23,626 - INFO - ========== 新模式：为字幕 #50 生成4套场景方案 ==========
2025-08-07 17:41:23,626 - INFO - 字幕序号列表: [2190, 2203]
2025-08-07 17:41:23,626 - INFO - 
--- 生成方案 #1：基于字幕序号 #2190 ---
2025-08-07 17:41:23,626 - INFO - 开始为单个字幕序号 #2190 匹配场景，目标时长: 7.26秒
2025-08-07 17:41:23,626 - INFO - 开始查找字幕序号 [2190] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:23,627 - INFO - 找到related_overlap场景: scene_id=2447, 字幕#2190
2025-08-07 17:41:23,627 - INFO - 找到related_overlap场景: scene_id=2448, 字幕#2190
2025-08-07 17:41:23,628 - INFO - 字幕 #2190 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:23,628 - INFO - 字幕序号 #2190 找到 2 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:23,628 - INFO - 选择第一个overlap场景作为起点: scene_id=2447
2025-08-07 17:41:23,628 - INFO - 添加起点场景: scene_id=2447, 时长=1.60秒, 累计时长=1.60秒
2025-08-07 17:41:23,628 - INFO - 起点场景时长不足，需要延伸填充 5.67秒
2025-08-07 17:41:23,628 - INFO - 起点场景在原始列表中的索引: 2446
2025-08-07 17:41:23,628 - INFO - 延伸添加场景: scene_id=2448 (完整时长 1.56秒)
2025-08-07 17:41:23,628 - INFO - 累计时长: 3.16秒
2025-08-07 17:41:23,628 - INFO - 延伸添加场景: scene_id=2449 (完整时长 1.80秒)
2025-08-07 17:41:23,628 - INFO - 累计时长: 4.96秒
2025-08-07 17:41:23,628 - INFO - 延伸添加场景: scene_id=2450 (完整时长 1.08秒)
2025-08-07 17:41:23,628 - INFO - 累计时长: 6.04秒
2025-08-07 17:41:23,628 - INFO - 延伸添加场景: scene_id=2451 (裁剪至 1.23秒)
2025-08-07 17:41:23,628 - INFO - 累计时长: 7.26秒
2025-08-07 17:41:23,628 - INFO - 字幕序号 #2190 场景匹配完成，共选择 5 个场景，总时长: 7.26秒
2025-08-07 17:41:23,628 - INFO - 方案 #1 生成成功，包含 5 个场景
2025-08-07 17:41:23,628 - INFO - 新模式：第1套方案的 5 个场景已加入全局已使用集合
2025-08-07 17:41:23,628 - INFO - 
--- 生成方案 #2：基于字幕序号 #2203 ---
2025-08-07 17:41:23,628 - INFO - 开始为单个字幕序号 #2203 匹配场景，目标时长: 7.26秒
2025-08-07 17:41:23,628 - INFO - 开始查找字幕序号 [2203] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:23,629 - INFO - 找到related_overlap场景: scene_id=2460, 字幕#2203
2025-08-07 17:41:23,629 - INFO - 字幕 #2203 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:23,629 - INFO - 字幕序号 #2203 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:23,629 - INFO - 选择第一个overlap场景作为起点: scene_id=2460
2025-08-07 17:41:23,629 - INFO - 添加起点场景: scene_id=2460, 时长=3.12秒, 累计时长=3.12秒
2025-08-07 17:41:23,629 - INFO - 起点场景时长不足，需要延伸填充 4.15秒
2025-08-07 17:41:23,629 - INFO - 起点场景在原始列表中的索引: 2459
2025-08-07 17:41:23,630 - INFO - 延伸添加场景: scene_id=2461 (完整时长 0.84秒)
2025-08-07 17:41:23,630 - INFO - 累计时长: 3.96秒
2025-08-07 17:41:23,630 - INFO - 延伸添加场景: scene_id=2462 (完整时长 1.00秒)
2025-08-07 17:41:23,630 - INFO - 累计时长: 4.96秒
2025-08-07 17:41:23,630 - INFO - 延伸添加场景: scene_id=2463 (完整时长 1.56秒)
2025-08-07 17:41:23,630 - INFO - 累计时长: 6.52秒
2025-08-07 17:41:23,630 - INFO - 延伸添加场景: scene_id=2464 (裁剪至 0.75秒)
2025-08-07 17:41:23,630 - INFO - 累计时长: 7.26秒
2025-08-07 17:41:23,630 - INFO - 字幕序号 #2203 场景匹配完成，共选择 5 个场景，总时长: 7.26秒
2025-08-07 17:41:23,630 - INFO - 方案 #2 生成成功，包含 5 个场景
2025-08-07 17:41:23,630 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:23,630 - INFO - ========== 当前模式：为字幕 #50 生成 1 套场景方案 ==========
2025-08-07 17:41:23,630 - INFO - 开始查找字幕序号 [2190, 2203] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:23,630 - INFO - 找到related_overlap场景: scene_id=2447, 字幕#2190
2025-08-07 17:41:23,630 - INFO - 找到related_overlap场景: scene_id=2448, 字幕#2190
2025-08-07 17:41:23,630 - INFO - 找到related_overlap场景: scene_id=2460, 字幕#2203
2025-08-07 17:41:23,631 - INFO - 字幕 #2190 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:23,631 - INFO - 字幕 #2203 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:23,631 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-08-07 17:41:23,631 - INFO - 开始生成方案 #1
2025-08-07 17:41:23,631 - INFO - 方案 #1: 为字幕#2190选择初始化overlap场景id=2448
2025-08-07 17:41:23,631 - INFO - 方案 #1: 为字幕#2203选择初始化overlap场景id=2460
2025-08-07 17:41:23,631 - INFO - 方案 #1: 初始选择后，当前总时长=4.68秒
2025-08-07 17:41:23,631 - INFO - 方案 #1: 额外添加overlap场景id=2447, 当前总时长=6.28秒
2025-08-07 17:41:23,631 - INFO - 方案 #1: 额外between选择后，当前总时长=6.28秒
2025-08-07 17:41:23,631 - INFO - 方案 #1: 场景总时长(6.28秒)小于音频时长(7.26秒)，需要延伸填充
2025-08-07 17:41:23,631 - INFO - 方案 #1: 最后一个场景ID: 2447
2025-08-07 17:41:23,631 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 2446
2025-08-07 17:41:23,631 - INFO - 方案 #1: 需要填充时长: 0.99秒
2025-08-07 17:41:23,631 - INFO - 方案 #1: 跳过已使用的场景: scene_id=2448
2025-08-07 17:41:23,631 - INFO - 方案 #1: 追加场景 scene_id=2449 (裁剪至 0.99秒)
2025-08-07 17:41:23,631 - INFO - 方案 #1: 成功填充至目标时长
2025-08-07 17:41:23,631 - INFO - 方案 #1 调整/填充后最终总时长: 7.26秒
2025-08-07 17:41:23,631 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:23,631 - INFO - ========== 当前模式：字幕 #50 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:23,631 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:23,631 - INFO - ========== 新模式：字幕 #50 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:23,631 - INFO - 
----- 处理字幕 #50 的方案 #1 -----
2025-08-07 17:41:23,631 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\50_1.mp4
2025-08-07 17:41:23,632 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxe_2t_ss
2025-08-07 17:41:23,632 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2447.mp4 (确认存在: True)
2025-08-07 17:41:23,632 - INFO - 添加场景ID=2447，时长=1.60秒，累计时长=1.60秒
2025-08-07 17:41:23,632 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2448.mp4 (确认存在: True)
2025-08-07 17:41:23,632 - INFO - 添加场景ID=2448，时长=1.56秒，累计时长=3.16秒
2025-08-07 17:41:23,632 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2449.mp4 (确认存在: True)
2025-08-07 17:41:23,632 - INFO - 添加场景ID=2449，时长=1.80秒，累计时长=4.96秒
2025-08-07 17:41:23,632 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2450.mp4 (确认存在: True)
2025-08-07 17:41:23,633 - INFO - 添加场景ID=2450，时长=1.08秒，累计时长=6.04秒
2025-08-07 17:41:23,633 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2451.mp4 (确认存在: True)
2025-08-07 17:41:23,633 - INFO - 添加场景ID=2451，时长=1.80秒，累计时长=7.84秒
2025-08-07 17:41:23,633 - INFO - 准备合并 5 个场景文件，总时长约 7.84秒
2025-08-07 17:41:23,633 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2447.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2448.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2449.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2450.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2451.mp4'

2025-08-07 17:41:23,633 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpxe_2t_ss\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpxe_2t_ss\temp_combined.mp4
2025-08-07 17:41:23,787 - INFO - 合并后的视频时长: 7.96秒，目标音频时长: 7.26秒
2025-08-07 17:41:23,787 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpxe_2t_ss\temp_combined.mp4 -ss 0 -to 7.264 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\50_1.mp4
2025-08-07 17:41:24,148 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:24,148 - INFO - 目标音频时长: 7.26秒
2025-08-07 17:41:24,148 - INFO - 实际视频时长: 7.30秒
2025-08-07 17:41:24,148 - INFO - 时长差异: 0.04秒 (0.54%)
2025-08-07 17:41:24,148 - INFO - ==========================================
2025-08-07 17:41:24,148 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:24,148 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\50_1.mp4
2025-08-07 17:41:24,149 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxe_2t_ss
2025-08-07 17:41:24,193 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:24,193 - INFO -   - 音频时长: 7.26秒
2025-08-07 17:41:24,193 - INFO -   - 视频时长: 7.30秒
2025-08-07 17:41:24,193 - INFO -   - 时长差异: 0.04秒 (0.54%)
2025-08-07 17:41:24,193 - INFO - 
----- 处理字幕 #50 的方案 #2 -----
2025-08-07 17:41:24,193 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\50_2.mp4
2025-08-07 17:41:24,193 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpk_6qilj2
2025-08-07 17:41:24,194 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2460.mp4 (确认存在: True)
2025-08-07 17:41:24,194 - INFO - 添加场景ID=2460，时长=3.12秒，累计时长=3.12秒
2025-08-07 17:41:24,194 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2461.mp4 (确认存在: True)
2025-08-07 17:41:24,194 - INFO - 添加场景ID=2461，时长=0.84秒，累计时长=3.96秒
2025-08-07 17:41:24,194 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2462.mp4 (确认存在: True)
2025-08-07 17:41:24,194 - INFO - 添加场景ID=2462，时长=1.00秒，累计时长=4.96秒
2025-08-07 17:41:24,194 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2463.mp4 (确认存在: True)
2025-08-07 17:41:24,194 - INFO - 添加场景ID=2463，时长=1.56秒，累计时长=6.52秒
2025-08-07 17:41:24,194 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2464.mp4 (确认存在: True)
2025-08-07 17:41:24,194 - INFO - 添加场景ID=2464，时长=1.20秒，累计时长=7.72秒
2025-08-07 17:41:24,194 - INFO - 准备合并 5 个场景文件，总时长约 7.72秒
2025-08-07 17:41:24,194 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2460.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2461.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2462.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2463.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2464.mp4'

2025-08-07 17:41:24,194 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpk_6qilj2\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpk_6qilj2\temp_combined.mp4
2025-08-07 17:41:24,347 - INFO - 合并后的视频时长: 7.84秒，目标音频时长: 7.26秒
2025-08-07 17:41:24,347 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpk_6qilj2\temp_combined.mp4 -ss 0 -to 7.264 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\50_2.mp4
2025-08-07 17:41:24,704 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:24,704 - INFO - 目标音频时长: 7.26秒
2025-08-07 17:41:24,704 - INFO - 实际视频时长: 7.30秒
2025-08-07 17:41:24,704 - INFO - 时长差异: 0.04秒 (0.54%)
2025-08-07 17:41:24,704 - INFO - ==========================================
2025-08-07 17:41:24,704 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:24,704 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\50_2.mp4
2025-08-07 17:41:24,705 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpk_6qilj2
2025-08-07 17:41:24,748 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:24,748 - INFO -   - 音频时长: 7.26秒
2025-08-07 17:41:24,748 - INFO -   - 视频时长: 7.30秒
2025-08-07 17:41:24,748 - INFO -   - 时长差异: 0.04秒 (0.54%)
2025-08-07 17:41:24,748 - INFO - 
----- 处理字幕 #50 的方案 #3 -----
2025-08-07 17:41:24,748 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\50_3.mp4
2025-08-07 17:41:24,748 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpfc7b0dxm
2025-08-07 17:41:24,748 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2448.mp4 (确认存在: True)
2025-08-07 17:41:24,748 - INFO - 添加场景ID=2448，时长=1.56秒，累计时长=1.56秒
2025-08-07 17:41:24,748 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2460.mp4 (确认存在: True)
2025-08-07 17:41:24,748 - INFO - 添加场景ID=2460，时长=3.12秒，累计时长=4.68秒
2025-08-07 17:41:24,748 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2447.mp4 (确认存在: True)
2025-08-07 17:41:24,748 - INFO - 添加场景ID=2447，时长=1.60秒，累计时长=6.28秒
2025-08-07 17:41:24,748 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2449.mp4 (确认存在: True)
2025-08-07 17:41:24,748 - INFO - 添加场景ID=2449，时长=1.80秒，累计时长=8.08秒
2025-08-07 17:41:24,748 - INFO - 准备合并 4 个场景文件，总时长约 8.08秒
2025-08-07 17:41:24,750 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2448.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2460.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2447.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2449.mp4'

2025-08-07 17:41:24,750 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpfc7b0dxm\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpfc7b0dxm\temp_combined.mp4
2025-08-07 17:41:24,893 - INFO - 合并后的视频时长: 8.17秒，目标音频时长: 7.26秒
2025-08-07 17:41:24,893 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpfc7b0dxm\temp_combined.mp4 -ss 0 -to 7.264 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\50_3.mp4
2025-08-07 17:41:25,242 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:25,242 - INFO - 目标音频时长: 7.26秒
2025-08-07 17:41:25,242 - INFO - 实际视频时长: 7.30秒
2025-08-07 17:41:25,242 - INFO - 时长差异: 0.04秒 (0.54%)
2025-08-07 17:41:25,242 - INFO - ==========================================
2025-08-07 17:41:25,242 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:25,242 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\50_3.mp4
2025-08-07 17:41:25,242 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpfc7b0dxm
2025-08-07 17:41:25,286 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:25,286 - INFO -   - 音频时长: 7.26秒
2025-08-07 17:41:25,286 - INFO -   - 视频时长: 7.30秒
2025-08-07 17:41:25,286 - INFO -   - 时长差异: 0.04秒 (0.54%)
2025-08-07 17:41:25,286 - INFO - 
字幕 #50 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:25,286 - INFO - 生成的视频文件:
2025-08-07 17:41:25,286 - INFO -   1. F:/github/aicut_auto/newcut_ai\50_1.mp4
2025-08-07 17:41:25,286 - INFO -   2. F:/github/aicut_auto/newcut_ai\50_2.mp4
2025-08-07 17:41:25,286 - INFO -   3. F:/github/aicut_auto/newcut_ai\50_3.mp4
2025-08-07 17:41:25,286 - INFO - ========== 字幕 #50 处理结束 ==========

