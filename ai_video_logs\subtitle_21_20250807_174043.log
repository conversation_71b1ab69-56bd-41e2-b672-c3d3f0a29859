2025-08-07 17:40:43,202 - INFO - ========== 字幕 #21 处理开始 ==========
2025-08-07 17:40:43,202 - INFO - 字幕内容: 只见女孩拿出奶瓶补充能量，引来全场哄笑，没人相信她能创造奇迹。
2025-08-07 17:40:43,202 - INFO - 字幕序号: [607, 613]
2025-08-07 17:40:43,202 - INFO - 音频文件详情:
2025-08-07 17:40:43,202 - INFO -   - 路径: output\21.wav
2025-08-07 17:40:43,202 - INFO -   - 时长: 4.01秒
2025-08-07 17:40:43,202 - INFO -   - 验证音频时长: 4.01秒
2025-08-07 17:40:43,203 - INFO - 字幕时间戳信息:
2025-08-07 17:40:43,203 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:43,203 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:43,203 - INFO -   - 根据生成的音频时长(4.01秒)已调整字幕时间戳
2025-08-07 17:40:43,203 - INFO - ========== 新模式：为字幕 #21 生成4套场景方案 ==========
2025-08-07 17:40:43,203 - INFO - 字幕序号列表: [607, 613]
2025-08-07 17:40:43,203 - INFO - 
--- 生成方案 #1：基于字幕序号 #607 ---
2025-08-07 17:40:43,203 - INFO - 开始为单个字幕序号 #607 匹配场景，目标时长: 4.01秒
2025-08-07 17:40:43,203 - INFO - 开始查找字幕序号 [607] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:43,203 - INFO - 找到related_overlap场景: scene_id=730, 字幕#607
2025-08-07 17:40:43,204 - INFO - 找到related_between场景: scene_id=727, 字幕#607
2025-08-07 17:40:43,204 - INFO - 找到related_between场景: scene_id=728, 字幕#607
2025-08-07 17:40:43,204 - INFO - 找到related_between场景: scene_id=729, 字幕#607
2025-08-07 17:40:43,204 - INFO - 字幕 #607 找到 1 个overlap场景, 3 个between场景
2025-08-07 17:40:43,204 - INFO - 字幕序号 #607 找到 1 个可用overlap场景, 3 个可用between场景
2025-08-07 17:40:43,204 - INFO - 选择第一个overlap场景作为起点: scene_id=730
2025-08-07 17:40:43,204 - INFO - 添加起点场景: scene_id=730, 时长=3.40秒, 累计时长=3.40秒
2025-08-07 17:40:43,204 - INFO - 起点场景时长不足，需要延伸填充 0.61秒
2025-08-07 17:40:43,205 - INFO - 起点场景在原始列表中的索引: 729
2025-08-07 17:40:43,205 - INFO - 延伸添加场景: scene_id=731 (裁剪至 0.61秒)
2025-08-07 17:40:43,205 - INFO - 累计时长: 4.01秒
2025-08-07 17:40:43,205 - INFO - 字幕序号 #607 场景匹配完成，共选择 2 个场景，总时长: 4.01秒
2025-08-07 17:40:43,205 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-08-07 17:40:43,205 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-08-07 17:40:43,205 - INFO - 
--- 生成方案 #2：基于字幕序号 #613 ---
2025-08-07 17:40:43,205 - INFO - 开始为单个字幕序号 #613 匹配场景，目标时长: 4.01秒
2025-08-07 17:40:43,205 - INFO - 开始查找字幕序号 [613] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:43,205 - INFO - 找到related_overlap场景: scene_id=735, 字幕#613
2025-08-07 17:40:43,205 - INFO - 字幕 #613 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:43,205 - INFO - 字幕序号 #613 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:43,205 - INFO - 选择第一个overlap场景作为起点: scene_id=735
2025-08-07 17:40:43,205 - INFO - 添加起点场景: scene_id=735, 时长=2.00秒, 累计时长=2.00秒
2025-08-07 17:40:43,205 - INFO - 起点场景时长不足，需要延伸填充 2.01秒
2025-08-07 17:40:43,205 - INFO - 起点场景在原始列表中的索引: 734
2025-08-07 17:40:43,205 - INFO - 延伸添加场景: scene_id=736 (裁剪至 2.01秒)
2025-08-07 17:40:43,205 - INFO - 累计时长: 4.01秒
2025-08-07 17:40:43,205 - INFO - 字幕序号 #613 场景匹配完成，共选择 2 个场景，总时长: 4.01秒
2025-08-07 17:40:43,205 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-08-07 17:40:43,205 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:43,205 - INFO - ========== 当前模式：为字幕 #21 生成 1 套场景方案 ==========
2025-08-07 17:40:43,205 - INFO - 开始查找字幕序号 [607, 613] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:43,206 - INFO - 找到related_overlap场景: scene_id=730, 字幕#607
2025-08-07 17:40:43,206 - INFO - 找到related_overlap场景: scene_id=735, 字幕#613
2025-08-07 17:40:43,206 - INFO - 找到related_between场景: scene_id=727, 字幕#607
2025-08-07 17:40:43,206 - INFO - 找到related_between场景: scene_id=728, 字幕#607
2025-08-07 17:40:43,206 - INFO - 找到related_between场景: scene_id=729, 字幕#607
2025-08-07 17:40:43,207 - INFO - 字幕 #607 找到 1 个overlap场景, 3 个between场景
2025-08-07 17:40:43,207 - INFO - 字幕 #613 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:43,207 - INFO - 共收集 2 个未使用的overlap场景和 3 个未使用的between场景
2025-08-07 17:40:43,207 - INFO - 开始生成方案 #1
2025-08-07 17:40:43,207 - INFO - 方案 #1: 为字幕#607选择初始化overlap场景id=730
2025-08-07 17:40:43,207 - INFO - 方案 #1: 为字幕#613选择初始化overlap场景id=735
2025-08-07 17:40:43,207 - INFO - 方案 #1: 初始选择后，当前总时长=5.40秒
2025-08-07 17:40:43,207 - INFO - 方案 #1: 额外between选择后，当前总时长=5.40秒
2025-08-07 17:40:43,207 - INFO - 方案 #1: 场景总时长(5.40秒)大于音频时长(4.01秒)，需要裁剪
2025-08-07 17:40:43,207 - INFO - 调整前总时长: 5.40秒, 目标时长: 4.01秒
2025-08-07 17:40:43,207 - INFO - 需要裁剪 1.39秒
2025-08-07 17:40:43,207 - INFO - 裁剪最长场景ID=730：从3.40秒裁剪至2.01秒
2025-08-07 17:40:43,207 - INFO - 调整后总时长: 4.01秒，与目标时长差异: 0.00秒
2025-08-07 17:40:43,207 - INFO - 方案 #1 调整/填充后最终总时长: 4.01秒
2025-08-07 17:40:43,207 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:43,207 - INFO - ========== 当前模式：字幕 #21 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:43,207 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:43,207 - INFO - ========== 新模式：字幕 #21 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:43,207 - INFO - 
----- 处理字幕 #21 的方案 #1 -----
2025-08-07 17:40:43,207 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\21_1.mp4
2025-08-07 17:40:43,207 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_uvvx_em
2025-08-07 17:40:43,208 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\730.mp4 (确认存在: True)
2025-08-07 17:40:43,208 - INFO - 添加场景ID=730，时长=3.40秒，累计时长=3.40秒
2025-08-07 17:40:43,208 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\731.mp4 (确认存在: True)
2025-08-07 17:40:43,208 - INFO - 添加场景ID=731，时长=1.84秒，累计时长=5.24秒
2025-08-07 17:40:43,208 - INFO - 准备合并 2 个场景文件，总时长约 5.24秒
2025-08-07 17:40:43,208 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/730.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/731.mp4'

2025-08-07 17:40:43,208 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp_uvvx_em\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp_uvvx_em\temp_combined.mp4
2025-08-07 17:40:43,337 - INFO - 合并后的视频时长: 5.29秒，目标音频时长: 4.01秒
2025-08-07 17:40:43,337 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp_uvvx_em\temp_combined.mp4 -ss 0 -to 4.006 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\21_1.mp4
2025-08-07 17:40:43,610 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:43,610 - INFO - 目标音频时长: 4.01秒
2025-08-07 17:40:43,610 - INFO - 实际视频时长: 4.06秒
2025-08-07 17:40:43,610 - INFO - 时长差异: 0.06秒 (1.42%)
2025-08-07 17:40:43,610 - INFO - ==========================================
2025-08-07 17:40:43,610 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:43,610 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\21_1.mp4
2025-08-07 17:40:43,611 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_uvvx_em
2025-08-07 17:40:43,662 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:43,662 - INFO -   - 音频时长: 4.01秒
2025-08-07 17:40:43,662 - INFO -   - 视频时长: 4.06秒
2025-08-07 17:40:43,662 - INFO -   - 时长差异: 0.06秒 (1.42%)
2025-08-07 17:40:43,662 - INFO - 
----- 处理字幕 #21 的方案 #2 -----
2025-08-07 17:40:43,662 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\21_2.mp4
2025-08-07 17:40:43,662 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpy785n52e
2025-08-07 17:40:43,663 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\735.mp4 (确认存在: True)
2025-08-07 17:40:43,663 - INFO - 添加场景ID=735，时长=2.00秒，累计时长=2.00秒
2025-08-07 17:40:43,663 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\736.mp4 (确认存在: True)
2025-08-07 17:40:43,663 - INFO - 添加场景ID=736，时长=2.40秒，累计时长=4.40秒
2025-08-07 17:40:43,663 - INFO - 准备合并 2 个场景文件，总时长约 4.40秒
2025-08-07 17:40:43,663 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/735.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/736.mp4'

2025-08-07 17:40:43,663 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpy785n52e\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpy785n52e\temp_combined.mp4
2025-08-07 17:40:43,787 - INFO - 合并后的视频时长: 4.45秒，目标音频时长: 4.01秒
2025-08-07 17:40:43,788 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpy785n52e\temp_combined.mp4 -ss 0 -to 4.006 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\21_2.mp4
2025-08-07 17:40:44,071 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:44,071 - INFO - 目标音频时长: 4.01秒
2025-08-07 17:40:44,071 - INFO - 实际视频时长: 4.06秒
2025-08-07 17:40:44,071 - INFO - 时长差异: 0.06秒 (1.42%)
2025-08-07 17:40:44,071 - INFO - ==========================================
2025-08-07 17:40:44,071 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:44,072 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\21_2.mp4
2025-08-07 17:40:44,072 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpy785n52e
2025-08-07 17:40:44,115 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:44,115 - INFO -   - 音频时长: 4.01秒
2025-08-07 17:40:44,115 - INFO -   - 视频时长: 4.06秒
2025-08-07 17:40:44,116 - INFO -   - 时长差异: 0.06秒 (1.42%)
2025-08-07 17:40:44,116 - INFO - 
----- 处理字幕 #21 的方案 #3 -----
2025-08-07 17:40:44,116 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\21_3.mp4
2025-08-07 17:40:44,116 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpq1hcevtl
2025-08-07 17:40:44,117 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\730.mp4 (确认存在: True)
2025-08-07 17:40:44,117 - INFO - 添加场景ID=730，时长=3.40秒，累计时长=3.40秒
2025-08-07 17:40:44,117 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\735.mp4 (确认存在: True)
2025-08-07 17:40:44,117 - INFO - 添加场景ID=735，时长=2.00秒，累计时长=5.40秒
2025-08-07 17:40:44,117 - INFO - 准备合并 2 个场景文件，总时长约 5.40秒
2025-08-07 17:40:44,117 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/730.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/735.mp4'

2025-08-07 17:40:44,117 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpq1hcevtl\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpq1hcevtl\temp_combined.mp4
2025-08-07 17:40:44,242 - INFO - 合并后的视频时长: 5.45秒，目标音频时长: 4.01秒
2025-08-07 17:40:44,242 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpq1hcevtl\temp_combined.mp4 -ss 0 -to 4.006 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\21_3.mp4
2025-08-07 17:40:44,519 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:44,519 - INFO - 目标音频时长: 4.01秒
2025-08-07 17:40:44,519 - INFO - 实际视频时长: 4.06秒
2025-08-07 17:40:44,519 - INFO - 时长差异: 0.06秒 (1.42%)
2025-08-07 17:40:44,519 - INFO - ==========================================
2025-08-07 17:40:44,519 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:44,519 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\21_3.mp4
2025-08-07 17:40:44,520 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpq1hcevtl
2025-08-07 17:40:44,563 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:44,563 - INFO -   - 音频时长: 4.01秒
2025-08-07 17:40:44,563 - INFO -   - 视频时长: 4.06秒
2025-08-07 17:40:44,563 - INFO -   - 时长差异: 0.06秒 (1.42%)
2025-08-07 17:40:44,563 - INFO - 
字幕 #21 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:44,563 - INFO - 生成的视频文件:
2025-08-07 17:40:44,563 - INFO -   1. F:/github/aicut_auto/newcut_ai\21_1.mp4
2025-08-07 17:40:44,563 - INFO -   2. F:/github/aicut_auto/newcut_ai\21_2.mp4
2025-08-07 17:40:44,563 - INFO -   3. F:/github/aicut_auto/newcut_ai\21_3.mp4
2025-08-07 17:40:44,563 - INFO - ========== 字幕 #21 处理结束 ==========

