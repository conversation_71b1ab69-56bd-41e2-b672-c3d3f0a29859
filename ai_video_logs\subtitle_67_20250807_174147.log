2025-08-07 17:41:47,410 - INFO - ========== 字幕 #67 处理开始 ==========
2025-08-07 17:41:47,410 - INFO - 字幕内容: 女孩现出真身，强大的气场让对方震惊不已，他终于明白，眼前的女孩，真的是那位传说中的老祖宗。
2025-08-07 17:41:47,410 - INFO - 字幕序号: [3053, 3055]
2025-08-07 17:41:47,410 - INFO - 音频文件详情:
2025-08-07 17:41:47,410 - INFO -   - 路径: output\67.wav
2025-08-07 17:41:47,410 - INFO -   - 时长: 4.92秒
2025-08-07 17:41:47,410 - INFO -   - 验证音频时长: 4.92秒
2025-08-07 17:41:47,410 - INFO - 字幕时间戳信息:
2025-08-07 17:41:47,410 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:47,410 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:47,410 - INFO -   - 根据生成的音频时长(4.92秒)已调整字幕时间戳
2025-08-07 17:41:47,410 - INFO - ========== 新模式：为字幕 #67 生成4套场景方案 ==========
2025-08-07 17:41:47,411 - INFO - 字幕序号列表: [3053, 3055]
2025-08-07 17:41:47,411 - INFO - 
--- 生成方案 #1：基于字幕序号 #3053 ---
2025-08-07 17:41:47,411 - INFO - 开始为单个字幕序号 #3053 匹配场景，目标时长: 4.92秒
2025-08-07 17:41:47,411 - INFO - 开始查找字幕序号 [3053] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:47,411 - INFO - 找到related_overlap场景: scene_id=3369, 字幕#3053
2025-08-07 17:41:47,412 - INFO - 找到related_between场景: scene_id=3367, 字幕#3053
2025-08-07 17:41:47,412 - INFO - 找到related_between场景: scene_id=3368, 字幕#3053
2025-08-07 17:41:47,412 - INFO - 字幕 #3053 找到 1 个overlap场景, 2 个between场景
2025-08-07 17:41:47,412 - INFO - 字幕序号 #3053 找到 1 个可用overlap场景, 2 个可用between场景
2025-08-07 17:41:47,412 - INFO - 选择第一个overlap场景作为起点: scene_id=3369
2025-08-07 17:41:47,412 - INFO - 添加起点场景: scene_id=3369, 时长=1.64秒, 累计时长=1.64秒
2025-08-07 17:41:47,412 - INFO - 起点场景时长不足，需要延伸填充 3.28秒
2025-08-07 17:41:47,412 - INFO - 起点场景在原始列表中的索引: 3368
2025-08-07 17:41:47,412 - INFO - 延伸添加场景: scene_id=3370 (完整时长 1.84秒)
2025-08-07 17:41:47,412 - INFO - 累计时长: 3.48秒
2025-08-07 17:41:47,412 - INFO - 延伸添加场景: scene_id=3371 (完整时长 0.96秒)
2025-08-07 17:41:47,412 - INFO - 累计时长: 4.44秒
2025-08-07 17:41:47,412 - INFO - 延伸添加场景: scene_id=3372 (裁剪至 0.48秒)
2025-08-07 17:41:47,412 - INFO - 累计时长: 4.92秒
2025-08-07 17:41:47,412 - INFO - 字幕序号 #3053 场景匹配完成，共选择 4 个场景，总时长: 4.92秒
2025-08-07 17:41:47,412 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-08-07 17:41:47,412 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-08-07 17:41:47,412 - INFO - 
--- 生成方案 #2：基于字幕序号 #3055 ---
2025-08-07 17:41:47,412 - INFO - 开始为单个字幕序号 #3055 匹配场景，目标时长: 4.92秒
2025-08-07 17:41:47,412 - INFO - 开始查找字幕序号 [3055] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:47,413 - INFO - 找到related_overlap场景: scene_id=3373, 字幕#3055
2025-08-07 17:41:47,413 - INFO - 找到related_between场景: scene_id=3371, 字幕#3055
2025-08-07 17:41:47,413 - INFO - 找到related_between场景: scene_id=3372, 字幕#3055
2025-08-07 17:41:47,413 - INFO - 字幕 #3055 找到 1 个overlap场景, 2 个between场景
2025-08-07 17:41:47,413 - INFO - 字幕序号 #3055 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:47,413 - INFO - 选择第一个overlap场景作为起点: scene_id=3373
2025-08-07 17:41:47,413 - INFO - 添加起点场景: scene_id=3373, 时长=3.60秒, 累计时长=3.60秒
2025-08-07 17:41:47,413 - INFO - 起点场景时长不足，需要延伸填充 1.32秒
2025-08-07 17:41:47,414 - INFO - 起点场景在原始列表中的索引: 3372
2025-08-07 17:41:47,414 - INFO - 延伸添加场景: scene_id=3374 (裁剪至 1.32秒)
2025-08-07 17:41:47,414 - INFO - 累计时长: 4.92秒
2025-08-07 17:41:47,414 - INFO - 字幕序号 #3055 场景匹配完成，共选择 2 个场景，总时长: 4.92秒
2025-08-07 17:41:47,414 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-08-07 17:41:47,414 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:47,414 - INFO - ========== 当前模式：为字幕 #67 生成 1 套场景方案 ==========
2025-08-07 17:41:47,414 - INFO - 开始查找字幕序号 [3053, 3055] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:47,414 - INFO - 找到related_overlap场景: scene_id=3369, 字幕#3053
2025-08-07 17:41:47,414 - INFO - 找到related_overlap场景: scene_id=3373, 字幕#3055
2025-08-07 17:41:47,415 - INFO - 找到related_between场景: scene_id=3367, 字幕#3053
2025-08-07 17:41:47,415 - INFO - 找到related_between场景: scene_id=3368, 字幕#3053
2025-08-07 17:41:47,415 - INFO - 找到related_between场景: scene_id=3371, 字幕#3055
2025-08-07 17:41:47,415 - INFO - 找到related_between场景: scene_id=3372, 字幕#3055
2025-08-07 17:41:47,415 - INFO - 字幕 #3053 找到 1 个overlap场景, 2 个between场景
2025-08-07 17:41:47,415 - INFO - 字幕 #3055 找到 1 个overlap场景, 2 个between场景
2025-08-07 17:41:47,415 - INFO - 共收集 2 个未使用的overlap场景和 4 个未使用的between场景
2025-08-07 17:41:47,415 - INFO - 开始生成方案 #1
2025-08-07 17:41:47,415 - INFO - 方案 #1: 为字幕#3053选择初始化overlap场景id=3369
2025-08-07 17:41:47,415 - INFO - 方案 #1: 为字幕#3055选择初始化overlap场景id=3373
2025-08-07 17:41:47,415 - INFO - 方案 #1: 初始选择后，当前总时长=5.24秒
2025-08-07 17:41:47,415 - INFO - 方案 #1: 额外between选择后，当前总时长=5.24秒
2025-08-07 17:41:47,415 - INFO - 方案 #1: 场景总时长(5.24秒)大于音频时长(4.92秒)，需要裁剪
2025-08-07 17:41:47,415 - INFO - 调整前总时长: 5.24秒, 目标时长: 4.92秒
2025-08-07 17:41:47,415 - INFO - 需要裁剪 0.32秒
2025-08-07 17:41:47,415 - INFO - 裁剪最长场景ID=3373：从3.60秒裁剪至3.28秒
2025-08-07 17:41:47,415 - INFO - 调整后总时长: 4.92秒，与目标时长差异: 0.00秒
2025-08-07 17:41:47,415 - INFO - 方案 #1 调整/填充后最终总时长: 4.92秒
2025-08-07 17:41:47,415 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:47,415 - INFO - ========== 当前模式：字幕 #67 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:47,415 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:47,415 - INFO - ========== 新模式：字幕 #67 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:47,415 - INFO - 
----- 处理字幕 #67 的方案 #1 -----
2025-08-07 17:41:47,415 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\67_1.mp4
2025-08-07 17:41:47,415 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpvy7q79ag
2025-08-07 17:41:47,416 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3369.mp4 (确认存在: True)
2025-08-07 17:41:47,416 - INFO - 添加场景ID=3369，时长=1.64秒，累计时长=1.64秒
2025-08-07 17:41:47,416 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3370.mp4 (确认存在: True)
2025-08-07 17:41:47,416 - INFO - 添加场景ID=3370，时长=1.84秒，累计时长=3.48秒
2025-08-07 17:41:47,416 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3371.mp4 (确认存在: True)
2025-08-07 17:41:47,416 - INFO - 添加场景ID=3371，时长=0.96秒，累计时长=4.44秒
2025-08-07 17:41:47,416 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3372.mp4 (确认存在: True)
2025-08-07 17:41:47,416 - INFO - 添加场景ID=3372，时长=0.76秒，累计时长=5.20秒
2025-08-07 17:41:47,416 - INFO - 准备合并 4 个场景文件，总时长约 5.20秒
2025-08-07 17:41:47,416 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3369.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3370.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3371.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3372.mp4'

2025-08-07 17:41:47,416 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpvy7q79ag\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpvy7q79ag\temp_combined.mp4
2025-08-07 17:41:47,583 - INFO - 合并后的视频时长: 5.29秒，目标音频时长: 4.92秒
2025-08-07 17:41:47,583 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpvy7q79ag\temp_combined.mp4 -ss 0 -to 4.92 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\67_1.mp4
2025-08-07 17:41:47,911 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:47,911 - INFO - 目标音频时长: 4.92秒
2025-08-07 17:41:47,911 - INFO - 实际视频时长: 4.94秒
2025-08-07 17:41:47,911 - INFO - 时长差异: 0.02秒 (0.47%)
2025-08-07 17:41:47,911 - INFO - ==========================================
2025-08-07 17:41:47,911 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:47,911 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\67_1.mp4
2025-08-07 17:41:47,911 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpvy7q79ag
2025-08-07 17:41:47,957 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:47,957 - INFO -   - 音频时长: 4.92秒
2025-08-07 17:41:47,957 - INFO -   - 视频时长: 4.94秒
2025-08-07 17:41:47,957 - INFO -   - 时长差异: 0.02秒 (0.47%)
2025-08-07 17:41:47,957 - INFO - 
----- 处理字幕 #67 的方案 #2 -----
2025-08-07 17:41:47,957 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\67_2.mp4
2025-08-07 17:41:47,957 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmponsd0u97
2025-08-07 17:41:47,958 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3373.mp4 (确认存在: True)
2025-08-07 17:41:47,958 - INFO - 添加场景ID=3373，时长=3.60秒，累计时长=3.60秒
2025-08-07 17:41:47,958 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3374.mp4 (确认存在: True)
2025-08-07 17:41:47,958 - INFO - 添加场景ID=3374，时长=1.68秒，累计时长=5.28秒
2025-08-07 17:41:47,958 - INFO - 准备合并 2 个场景文件，总时长约 5.28秒
2025-08-07 17:41:47,958 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3373.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3374.mp4'

2025-08-07 17:41:47,958 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmponsd0u97\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmponsd0u97\temp_combined.mp4
2025-08-07 17:41:48,085 - INFO - 合并后的视频时长: 5.33秒，目标音频时长: 4.92秒
2025-08-07 17:41:48,085 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmponsd0u97\temp_combined.mp4 -ss 0 -to 4.92 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\67_2.mp4
2025-08-07 17:41:48,406 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:48,406 - INFO - 目标音频时长: 4.92秒
2025-08-07 17:41:48,406 - INFO - 实际视频时长: 4.94秒
2025-08-07 17:41:48,406 - INFO - 时长差异: 0.02秒 (0.47%)
2025-08-07 17:41:48,406 - INFO - ==========================================
2025-08-07 17:41:48,406 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:48,406 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\67_2.mp4
2025-08-07 17:41:48,407 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmponsd0u97
2025-08-07 17:41:48,451 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:48,451 - INFO -   - 音频时长: 4.92秒
2025-08-07 17:41:48,451 - INFO -   - 视频时长: 4.94秒
2025-08-07 17:41:48,451 - INFO -   - 时长差异: 0.02秒 (0.47%)
2025-08-07 17:41:48,451 - INFO - 
----- 处理字幕 #67 的方案 #3 -----
2025-08-07 17:41:48,451 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\67_3.mp4
2025-08-07 17:41:48,451 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpssxuiuli
2025-08-07 17:41:48,452 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3369.mp4 (确认存在: True)
2025-08-07 17:41:48,452 - INFO - 添加场景ID=3369，时长=1.64秒，累计时长=1.64秒
2025-08-07 17:41:48,452 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3373.mp4 (确认存在: True)
2025-08-07 17:41:48,452 - INFO - 添加场景ID=3373，时长=3.60秒，累计时长=5.24秒
2025-08-07 17:41:48,452 - INFO - 准备合并 2 个场景文件，总时长约 5.24秒
2025-08-07 17:41:48,452 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3369.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3373.mp4'

2025-08-07 17:41:48,452 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpssxuiuli\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpssxuiuli\temp_combined.mp4
2025-08-07 17:41:48,572 - INFO - 合并后的视频时长: 5.29秒，目标音频时长: 4.92秒
2025-08-07 17:41:48,572 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpssxuiuli\temp_combined.mp4 -ss 0 -to 4.92 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\67_3.mp4
2025-08-07 17:41:48,873 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:48,873 - INFO - 目标音频时长: 4.92秒
2025-08-07 17:41:48,873 - INFO - 实际视频时长: 4.94秒
2025-08-07 17:41:48,873 - INFO - 时长差异: 0.02秒 (0.47%)
2025-08-07 17:41:48,873 - INFO - ==========================================
2025-08-07 17:41:48,873 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:48,873 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\67_3.mp4
2025-08-07 17:41:48,875 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpssxuiuli
2025-08-07 17:41:48,919 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:48,919 - INFO -   - 音频时长: 4.92秒
2025-08-07 17:41:48,919 - INFO -   - 视频时长: 4.94秒
2025-08-07 17:41:48,919 - INFO -   - 时长差异: 0.02秒 (0.47%)
2025-08-07 17:41:48,919 - INFO - 
字幕 #67 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:48,919 - INFO - 生成的视频文件:
2025-08-07 17:41:48,919 - INFO -   1. F:/github/aicut_auto/newcut_ai\67_1.mp4
2025-08-07 17:41:48,919 - INFO -   2. F:/github/aicut_auto/newcut_ai\67_2.mp4
2025-08-07 17:41:48,919 - INFO -   3. F:/github/aicut_auto/newcut_ai\67_3.mp4
2025-08-07 17:41:48,919 - INFO - ========== 字幕 #67 处理结束 ==========

