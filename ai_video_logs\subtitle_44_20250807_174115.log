2025-08-07 17:41:15,916 - INFO - ========== 字幕 #44 处理开始 ==========
2025-08-07 17:41:15,916 - INFO - 字幕内容: 走投无路的心机女终于崩溃，供出了幕后主使，正是男人的二弟。
2025-08-07 17:41:15,916 - INFO - 字幕序号: [1816, 1817]
2025-08-07 17:41:15,916 - INFO - 音频文件详情:
2025-08-07 17:41:15,916 - INFO -   - 路径: output\44.wav
2025-08-07 17:41:15,916 - INFO -   - 时长: 3.85秒
2025-08-07 17:41:15,917 - INFO -   - 验证音频时长: 3.85秒
2025-08-07 17:41:15,917 - INFO - 字幕时间戳信息:
2025-08-07 17:41:15,917 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:15,927 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:15,927 - INFO -   - 根据生成的音频时长(3.85秒)已调整字幕时间戳
2025-08-07 17:41:15,927 - INFO - ========== 新模式：为字幕 #44 生成4套场景方案 ==========
2025-08-07 17:41:15,927 - INFO - 字幕序号列表: [1816, 1817]
2025-08-07 17:41:15,927 - INFO - 
--- 生成方案 #1：基于字幕序号 #1816 ---
2025-08-07 17:41:15,927 - INFO - 开始为单个字幕序号 #1816 匹配场景，目标时长: 3.85秒
2025-08-07 17:41:15,927 - INFO - 开始查找字幕序号 [1816] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:15,927 - INFO - 找到related_overlap场景: scene_id=2023, 字幕#1816
2025-08-07 17:41:15,927 - INFO - 找到related_overlap场景: scene_id=2024, 字幕#1816
2025-08-07 17:41:15,928 - INFO - 找到related_between场景: scene_id=2022, 字幕#1816
2025-08-07 17:41:15,928 - INFO - 字幕 #1816 找到 2 个overlap场景, 1 个between场景
2025-08-07 17:41:15,928 - INFO - 字幕序号 #1816 找到 2 个可用overlap场景, 1 个可用between场景
2025-08-07 17:41:15,928 - INFO - 选择第一个overlap场景作为起点: scene_id=2023
2025-08-07 17:41:15,928 - INFO - 添加起点场景: scene_id=2023, 时长=1.92秒, 累计时长=1.92秒
2025-08-07 17:41:15,928 - INFO - 起点场景时长不足，需要延伸填充 1.93秒
2025-08-07 17:41:15,928 - INFO - 起点场景在原始列表中的索引: 2022
2025-08-07 17:41:15,928 - INFO - 延伸添加场景: scene_id=2024 (裁剪至 1.93秒)
2025-08-07 17:41:15,928 - INFO - 累计时长: 3.85秒
2025-08-07 17:41:15,928 - INFO - 字幕序号 #1816 场景匹配完成，共选择 2 个场景，总时长: 3.85秒
2025-08-07 17:41:15,928 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-08-07 17:41:15,928 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-08-07 17:41:15,928 - INFO - 
--- 生成方案 #2：基于字幕序号 #1817 ---
2025-08-07 17:41:15,929 - INFO - 开始为单个字幕序号 #1817 匹配场景，目标时长: 3.85秒
2025-08-07 17:41:15,929 - INFO - 开始查找字幕序号 [1817] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:15,929 - INFO - 找到related_overlap场景: scene_id=2024, 字幕#1817
2025-08-07 17:41:15,930 - INFO - 字幕 #1817 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:15,930 - INFO - 字幕序号 #1817 找到 0 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:15,930 - ERROR - 字幕序号 #1817 没有找到任何可用的匹配场景
2025-08-07 17:41:15,930 - WARNING - 方案 #2 生成失败，未找到合适的场景
2025-08-07 17:41:15,930 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-08-07 17:41:15,930 - INFO - ========== 当前模式：为字幕 #44 生成 1 套场景方案 ==========
2025-08-07 17:41:15,930 - INFO - 开始查找字幕序号 [1816, 1817] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:15,930 - INFO - 找到related_overlap场景: scene_id=2023, 字幕#1816
2025-08-07 17:41:15,930 - INFO - 找到related_overlap场景: scene_id=2024, 字幕#1816
2025-08-07 17:41:15,931 - INFO - 找到related_between场景: scene_id=2022, 字幕#1816
2025-08-07 17:41:15,931 - INFO - 字幕 #1816 找到 2 个overlap场景, 1 个between场景
2025-08-07 17:41:15,931 - INFO - 字幕 #1817 找到 0 个overlap场景, 0 个between场景
2025-08-07 17:41:15,931 - WARNING - 字幕 #1817 没有找到任何匹配场景!
2025-08-07 17:41:15,931 - INFO - 共收集 2 个未使用的overlap场景和 1 个未使用的between场景
2025-08-07 17:41:15,931 - INFO - 开始生成方案 #1
2025-08-07 17:41:15,931 - INFO - 方案 #1: 为字幕#1816选择初始化overlap场景id=2024
2025-08-07 17:41:15,931 - INFO - 方案 #1: 初始选择后，当前总时长=2.64秒
2025-08-07 17:41:15,931 - INFO - 方案 #1: 额外添加overlap场景id=2023, 当前总时长=4.56秒
2025-08-07 17:41:15,931 - INFO - 方案 #1: 额外between选择后，当前总时长=4.56秒
2025-08-07 17:41:15,931 - INFO - 方案 #1: 场景总时长(4.56秒)大于音频时长(3.85秒)，需要裁剪
2025-08-07 17:41:15,931 - INFO - 调整前总时长: 4.56秒, 目标时长: 3.85秒
2025-08-07 17:41:15,931 - INFO - 需要裁剪 0.71秒
2025-08-07 17:41:15,931 - INFO - 裁剪最长场景ID=2024：从2.64秒裁剪至1.93秒
2025-08-07 17:41:15,931 - INFO - 调整后总时长: 3.85秒，与目标时长差异: 0.00秒
2025-08-07 17:41:15,931 - INFO - 方案 #1 调整/填充后最终总时长: 3.85秒
2025-08-07 17:41:15,931 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:15,931 - INFO - ========== 当前模式：字幕 #44 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:15,931 - INFO - 方案 #2 (传统模式) 生成成功
2025-08-07 17:41:15,931 - INFO - ========== 新模式：字幕 #44 共生成 2 套有效场景方案 ==========
2025-08-07 17:41:15,931 - INFO - 
----- 处理字幕 #44 的方案 #1 -----
2025-08-07 17:41:15,931 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\44_1.mp4
2025-08-07 17:41:15,932 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpl6rzupud
2025-08-07 17:41:15,932 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2023.mp4 (确认存在: True)
2025-08-07 17:41:15,932 - INFO - 添加场景ID=2023，时长=1.92秒，累计时长=1.92秒
2025-08-07 17:41:15,932 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2024.mp4 (确认存在: True)
2025-08-07 17:41:15,932 - INFO - 添加场景ID=2024，时长=2.64秒，累计时长=4.56秒
2025-08-07 17:41:15,932 - INFO - 准备合并 2 个场景文件，总时长约 4.56秒
2025-08-07 17:41:15,932 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2023.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2024.mp4'

2025-08-07 17:41:15,932 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpl6rzupud\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpl6rzupud\temp_combined.mp4
2025-08-07 17:41:16,051 - INFO - 合并后的视频时长: 4.61秒，目标音频时长: 3.85秒
2025-08-07 17:41:16,051 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpl6rzupud\temp_combined.mp4 -ss 0 -to 3.853 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\44_1.mp4
2025-08-07 17:41:16,321 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:16,321 - INFO - 目标音频时长: 3.85秒
2025-08-07 17:41:16,321 - INFO - 实际视频时长: 3.90秒
2025-08-07 17:41:16,321 - INFO - 时长差异: 0.05秒 (1.30%)
2025-08-07 17:41:16,321 - INFO - ==========================================
2025-08-07 17:41:16,321 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:16,321 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\44_1.mp4
2025-08-07 17:41:16,322 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpl6rzupud
2025-08-07 17:41:16,364 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:16,364 - INFO -   - 音频时长: 3.85秒
2025-08-07 17:41:16,364 - INFO -   - 视频时长: 3.90秒
2025-08-07 17:41:16,364 - INFO -   - 时长差异: 0.05秒 (1.30%)
2025-08-07 17:41:16,364 - INFO - 
----- 处理字幕 #44 的方案 #2 -----
2025-08-07 17:41:16,364 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\44_2.mp4
2025-08-07 17:41:16,364 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpg86av4nf
2025-08-07 17:41:16,365 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2024.mp4 (确认存在: True)
2025-08-07 17:41:16,365 - INFO - 添加场景ID=2024，时长=2.64秒，累计时长=2.64秒
2025-08-07 17:41:16,365 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2023.mp4 (确认存在: True)
2025-08-07 17:41:16,365 - INFO - 添加场景ID=2023，时长=1.92秒，累计时长=4.56秒
2025-08-07 17:41:16,365 - INFO - 准备合并 2 个场景文件，总时长约 4.56秒
2025-08-07 17:41:16,365 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2024.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2023.mp4'

2025-08-07 17:41:16,365 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpg86av4nf\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpg86av4nf\temp_combined.mp4
2025-08-07 17:41:16,493 - INFO - 合并后的视频时长: 4.61秒，目标音频时长: 3.85秒
2025-08-07 17:41:16,493 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpg86av4nf\temp_combined.mp4 -ss 0 -to 3.853 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\44_2.mp4
2025-08-07 17:41:16,768 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:16,768 - INFO - 目标音频时长: 3.85秒
2025-08-07 17:41:16,768 - INFO - 实际视频时长: 3.90秒
2025-08-07 17:41:16,768 - INFO - 时长差异: 0.05秒 (1.30%)
2025-08-07 17:41:16,768 - INFO - ==========================================
2025-08-07 17:41:16,768 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:16,768 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\44_2.mp4
2025-08-07 17:41:16,769 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpg86av4nf
2025-08-07 17:41:16,811 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:16,811 - INFO -   - 音频时长: 3.85秒
2025-08-07 17:41:16,811 - INFO -   - 视频时长: 3.90秒
2025-08-07 17:41:16,811 - INFO -   - 时长差异: 0.05秒 (1.30%)
2025-08-07 17:41:16,811 - INFO - 
字幕 #44 处理完成，成功生成 2/2 套方案
2025-08-07 17:41:16,811 - INFO - 生成的视频文件:
2025-08-07 17:41:16,811 - INFO -   1. F:/github/aicut_auto/newcut_ai\44_1.mp4
2025-08-07 17:41:16,811 - INFO -   2. F:/github/aicut_auto/newcut_ai\44_2.mp4
2025-08-07 17:41:16,811 - INFO - ========== 字幕 #44 处理结束 ==========

