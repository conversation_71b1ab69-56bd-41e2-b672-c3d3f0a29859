2025-08-07 17:41:21,178 - INFO - ========== 字幕 #48 处理开始 ==========
2025-08-07 17:41:21,178 - INFO - 字幕内容: 为了增加可信度，他甚至谎称这神药是传说中的“清音医仙”亲手传授。
2025-08-07 17:41:21,178 - INFO - 字幕序号: [2111, 2113]
2025-08-07 17:41:21,179 - INFO - 音频文件详情:
2025-08-07 17:41:21,179 - INFO -   - 路径: output\48.wav
2025-08-07 17:41:21,179 - INFO -   - 时长: 4.96秒
2025-08-07 17:41:21,179 - INFO -   - 验证音频时长: 4.96秒
2025-08-07 17:41:21,179 - INFO - 字幕时间戳信息:
2025-08-07 17:41:21,189 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:21,189 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:21,189 - INFO -   - 根据生成的音频时长(4.96秒)已调整字幕时间戳
2025-08-07 17:41:21,189 - INFO - ========== 新模式：为字幕 #48 生成4套场景方案 ==========
2025-08-07 17:41:21,189 - INFO - 字幕序号列表: [2111, 2113]
2025-08-07 17:41:21,189 - INFO - 
--- 生成方案 #1：基于字幕序号 #2111 ---
2025-08-07 17:41:21,189 - INFO - 开始为单个字幕序号 #2111 匹配场景，目标时长: 4.96秒
2025-08-07 17:41:21,189 - INFO - 开始查找字幕序号 [2111] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:21,190 - INFO - 找到related_overlap场景: scene_id=2377, 字幕#2111
2025-08-07 17:41:21,191 - INFO - 字幕 #2111 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:21,191 - INFO - 字幕序号 #2111 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:21,191 - INFO - 选择第一个overlap场景作为起点: scene_id=2377
2025-08-07 17:41:21,191 - INFO - 添加起点场景: scene_id=2377, 时长=2.80秒, 累计时长=2.80秒
2025-08-07 17:41:21,191 - INFO - 起点场景时长不足，需要延伸填充 2.16秒
2025-08-07 17:41:21,191 - INFO - 起点场景在原始列表中的索引: 2376
2025-08-07 17:41:21,191 - INFO - 延伸添加场景: scene_id=2378 (完整时长 1.60秒)
2025-08-07 17:41:21,191 - INFO - 累计时长: 4.40秒
2025-08-07 17:41:21,191 - INFO - 延伸添加场景: scene_id=2379 (裁剪至 0.56秒)
2025-08-07 17:41:21,191 - INFO - 累计时长: 4.96秒
2025-08-07 17:41:21,191 - INFO - 字幕序号 #2111 场景匹配完成，共选择 3 个场景，总时长: 4.96秒
2025-08-07 17:41:21,191 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-08-07 17:41:21,191 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-08-07 17:41:21,191 - INFO - 
--- 生成方案 #2：基于字幕序号 #2113 ---
2025-08-07 17:41:21,191 - INFO - 开始为单个字幕序号 #2113 匹配场景，目标时长: 4.96秒
2025-08-07 17:41:21,191 - INFO - 开始查找字幕序号 [2113] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:21,192 - INFO - 找到related_overlap场景: scene_id=2378, 字幕#2113
2025-08-07 17:41:21,192 - INFO - 字幕 #2113 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:21,192 - INFO - 字幕序号 #2113 找到 0 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:21,192 - ERROR - 字幕序号 #2113 没有找到任何可用的匹配场景
2025-08-07 17:41:21,192 - WARNING - 方案 #2 生成失败，未找到合适的场景
2025-08-07 17:41:21,192 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-08-07 17:41:21,192 - INFO - ========== 当前模式：为字幕 #48 生成 1 套场景方案 ==========
2025-08-07 17:41:21,192 - INFO - 开始查找字幕序号 [2111, 2113] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:21,193 - INFO - 找到related_overlap场景: scene_id=2377, 字幕#2111
2025-08-07 17:41:21,193 - INFO - 找到related_overlap场景: scene_id=2378, 字幕#2113
2025-08-07 17:41:21,193 - INFO - 字幕 #2111 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:21,194 - INFO - 字幕 #2113 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:21,194 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-08-07 17:41:21,194 - INFO - 开始生成方案 #1
2025-08-07 17:41:21,194 - INFO - 方案 #1: 为字幕#2111选择初始化overlap场景id=2377
2025-08-07 17:41:21,194 - INFO - 方案 #1: 为字幕#2113选择初始化overlap场景id=2378
2025-08-07 17:41:21,194 - INFO - 方案 #1: 初始选择后，当前总时长=4.40秒
2025-08-07 17:41:21,194 - INFO - 方案 #1: 额外between选择后，当前总时长=4.40秒
2025-08-07 17:41:21,194 - INFO - 方案 #1: 场景总时长(4.40秒)小于音频时长(4.96秒)，需要延伸填充
2025-08-07 17:41:21,194 - INFO - 方案 #1: 最后一个场景ID: 2378
2025-08-07 17:41:21,194 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 2377
2025-08-07 17:41:21,194 - INFO - 方案 #1: 需要填充时长: 0.56秒
2025-08-07 17:41:21,194 - INFO - 方案 #1: 追加场景 scene_id=2379 (裁剪至 0.56秒)
2025-08-07 17:41:21,194 - INFO - 方案 #1: 成功填充至目标时长
2025-08-07 17:41:21,194 - INFO - 方案 #1 调整/填充后最终总时长: 4.96秒
2025-08-07 17:41:21,194 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:21,194 - INFO - ========== 当前模式：字幕 #48 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:21,194 - INFO - 方案 #2 (传统模式) 生成成功
2025-08-07 17:41:21,194 - INFO - ========== 新模式：字幕 #48 共生成 2 套有效场景方案 ==========
2025-08-07 17:41:21,194 - INFO - 
----- 处理字幕 #48 的方案 #1 -----
2025-08-07 17:41:21,194 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\48_1.mp4
2025-08-07 17:41:21,194 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplulnm7gc
2025-08-07 17:41:21,195 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2377.mp4 (确认存在: True)
2025-08-07 17:41:21,195 - INFO - 添加场景ID=2377，时长=2.80秒，累计时长=2.80秒
2025-08-07 17:41:21,195 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2378.mp4 (确认存在: True)
2025-08-07 17:41:21,195 - INFO - 添加场景ID=2378，时长=1.60秒，累计时长=4.40秒
2025-08-07 17:41:21,195 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2379.mp4 (确认存在: True)
2025-08-07 17:41:21,195 - INFO - 添加场景ID=2379，时长=1.20秒，累计时长=5.60秒
2025-08-07 17:41:21,195 - INFO - 准备合并 3 个场景文件，总时长约 5.60秒
2025-08-07 17:41:21,195 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2377.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2378.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2379.mp4'

2025-08-07 17:41:21,195 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmplulnm7gc\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmplulnm7gc\temp_combined.mp4
2025-08-07 17:41:21,324 - INFO - 合并后的视频时长: 5.67秒，目标音频时长: 4.96秒
2025-08-07 17:41:21,324 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmplulnm7gc\temp_combined.mp4 -ss 0 -to 4.959 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\48_1.mp4
2025-08-07 17:41:21,671 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:21,671 - INFO - 目标音频时长: 4.96秒
2025-08-07 17:41:21,671 - INFO - 实际视频时长: 4.98秒
2025-08-07 17:41:21,671 - INFO - 时长差异: 0.02秒 (0.48%)
2025-08-07 17:41:21,671 - INFO - ==========================================
2025-08-07 17:41:21,671 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:21,671 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\48_1.mp4
2025-08-07 17:41:21,672 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplulnm7gc
2025-08-07 17:41:21,724 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:21,724 - INFO -   - 音频时长: 4.96秒
2025-08-07 17:41:21,724 - INFO -   - 视频时长: 4.98秒
2025-08-07 17:41:21,724 - INFO -   - 时长差异: 0.02秒 (0.48%)
2025-08-07 17:41:21,724 - INFO - 
----- 处理字幕 #48 的方案 #2 -----
2025-08-07 17:41:21,724 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\48_2.mp4
2025-08-07 17:41:21,724 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpj3m26qma
2025-08-07 17:41:21,725 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2377.mp4 (确认存在: True)
2025-08-07 17:41:21,725 - INFO - 添加场景ID=2377，时长=2.80秒，累计时长=2.80秒
2025-08-07 17:41:21,725 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2378.mp4 (确认存在: True)
2025-08-07 17:41:21,725 - INFO - 添加场景ID=2378，时长=1.60秒，累计时长=4.40秒
2025-08-07 17:41:21,725 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2379.mp4 (确认存在: True)
2025-08-07 17:41:21,725 - INFO - 添加场景ID=2379，时长=1.20秒，累计时长=5.60秒
2025-08-07 17:41:21,725 - INFO - 准备合并 3 个场景文件，总时长约 5.60秒
2025-08-07 17:41:21,725 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2377.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2378.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2379.mp4'

2025-08-07 17:41:21,727 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpj3m26qma\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpj3m26qma\temp_combined.mp4
2025-08-07 17:41:21,846 - INFO - 合并后的视频时长: 5.67秒，目标音频时长: 4.96秒
2025-08-07 17:41:21,847 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpj3m26qma\temp_combined.mp4 -ss 0 -to 4.959 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\48_2.mp4
2025-08-07 17:41:22,149 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:22,149 - INFO - 目标音频时长: 4.96秒
2025-08-07 17:41:22,149 - INFO - 实际视频时长: 4.98秒
2025-08-07 17:41:22,149 - INFO - 时长差异: 0.02秒 (0.48%)
2025-08-07 17:41:22,149 - INFO - ==========================================
2025-08-07 17:41:22,149 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:22,149 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\48_2.mp4
2025-08-07 17:41:22,150 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpj3m26qma
2025-08-07 17:41:22,192 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:22,192 - INFO -   - 音频时长: 4.96秒
2025-08-07 17:41:22,192 - INFO -   - 视频时长: 4.98秒
2025-08-07 17:41:22,192 - INFO -   - 时长差异: 0.02秒 (0.48%)
2025-08-07 17:41:22,192 - INFO - 
字幕 #48 处理完成，成功生成 2/2 套方案
2025-08-07 17:41:22,192 - INFO - 生成的视频文件:
2025-08-07 17:41:22,192 - INFO -   1. F:/github/aicut_auto/newcut_ai\48_1.mp4
2025-08-07 17:41:22,192 - INFO -   2. F:/github/aicut_auto/newcut_ai\48_2.mp4
2025-08-07 17:41:22,192 - INFO - ========== 字幕 #48 处理结束 ==========

