2025-08-07 17:40:49,279 - INFO - ========== 字幕 #25 处理开始 ==========
2025-08-07 17:40:49,279 - INFO - 字幕内容: 二弟见状急了，他拿出一份有九成股东联名支持的血书，声称按照祖训，他才是第一继承人。
2025-08-07 17:40:49,279 - INFO - 字幕序号: [687, 703]
2025-08-07 17:40:49,279 - INFO - 音频文件详情:
2025-08-07 17:40:49,279 - INFO -   - 路径: output\25.wav
2025-08-07 17:40:49,279 - INFO -   - 时长: 5.64秒
2025-08-07 17:40:49,279 - INFO -   - 验证音频时长: 5.64秒
2025-08-07 17:40:49,279 - INFO - 字幕时间戳信息:
2025-08-07 17:40:49,279 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:49,280 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:49,280 - INFO -   - 根据生成的音频时长(5.64秒)已调整字幕时间戳
2025-08-07 17:40:49,280 - INFO - ========== 新模式：为字幕 #25 生成4套场景方案 ==========
2025-08-07 17:40:49,280 - INFO - 字幕序号列表: [687, 703]
2025-08-07 17:40:49,280 - INFO - 
--- 生成方案 #1：基于字幕序号 #687 ---
2025-08-07 17:40:49,280 - INFO - 开始为单个字幕序号 #687 匹配场景，目标时长: 5.64秒
2025-08-07 17:40:49,280 - INFO - 开始查找字幕序号 [687] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:49,280 - INFO - 找到related_overlap场景: scene_id=837, 字幕#687
2025-08-07 17:40:49,281 - INFO - 字幕 #687 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:49,281 - INFO - 字幕序号 #687 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:49,281 - INFO - 选择第一个overlap场景作为起点: scene_id=837
2025-08-07 17:40:49,281 - INFO - 添加起点场景: scene_id=837, 时长=1.68秒, 累计时长=1.68秒
2025-08-07 17:40:49,281 - INFO - 起点场景时长不足，需要延伸填充 3.96秒
2025-08-07 17:40:49,281 - INFO - 起点场景在原始列表中的索引: 836
2025-08-07 17:40:49,281 - INFO - 延伸添加场景: scene_id=838 (完整时长 2.04秒)
2025-08-07 17:40:49,281 - INFO - 累计时长: 3.72秒
2025-08-07 17:40:49,281 - INFO - 延伸添加场景: scene_id=839 (裁剪至 1.92秒)
2025-08-07 17:40:49,281 - INFO - 累计时长: 5.64秒
2025-08-07 17:40:49,281 - INFO - 字幕序号 #687 场景匹配完成，共选择 3 个场景，总时长: 5.64秒
2025-08-07 17:40:49,281 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-08-07 17:40:49,281 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-08-07 17:40:49,281 - INFO - 
--- 生成方案 #2：基于字幕序号 #703 ---
2025-08-07 17:40:49,281 - INFO - 开始为单个字幕序号 #703 匹配场景，目标时长: 5.64秒
2025-08-07 17:40:49,281 - INFO - 开始查找字幕序号 [703] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:49,281 - INFO - 找到related_overlap场景: scene_id=858, 字幕#703
2025-08-07 17:40:49,282 - INFO - 字幕 #703 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:49,282 - INFO - 字幕序号 #703 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:49,282 - INFO - 选择第一个overlap场景作为起点: scene_id=858
2025-08-07 17:40:49,282 - INFO - 添加起点场景: scene_id=858, 时长=1.88秒, 累计时长=1.88秒
2025-08-07 17:40:49,282 - INFO - 起点场景时长不足，需要延伸填充 3.76秒
2025-08-07 17:40:49,282 - INFO - 起点场景在原始列表中的索引: 857
2025-08-07 17:40:49,282 - INFO - 延伸添加场景: scene_id=859 (完整时长 1.04秒)
2025-08-07 17:40:49,282 - INFO - 累计时长: 2.92秒
2025-08-07 17:40:49,282 - INFO - 延伸添加场景: scene_id=860 (完整时长 1.28秒)
2025-08-07 17:40:49,282 - INFO - 累计时长: 4.20秒
2025-08-07 17:40:49,282 - INFO - 延伸添加场景: scene_id=861 (裁剪至 1.45秒)
2025-08-07 17:40:49,282 - INFO - 累计时长: 5.64秒
2025-08-07 17:40:49,282 - INFO - 字幕序号 #703 场景匹配完成，共选择 4 个场景，总时长: 5.64秒
2025-08-07 17:40:49,282 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-08-07 17:40:49,283 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:49,283 - INFO - ========== 当前模式：为字幕 #25 生成 1 套场景方案 ==========
2025-08-07 17:40:49,283 - INFO - 开始查找字幕序号 [687, 703] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:49,283 - INFO - 找到related_overlap场景: scene_id=837, 字幕#687
2025-08-07 17:40:49,283 - INFO - 找到related_overlap场景: scene_id=858, 字幕#703
2025-08-07 17:40:49,284 - INFO - 字幕 #687 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:49,284 - INFO - 字幕 #703 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:49,284 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-08-07 17:40:49,284 - INFO - 开始生成方案 #1
2025-08-07 17:40:49,284 - INFO - 方案 #1: 为字幕#687选择初始化overlap场景id=837
2025-08-07 17:40:49,284 - INFO - 方案 #1: 为字幕#703选择初始化overlap场景id=858
2025-08-07 17:40:49,284 - INFO - 方案 #1: 初始选择后，当前总时长=3.56秒
2025-08-07 17:40:49,284 - INFO - 方案 #1: 额外between选择后，当前总时长=3.56秒
2025-08-07 17:40:49,284 - INFO - 方案 #1: 场景总时长(3.56秒)小于音频时长(5.64秒)，需要延伸填充
2025-08-07 17:40:49,284 - INFO - 方案 #1: 最后一个场景ID: 858
2025-08-07 17:40:49,284 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 857
2025-08-07 17:40:49,284 - INFO - 方案 #1: 需要填充时长: 2.08秒
2025-08-07 17:40:49,284 - INFO - 方案 #1: 追加场景 scene_id=859 (完整时长 1.04秒)
2025-08-07 17:40:49,284 - INFO - 方案 #1: 追加场景 scene_id=860 (裁剪至 1.05秒)
2025-08-07 17:40:49,284 - INFO - 方案 #1: 成功填充至目标时长
2025-08-07 17:40:49,284 - INFO - 方案 #1 调整/填充后最终总时长: 5.64秒
2025-08-07 17:40:49,284 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:49,284 - INFO - ========== 当前模式：字幕 #25 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:49,284 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:49,284 - INFO - ========== 新模式：字幕 #25 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:49,284 - INFO - 
----- 处理字幕 #25 的方案 #1 -----
2025-08-07 17:40:49,284 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\25_1.mp4
2025-08-07 17:40:49,284 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpg91bqvid
2025-08-07 17:40:49,285 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\837.mp4 (确认存在: True)
2025-08-07 17:40:49,285 - INFO - 添加场景ID=837，时长=1.68秒，累计时长=1.68秒
2025-08-07 17:40:49,285 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\838.mp4 (确认存在: True)
2025-08-07 17:40:49,285 - INFO - 添加场景ID=838，时长=2.04秒，累计时长=3.72秒
2025-08-07 17:40:49,285 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\839.mp4 (确认存在: True)
2025-08-07 17:40:49,285 - INFO - 添加场景ID=839，时长=3.32秒，累计时长=7.04秒
2025-08-07 17:40:49,285 - INFO - 准备合并 3 个场景文件，总时长约 7.04秒
2025-08-07 17:40:49,285 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/837.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/838.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/839.mp4'

2025-08-07 17:40:49,285 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpg91bqvid\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpg91bqvid\temp_combined.mp4
2025-08-07 17:40:49,421 - INFO - 合并后的视频时长: 7.11秒，目标音频时长: 5.64秒
2025-08-07 17:40:49,421 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpg91bqvid\temp_combined.mp4 -ss 0 -to 5.643 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\25_1.mp4
2025-08-07 17:40:49,729 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:49,729 - INFO - 目标音频时长: 5.64秒
2025-08-07 17:40:49,729 - INFO - 实际视频时长: 5.70秒
2025-08-07 17:40:49,729 - INFO - 时长差异: 0.06秒 (1.06%)
2025-08-07 17:40:49,729 - INFO - ==========================================
2025-08-07 17:40:49,729 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:49,729 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\25_1.mp4
2025-08-07 17:40:49,730 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpg91bqvid
2025-08-07 17:40:49,776 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:49,776 - INFO -   - 音频时长: 5.64秒
2025-08-07 17:40:49,776 - INFO -   - 视频时长: 5.70秒
2025-08-07 17:40:49,776 - INFO -   - 时长差异: 0.06秒 (1.06%)
2025-08-07 17:40:49,776 - INFO - 
----- 处理字幕 #25 的方案 #2 -----
2025-08-07 17:40:49,776 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\25_2.mp4
2025-08-07 17:40:49,777 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpmi8jk9x7
2025-08-07 17:40:49,777 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\858.mp4 (确认存在: True)
2025-08-07 17:40:49,777 - INFO - 添加场景ID=858，时长=1.88秒，累计时长=1.88秒
2025-08-07 17:40:49,777 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\859.mp4 (确认存在: True)
2025-08-07 17:40:49,777 - INFO - 添加场景ID=859，时长=1.04秒，累计时长=2.92秒
2025-08-07 17:40:49,777 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\860.mp4 (确认存在: True)
2025-08-07 17:40:49,777 - INFO - 添加场景ID=860，时长=1.28秒，累计时长=4.20秒
2025-08-07 17:40:49,778 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\861.mp4 (确认存在: True)
2025-08-07 17:40:49,778 - INFO - 添加场景ID=861，时长=1.96秒，累计时长=6.16秒
2025-08-07 17:40:49,778 - INFO - 准备合并 4 个场景文件，总时长约 6.16秒
2025-08-07 17:40:49,778 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/858.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/859.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/860.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/861.mp4'

2025-08-07 17:40:49,778 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpmi8jk9x7\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpmi8jk9x7\temp_combined.mp4
2025-08-07 17:40:49,947 - INFO - 合并后的视频时长: 6.25秒，目标音频时长: 5.64秒
2025-08-07 17:40:49,947 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpmi8jk9x7\temp_combined.mp4 -ss 0 -to 5.643 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\25_2.mp4
2025-08-07 17:40:50,302 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:50,302 - INFO - 目标音频时长: 5.64秒
2025-08-07 17:40:50,302 - INFO - 实际视频时长: 5.70秒
2025-08-07 17:40:50,302 - INFO - 时长差异: 0.06秒 (1.06%)
2025-08-07 17:40:50,302 - INFO - ==========================================
2025-08-07 17:40:50,302 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:50,302 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\25_2.mp4
2025-08-07 17:40:50,302 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpmi8jk9x7
2025-08-07 17:40:50,356 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:50,356 - INFO -   - 音频时长: 5.64秒
2025-08-07 17:40:50,356 - INFO -   - 视频时长: 5.70秒
2025-08-07 17:40:50,356 - INFO -   - 时长差异: 0.06秒 (1.06%)
2025-08-07 17:40:50,356 - INFO - 
----- 处理字幕 #25 的方案 #3 -----
2025-08-07 17:40:50,356 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\25_3.mp4
2025-08-07 17:40:50,357 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp9ljio2p0
2025-08-07 17:40:50,357 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\837.mp4 (确认存在: True)
2025-08-07 17:40:50,357 - INFO - 添加场景ID=837，时长=1.68秒，累计时长=1.68秒
2025-08-07 17:40:50,357 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\858.mp4 (确认存在: True)
2025-08-07 17:40:50,357 - INFO - 添加场景ID=858，时长=1.88秒，累计时长=3.56秒
2025-08-07 17:40:50,357 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\859.mp4 (确认存在: True)
2025-08-07 17:40:50,357 - INFO - 添加场景ID=859，时长=1.04秒，累计时长=4.60秒
2025-08-07 17:40:50,357 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\860.mp4 (确认存在: True)
2025-08-07 17:40:50,357 - INFO - 添加场景ID=860，时长=1.28秒，累计时长=5.88秒
2025-08-07 17:40:50,357 - INFO - 准备合并 4 个场景文件，总时长约 5.88秒
2025-08-07 17:40:50,357 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/837.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/858.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/859.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/860.mp4'

2025-08-07 17:40:50,357 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp9ljio2p0\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp9ljio2p0\temp_combined.mp4
2025-08-07 17:40:50,516 - INFO - 合并后的视频时长: 5.97秒，目标音频时长: 5.64秒
2025-08-07 17:40:50,516 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp9ljio2p0\temp_combined.mp4 -ss 0 -to 5.643 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\25_3.mp4
2025-08-07 17:40:50,830 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:50,830 - INFO - 目标音频时长: 5.64秒
2025-08-07 17:40:50,830 - INFO - 实际视频时长: 5.70秒
2025-08-07 17:40:50,830 - INFO - 时长差异: 0.06秒 (1.06%)
2025-08-07 17:40:50,830 - INFO - ==========================================
2025-08-07 17:40:50,830 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:50,830 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\25_3.mp4
2025-08-07 17:40:50,831 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp9ljio2p0
2025-08-07 17:40:50,878 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:50,878 - INFO -   - 音频时长: 5.64秒
2025-08-07 17:40:50,878 - INFO -   - 视频时长: 5.70秒
2025-08-07 17:40:50,878 - INFO -   - 时长差异: 0.06秒 (1.06%)
2025-08-07 17:40:50,878 - INFO - 
字幕 #25 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:50,879 - INFO - 生成的视频文件:
2025-08-07 17:40:50,879 - INFO -   1. F:/github/aicut_auto/newcut_ai\25_1.mp4
2025-08-07 17:40:50,879 - INFO -   2. F:/github/aicut_auto/newcut_ai\25_2.mp4
2025-08-07 17:40:50,879 - INFO -   3. F:/github/aicut_auto/newcut_ai\25_3.mp4
2025-08-07 17:40:50,879 - INFO - ========== 字幕 #25 处理结束 ==========

