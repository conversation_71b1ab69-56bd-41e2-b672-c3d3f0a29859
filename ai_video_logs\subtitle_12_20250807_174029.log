2025-08-07 17:40:29,630 - INFO - ========== 字幕 #12 处理开始 ==========
2025-08-07 17:40:29,630 - INFO - 字幕内容: 为了让女孩名正言顺留下，爷爷请求她治好大孙子的腿，女孩自信满满，这不过是轻而易举的小事。
2025-08-07 17:40:29,630 - INFO - 字幕序号: [270, 280]
2025-08-07 17:40:29,630 - INFO - 音频文件详情:
2025-08-07 17:40:29,630 - INFO -   - 路径: output\12.wav
2025-08-07 17:40:29,630 - INFO -   - 时长: 4.29秒
2025-08-07 17:40:29,631 - INFO -   - 验证音频时长: 4.29秒
2025-08-07 17:40:29,631 - INFO - 字幕时间戳信息:
2025-08-07 17:40:29,631 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:29,631 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:29,631 - INFO -   - 根据生成的音频时长(4.29秒)已调整字幕时间戳
2025-08-07 17:40:29,631 - INFO - ========== 新模式：为字幕 #12 生成4套场景方案 ==========
2025-08-07 17:40:29,631 - INFO - 字幕序号列表: [270, 280]
2025-08-07 17:40:29,631 - INFO - 
--- 生成方案 #1：基于字幕序号 #270 ---
2025-08-07 17:40:29,631 - INFO - 开始为单个字幕序号 #270 匹配场景，目标时长: 4.29秒
2025-08-07 17:40:29,631 - INFO - 开始查找字幕序号 [270] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:29,631 - INFO - 找到related_overlap场景: scene_id=403, 字幕#270
2025-08-07 17:40:29,632 - INFO - 字幕 #270 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:29,632 - INFO - 字幕序号 #270 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:29,632 - INFO - 选择第一个overlap场景作为起点: scene_id=403
2025-08-07 17:40:29,632 - INFO - 添加起点场景: scene_id=403, 时长=3.68秒, 累计时长=3.68秒
2025-08-07 17:40:29,632 - INFO - 起点场景时长不足，需要延伸填充 0.61秒
2025-08-07 17:40:29,632 - INFO - 起点场景在原始列表中的索引: 402
2025-08-07 17:40:29,632 - INFO - 延伸添加场景: scene_id=404 (裁剪至 0.61秒)
2025-08-07 17:40:29,632 - INFO - 累计时长: 4.29秒
2025-08-07 17:40:29,632 - INFO - 字幕序号 #270 场景匹配完成，共选择 2 个场景，总时长: 4.29秒
2025-08-07 17:40:29,632 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-08-07 17:40:29,632 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-08-07 17:40:29,632 - INFO - 
--- 生成方案 #2：基于字幕序号 #280 ---
2025-08-07 17:40:29,632 - INFO - 开始为单个字幕序号 #280 匹配场景，目标时长: 4.29秒
2025-08-07 17:40:29,632 - INFO - 开始查找字幕序号 [280] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:29,633 - INFO - 找到related_overlap场景: scene_id=412, 字幕#280
2025-08-07 17:40:29,633 - INFO - 字幕 #280 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:29,634 - INFO - 字幕序号 #280 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:29,634 - INFO - 选择第一个overlap场景作为起点: scene_id=412
2025-08-07 17:40:29,634 - INFO - 添加起点场景: scene_id=412, 时长=1.72秒, 累计时长=1.72秒
2025-08-07 17:40:29,634 - INFO - 起点场景时长不足，需要延伸填充 2.57秒
2025-08-07 17:40:29,634 - INFO - 起点场景在原始列表中的索引: 411
2025-08-07 17:40:29,634 - INFO - 延伸添加场景: scene_id=413 (完整时长 1.36秒)
2025-08-07 17:40:29,634 - INFO - 累计时长: 3.08秒
2025-08-07 17:40:29,634 - INFO - 延伸添加场景: scene_id=414 (完整时长 0.92秒)
2025-08-07 17:40:29,634 - INFO - 累计时长: 4.00秒
2025-08-07 17:40:29,634 - INFO - 延伸添加场景: scene_id=415 (裁剪至 0.29秒)
2025-08-07 17:40:29,634 - INFO - 累计时长: 4.29秒
2025-08-07 17:40:29,634 - INFO - 字幕序号 #280 场景匹配完成，共选择 4 个场景，总时长: 4.29秒
2025-08-07 17:40:29,634 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-08-07 17:40:29,634 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:29,634 - INFO - ========== 当前模式：为字幕 #12 生成 1 套场景方案 ==========
2025-08-07 17:40:29,634 - INFO - 开始查找字幕序号 [270, 280] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:29,634 - INFO - 找到related_overlap场景: scene_id=403, 字幕#270
2025-08-07 17:40:29,634 - INFO - 找到related_overlap场景: scene_id=412, 字幕#280
2025-08-07 17:40:29,635 - INFO - 字幕 #270 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:29,635 - INFO - 字幕 #280 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:29,635 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-08-07 17:40:29,635 - INFO - 开始生成方案 #1
2025-08-07 17:40:29,635 - INFO - 方案 #1: 为字幕#270选择初始化overlap场景id=403
2025-08-07 17:40:29,635 - INFO - 方案 #1: 为字幕#280选择初始化overlap场景id=412
2025-08-07 17:40:29,635 - INFO - 方案 #1: 初始选择后，当前总时长=5.40秒
2025-08-07 17:40:29,635 - INFO - 方案 #1: 额外between选择后，当前总时长=5.40秒
2025-08-07 17:40:29,635 - INFO - 方案 #1: 场景总时长(5.40秒)大于音频时长(4.29秒)，需要裁剪
2025-08-07 17:40:29,635 - INFO - 调整前总时长: 5.40秒, 目标时长: 4.29秒
2025-08-07 17:40:29,635 - INFO - 需要裁剪 1.11秒
2025-08-07 17:40:29,635 - INFO - 裁剪最长场景ID=403：从3.68秒裁剪至2.57秒
2025-08-07 17:40:29,635 - INFO - 调整后总时长: 4.29秒，与目标时长差异: 0.00秒
2025-08-07 17:40:29,635 - INFO - 方案 #1 调整/填充后最终总时长: 4.29秒
2025-08-07 17:40:29,635 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:29,635 - INFO - ========== 当前模式：字幕 #12 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:29,635 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:29,635 - INFO - ========== 新模式：字幕 #12 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:29,635 - INFO - 
----- 处理字幕 #12 的方案 #1 -----
2025-08-07 17:40:29,635 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\12_1.mp4
2025-08-07 17:40:29,636 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpga6j_m_e
2025-08-07 17:40:29,637 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\403.mp4 (确认存在: True)
2025-08-07 17:40:29,637 - INFO - 添加场景ID=403，时长=3.68秒，累计时长=3.68秒
2025-08-07 17:40:29,637 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\404.mp4 (确认存在: True)
2025-08-07 17:40:29,637 - INFO - 添加场景ID=404，时长=1.60秒，累计时长=5.28秒
2025-08-07 17:40:29,637 - INFO - 准备合并 2 个场景文件，总时长约 5.28秒
2025-08-07 17:40:29,637 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/403.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/404.mp4'

2025-08-07 17:40:29,637 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpga6j_m_e\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpga6j_m_e\temp_combined.mp4
2025-08-07 17:40:29,772 - INFO - 合并后的视频时长: 5.33秒，目标音频时长: 4.29秒
2025-08-07 17:40:29,772 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpga6j_m_e\temp_combined.mp4 -ss 0 -to 4.291 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\12_1.mp4
2025-08-07 17:40:30,070 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:30,071 - INFO - 目标音频时长: 4.29秒
2025-08-07 17:40:30,071 - INFO - 实际视频时长: 4.34秒
2025-08-07 17:40:30,071 - INFO - 时长差异: 0.05秒 (1.21%)
2025-08-07 17:40:30,071 - INFO - ==========================================
2025-08-07 17:40:30,071 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:30,071 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\12_1.mp4
2025-08-07 17:40:30,071 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpga6j_m_e
2025-08-07 17:40:30,115 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:30,115 - INFO -   - 音频时长: 4.29秒
2025-08-07 17:40:30,115 - INFO -   - 视频时长: 4.34秒
2025-08-07 17:40:30,115 - INFO -   - 时长差异: 0.05秒 (1.21%)
2025-08-07 17:40:30,115 - INFO - 
----- 处理字幕 #12 的方案 #2 -----
2025-08-07 17:40:30,115 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\12_2.mp4
2025-08-07 17:40:30,116 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp27wnq_uj
2025-08-07 17:40:30,116 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\412.mp4 (确认存在: True)
2025-08-07 17:40:30,116 - INFO - 添加场景ID=412，时长=1.72秒，累计时长=1.72秒
2025-08-07 17:40:30,116 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\413.mp4 (确认存在: True)
2025-08-07 17:40:30,116 - INFO - 添加场景ID=413，时长=1.36秒，累计时长=3.08秒
2025-08-07 17:40:30,116 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\414.mp4 (确认存在: True)
2025-08-07 17:40:30,116 - INFO - 添加场景ID=414，时长=0.92秒，累计时长=4.00秒
2025-08-07 17:40:30,116 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\415.mp4 (确认存在: True)
2025-08-07 17:40:30,116 - INFO - 添加场景ID=415，时长=1.16秒，累计时长=5.16秒
2025-08-07 17:40:30,116 - INFO - 准备合并 4 个场景文件，总时长约 5.16秒
2025-08-07 17:40:30,116 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/412.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/413.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/414.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/415.mp4'

2025-08-07 17:40:30,117 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp27wnq_uj\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp27wnq_uj\temp_combined.mp4
2025-08-07 17:40:30,278 - INFO - 合并后的视频时长: 5.25秒，目标音频时长: 4.29秒
2025-08-07 17:40:30,278 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp27wnq_uj\temp_combined.mp4 -ss 0 -to 4.291 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\12_2.mp4
2025-08-07 17:40:30,569 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:30,569 - INFO - 目标音频时长: 4.29秒
2025-08-07 17:40:30,569 - INFO - 实际视频时长: 4.34秒
2025-08-07 17:40:30,569 - INFO - 时长差异: 0.05秒 (1.21%)
2025-08-07 17:40:30,569 - INFO - ==========================================
2025-08-07 17:40:30,569 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:30,569 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\12_2.mp4
2025-08-07 17:40:30,569 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp27wnq_uj
2025-08-07 17:40:30,612 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:30,612 - INFO -   - 音频时长: 4.29秒
2025-08-07 17:40:30,612 - INFO -   - 视频时长: 4.34秒
2025-08-07 17:40:30,612 - INFO -   - 时长差异: 0.05秒 (1.21%)
2025-08-07 17:40:30,612 - INFO - 
----- 处理字幕 #12 的方案 #3 -----
2025-08-07 17:40:30,612 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\12_3.mp4
2025-08-07 17:40:30,613 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpi9p3gz8d
2025-08-07 17:40:30,613 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\403.mp4 (确认存在: True)
2025-08-07 17:40:30,613 - INFO - 添加场景ID=403，时长=3.68秒，累计时长=3.68秒
2025-08-07 17:40:30,613 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\412.mp4 (确认存在: True)
2025-08-07 17:40:30,613 - INFO - 添加场景ID=412，时长=1.72秒，累计时长=5.40秒
2025-08-07 17:40:30,613 - INFO - 准备合并 2 个场景文件，总时长约 5.40秒
2025-08-07 17:40:30,613 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/403.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/412.mp4'

2025-08-07 17:40:30,614 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpi9p3gz8d\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpi9p3gz8d\temp_combined.mp4
2025-08-07 17:40:30,749 - INFO - 合并后的视频时长: 5.45秒，目标音频时长: 4.29秒
2025-08-07 17:40:30,749 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpi9p3gz8d\temp_combined.mp4 -ss 0 -to 4.291 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\12_3.mp4
2025-08-07 17:40:31,035 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:31,035 - INFO - 目标音频时长: 4.29秒
2025-08-07 17:40:31,035 - INFO - 实际视频时长: 4.34秒
2025-08-07 17:40:31,035 - INFO - 时长差异: 0.05秒 (1.21%)
2025-08-07 17:40:31,035 - INFO - ==========================================
2025-08-07 17:40:31,035 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:31,035 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\12_3.mp4
2025-08-07 17:40:31,035 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpi9p3gz8d
2025-08-07 17:40:31,083 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:31,083 - INFO -   - 音频时长: 4.29秒
2025-08-07 17:40:31,083 - INFO -   - 视频时长: 4.34秒
2025-08-07 17:40:31,083 - INFO -   - 时长差异: 0.05秒 (1.21%)
2025-08-07 17:40:31,083 - INFO - 
字幕 #12 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:31,083 - INFO - 生成的视频文件:
2025-08-07 17:40:31,083 - INFO -   1. F:/github/aicut_auto/newcut_ai\12_1.mp4
2025-08-07 17:40:31,083 - INFO -   2. F:/github/aicut_auto/newcut_ai\12_2.mp4
2025-08-07 17:40:31,083 - INFO -   3. F:/github/aicut_auto/newcut_ai\12_3.mp4
2025-08-07 17:40:31,083 - INFO - ========== 字幕 #12 处理结束 ==========

