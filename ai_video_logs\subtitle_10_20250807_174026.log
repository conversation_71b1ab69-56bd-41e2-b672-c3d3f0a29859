2025-08-07 17:40:26,767 - INFO - ========== 字幕 #10 处理开始 ==========
2025-08-07 17:40:26,767 - INFO - 字幕内容: 为了掩人耳目，爷爷只好对外宣称，女孩是自己大孙子流落在外的女儿。
2025-08-07 17:40:26,768 - INFO - 字幕序号: [172, 177]
2025-08-07 17:40:26,768 - INFO - 音频文件详情:
2025-08-07 17:40:26,768 - INFO -   - 路径: output\10.wav
2025-08-07 17:40:26,768 - INFO -   - 时长: 3.74秒
2025-08-07 17:40:26,768 - INFO -   - 验证音频时长: 3.74秒
2025-08-07 17:40:26,768 - INFO - 字幕时间戳信息:
2025-08-07 17:40:26,768 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:26,768 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:26,768 - INFO -   - 根据生成的音频时长(3.74秒)已调整字幕时间戳
2025-08-07 17:40:26,768 - INFO - ========== 新模式：为字幕 #10 生成4套场景方案 ==========
2025-08-07 17:40:26,768 - INFO - 字幕序号列表: [172, 177]
2025-08-07 17:40:26,768 - INFO - 
--- 生成方案 #1：基于字幕序号 #172 ---
2025-08-07 17:40:26,768 - INFO - 开始为单个字幕序号 #172 匹配场景，目标时长: 3.74秒
2025-08-07 17:40:26,768 - INFO - 开始查找字幕序号 [172] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:26,768 - INFO - 找到related_overlap场景: scene_id=239, 字幕#172
2025-08-07 17:40:26,769 - INFO - 找到related_between场景: scene_id=240, 字幕#172
2025-08-07 17:40:26,769 - INFO - 找到related_between场景: scene_id=241, 字幕#172
2025-08-07 17:40:26,769 - INFO - 找到related_between场景: scene_id=242, 字幕#172
2025-08-07 17:40:26,769 - INFO - 找到related_between场景: scene_id=243, 字幕#172
2025-08-07 17:40:26,769 - INFO - 找到related_between场景: scene_id=244, 字幕#172
2025-08-07 17:40:26,770 - INFO - 字幕 #172 找到 1 个overlap场景, 5 个between场景
2025-08-07 17:40:26,770 - INFO - 字幕序号 #172 找到 1 个可用overlap场景, 5 个可用between场景
2025-08-07 17:40:26,770 - INFO - 选择第一个overlap场景作为起点: scene_id=239
2025-08-07 17:40:26,770 - INFO - 添加起点场景: scene_id=239, 时长=1.92秒, 累计时长=1.92秒
2025-08-07 17:40:26,770 - INFO - 起点场景时长不足，需要延伸填充 1.82秒
2025-08-07 17:40:26,770 - INFO - 起点场景在原始列表中的索引: 238
2025-08-07 17:40:26,770 - INFO - 延伸添加场景: scene_id=240 (完整时长 0.76秒)
2025-08-07 17:40:26,770 - INFO - 累计时长: 2.68秒
2025-08-07 17:40:26,770 - INFO - 延伸添加场景: scene_id=241 (完整时长 0.88秒)
2025-08-07 17:40:26,770 - INFO - 累计时长: 3.56秒
2025-08-07 17:40:26,770 - INFO - 延伸添加场景: scene_id=242 (裁剪至 0.18秒)
2025-08-07 17:40:26,770 - INFO - 累计时长: 3.74秒
2025-08-07 17:40:26,770 - INFO - 字幕序号 #172 场景匹配完成，共选择 4 个场景，总时长: 3.74秒
2025-08-07 17:40:26,771 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-08-07 17:40:26,771 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-08-07 17:40:26,771 - INFO - 
--- 生成方案 #2：基于字幕序号 #177 ---
2025-08-07 17:40:26,771 - INFO - 开始为单个字幕序号 #177 匹配场景，目标时长: 3.74秒
2025-08-07 17:40:26,771 - INFO - 开始查找字幕序号 [177] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:26,771 - INFO - 找到related_overlap场景: scene_id=261, 字幕#177
2025-08-07 17:40:26,771 - INFO - 找到related_overlap场景: scene_id=262, 字幕#177
2025-08-07 17:40:26,772 - INFO - 字幕 #177 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:40:26,772 - INFO - 字幕序号 #177 找到 2 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:26,772 - INFO - 选择第一个overlap场景作为起点: scene_id=261
2025-08-07 17:40:26,772 - INFO - 添加起点场景: scene_id=261, 时长=1.08秒, 累计时长=1.08秒
2025-08-07 17:40:26,772 - INFO - 起点场景时长不足，需要延伸填充 2.66秒
2025-08-07 17:40:26,772 - INFO - 起点场景在原始列表中的索引: 260
2025-08-07 17:40:26,772 - INFO - 延伸添加场景: scene_id=262 (完整时长 1.76秒)
2025-08-07 17:40:26,772 - INFO - 累计时长: 2.84秒
2025-08-07 17:40:26,772 - INFO - 延伸添加场景: scene_id=263 (裁剪至 0.90秒)
2025-08-07 17:40:26,772 - INFO - 累计时长: 3.74秒
2025-08-07 17:40:26,772 - INFO - 字幕序号 #177 场景匹配完成，共选择 3 个场景，总时长: 3.74秒
2025-08-07 17:40:26,772 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-08-07 17:40:26,772 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:26,772 - INFO - ========== 当前模式：为字幕 #10 生成 1 套场景方案 ==========
2025-08-07 17:40:26,772 - INFO - 开始查找字幕序号 [172, 177] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:26,772 - INFO - 找到related_overlap场景: scene_id=239, 字幕#172
2025-08-07 17:40:26,772 - INFO - 找到related_overlap场景: scene_id=261, 字幕#177
2025-08-07 17:40:26,772 - INFO - 找到related_overlap场景: scene_id=262, 字幕#177
2025-08-07 17:40:26,773 - INFO - 找到related_between场景: scene_id=240, 字幕#172
2025-08-07 17:40:26,773 - INFO - 找到related_between场景: scene_id=241, 字幕#172
2025-08-07 17:40:26,773 - INFO - 找到related_between场景: scene_id=242, 字幕#172
2025-08-07 17:40:26,773 - INFO - 找到related_between场景: scene_id=243, 字幕#172
2025-08-07 17:40:26,773 - INFO - 找到related_between场景: scene_id=244, 字幕#172
2025-08-07 17:40:26,773 - INFO - 字幕 #172 找到 1 个overlap场景, 5 个between场景
2025-08-07 17:40:26,773 - INFO - 字幕 #177 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:40:26,773 - INFO - 共收集 3 个未使用的overlap场景和 5 个未使用的between场景
2025-08-07 17:40:26,773 - INFO - 开始生成方案 #1
2025-08-07 17:40:26,773 - INFO - 方案 #1: 为字幕#172选择初始化overlap场景id=239
2025-08-07 17:40:26,773 - INFO - 方案 #1: 为字幕#177选择初始化overlap场景id=262
2025-08-07 17:40:26,773 - INFO - 方案 #1: 初始选择后，当前总时长=3.68秒
2025-08-07 17:40:26,773 - INFO - 方案 #1: 额外添加overlap场景id=261, 当前总时长=4.76秒
2025-08-07 17:40:26,773 - INFO - 方案 #1: 额外between选择后，当前总时长=4.76秒
2025-08-07 17:40:26,773 - INFO - 方案 #1: 场景总时长(4.76秒)大于音频时长(3.74秒)，需要裁剪
2025-08-07 17:40:26,773 - INFO - 调整前总时长: 4.76秒, 目标时长: 3.74秒
2025-08-07 17:40:26,773 - INFO - 需要裁剪 1.02秒
2025-08-07 17:40:26,773 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-08-07 17:40:26,773 - INFO - 裁剪场景ID=239：从1.92秒裁剪至1.00秒
2025-08-07 17:40:26,773 - INFO - 裁剪场景ID=262：从1.76秒裁剪至1.66秒
2025-08-07 17:40:26,773 - INFO - 调整后总时长: 3.74秒，与目标时长差异: 0.00秒
2025-08-07 17:40:26,773 - INFO - 方案 #1 调整/填充后最终总时长: 3.74秒
2025-08-07 17:40:26,773 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:26,773 - INFO - ========== 当前模式：字幕 #10 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:26,773 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:26,773 - INFO - ========== 新模式：字幕 #10 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:26,774 - INFO - 
----- 处理字幕 #10 的方案 #1 -----
2025-08-07 17:40:26,774 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\10_1.mp4
2025-08-07 17:40:26,774 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmptva8knea
2025-08-07 17:40:26,774 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\239.mp4 (确认存在: True)
2025-08-07 17:40:26,775 - INFO - 添加场景ID=239，时长=1.92秒，累计时长=1.92秒
2025-08-07 17:40:26,775 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\240.mp4 (确认存在: True)
2025-08-07 17:40:26,775 - INFO - 添加场景ID=240，时长=0.76秒，累计时长=2.68秒
2025-08-07 17:40:26,775 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\241.mp4 (确认存在: True)
2025-08-07 17:40:26,775 - INFO - 添加场景ID=241，时长=0.88秒，累计时长=3.56秒
2025-08-07 17:40:26,775 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\242.mp4 (确认存在: True)
2025-08-07 17:40:26,775 - INFO - 添加场景ID=242，时长=1.32秒，累计时长=4.88秒
2025-08-07 17:40:26,775 - INFO - 准备合并 4 个场景文件，总时长约 4.88秒
2025-08-07 17:40:26,775 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/239.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/240.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/241.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/242.mp4'

2025-08-07 17:40:26,775 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmptva8knea\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmptva8knea\temp_combined.mp4
2025-08-07 17:40:26,941 - INFO - 合并后的视频时长: 4.97秒，目标音频时长: 3.74秒
2025-08-07 17:40:26,941 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmptva8knea\temp_combined.mp4 -ss 0 -to 3.737 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\10_1.mp4
2025-08-07 17:40:27,217 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:27,217 - INFO - 目标音频时长: 3.74秒
2025-08-07 17:40:27,217 - INFO - 实际视频时长: 3.78秒
2025-08-07 17:40:27,217 - INFO - 时长差异: 0.05秒 (1.23%)
2025-08-07 17:40:27,217 - INFO - ==========================================
2025-08-07 17:40:27,217 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:27,217 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\10_1.mp4
2025-08-07 17:40:27,218 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmptva8knea
2025-08-07 17:40:27,261 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:27,261 - INFO -   - 音频时长: 3.74秒
2025-08-07 17:40:27,261 - INFO -   - 视频时长: 3.78秒
2025-08-07 17:40:27,261 - INFO -   - 时长差异: 0.05秒 (1.23%)
2025-08-07 17:40:27,261 - INFO - 
----- 处理字幕 #10 的方案 #2 -----
2025-08-07 17:40:27,261 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\10_2.mp4
2025-08-07 17:40:27,261 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpp1e0pfu_
2025-08-07 17:40:27,262 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\261.mp4 (确认存在: True)
2025-08-07 17:40:27,262 - INFO - 添加场景ID=261，时长=1.08秒，累计时长=1.08秒
2025-08-07 17:40:27,262 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\262.mp4 (确认存在: True)
2025-08-07 17:40:27,262 - INFO - 添加场景ID=262，时长=1.76秒，累计时长=2.84秒
2025-08-07 17:40:27,262 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\263.mp4 (确认存在: True)
2025-08-07 17:40:27,262 - INFO - 添加场景ID=263，时长=2.80秒，累计时长=5.64秒
2025-08-07 17:40:27,262 - INFO - 场景总时长(5.64秒)已达到音频时长(3.74秒)的1.5倍，停止添加场景
2025-08-07 17:40:27,262 - INFO - 准备合并 3 个场景文件，总时长约 5.64秒
2025-08-07 17:40:27,262 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/261.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/262.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/263.mp4'

2025-08-07 17:40:27,262 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpp1e0pfu_\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpp1e0pfu_\temp_combined.mp4
2025-08-07 17:40:27,416 - INFO - 合并后的视频时长: 5.71秒，目标音频时长: 3.74秒
2025-08-07 17:40:27,416 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpp1e0pfu_\temp_combined.mp4 -ss 0 -to 3.737 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\10_2.mp4
2025-08-07 17:40:27,702 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:27,702 - INFO - 目标音频时长: 3.74秒
2025-08-07 17:40:27,702 - INFO - 实际视频时长: 3.78秒
2025-08-07 17:40:27,702 - INFO - 时长差异: 0.05秒 (1.23%)
2025-08-07 17:40:27,702 - INFO - ==========================================
2025-08-07 17:40:27,702 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:27,702 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\10_2.mp4
2025-08-07 17:40:27,703 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpp1e0pfu_
2025-08-07 17:40:27,750 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:27,750 - INFO -   - 音频时长: 3.74秒
2025-08-07 17:40:27,750 - INFO -   - 视频时长: 3.78秒
2025-08-07 17:40:27,750 - INFO -   - 时长差异: 0.05秒 (1.23%)
2025-08-07 17:40:27,750 - INFO - 
----- 处理字幕 #10 的方案 #3 -----
2025-08-07 17:40:27,750 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\10_3.mp4
2025-08-07 17:40:27,750 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp4szq3io7
2025-08-07 17:40:27,751 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\239.mp4 (确认存在: True)
2025-08-07 17:40:27,751 - INFO - 添加场景ID=239，时长=1.92秒，累计时长=1.92秒
2025-08-07 17:40:27,751 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\262.mp4 (确认存在: True)
2025-08-07 17:40:27,751 - INFO - 添加场景ID=262，时长=1.76秒，累计时长=3.68秒
2025-08-07 17:40:27,751 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\261.mp4 (确认存在: True)
2025-08-07 17:40:27,751 - INFO - 添加场景ID=261，时长=1.08秒，累计时长=4.76秒
2025-08-07 17:40:27,751 - INFO - 准备合并 3 个场景文件，总时长约 4.76秒
2025-08-07 17:40:27,751 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/239.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/262.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/261.mp4'

2025-08-07 17:40:27,751 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp4szq3io7\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp4szq3io7\temp_combined.mp4
2025-08-07 17:40:27,886 - INFO - 合并后的视频时长: 4.83秒，目标音频时长: 3.74秒
2025-08-07 17:40:27,886 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp4szq3io7\temp_combined.mp4 -ss 0 -to 3.737 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\10_3.mp4
2025-08-07 17:40:28,155 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:28,155 - INFO - 目标音频时长: 3.74秒
2025-08-07 17:40:28,155 - INFO - 实际视频时长: 3.78秒
2025-08-07 17:40:28,155 - INFO - 时长差异: 0.05秒 (1.23%)
2025-08-07 17:40:28,155 - INFO - ==========================================
2025-08-07 17:40:28,155 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:28,155 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\10_3.mp4
2025-08-07 17:40:28,155 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp4szq3io7
2025-08-07 17:40:28,202 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:28,202 - INFO -   - 音频时长: 3.74秒
2025-08-07 17:40:28,202 - INFO -   - 视频时长: 3.78秒
2025-08-07 17:40:28,202 - INFO -   - 时长差异: 0.05秒 (1.23%)
2025-08-07 17:40:28,202 - INFO - 
字幕 #10 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:28,202 - INFO - 生成的视频文件:
2025-08-07 17:40:28,202 - INFO -   1. F:/github/aicut_auto/newcut_ai\10_1.mp4
2025-08-07 17:40:28,202 - INFO -   2. F:/github/aicut_auto/newcut_ai\10_2.mp4
2025-08-07 17:40:28,202 - INFO -   3. F:/github/aicut_auto/newcut_ai\10_3.mp4
2025-08-07 17:40:28,202 - INFO - ========== 字幕 #10 处理结束 ==========

