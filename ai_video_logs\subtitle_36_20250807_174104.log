2025-08-07 17:41:04,467 - INFO - ========== 字幕 #36 处理开始 ==========
2025-08-07 17:41:04,467 - INFO - 字幕内容: 心机女反锁房门，以为可以拖延时间，却没想到女孩只是轻轻一挥手，坚固的合金门便应声而开。
2025-08-07 17:41:04,467 - INFO - 字幕序号: [1316, 1327]
2025-08-07 17:41:04,467 - INFO - 音频文件详情:
2025-08-07 17:41:04,467 - INFO -   - 路径: output\36.wav
2025-08-07 17:41:04,467 - INFO -   - 时长: 4.60秒
2025-08-07 17:41:04,467 - INFO -   - 验证音频时长: 4.60秒
2025-08-07 17:41:04,467 - INFO - 字幕时间戳信息:
2025-08-07 17:41:04,477 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:04,477 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:04,477 - INFO -   - 根据生成的音频时长(4.60秒)已调整字幕时间戳
2025-08-07 17:41:04,477 - INFO - ========== 新模式：为字幕 #36 生成4套场景方案 ==========
2025-08-07 17:41:04,477 - INFO - 字幕序号列表: [1316, 1327]
2025-08-07 17:41:04,477 - INFO - 
--- 生成方案 #1：基于字幕序号 #1316 ---
2025-08-07 17:41:04,477 - INFO - 开始为单个字幕序号 #1316 匹配场景，目标时长: 4.60秒
2025-08-07 17:41:04,477 - INFO - 开始查找字幕序号 [1316] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:04,478 - INFO - 找到related_overlap场景: scene_id=1513, 字幕#1316
2025-08-07 17:41:04,478 - INFO - 找到related_between场景: scene_id=1512, 字幕#1316
2025-08-07 17:41:04,479 - INFO - 字幕 #1316 找到 1 个overlap场景, 1 个between场景
2025-08-07 17:41:04,479 - INFO - 字幕序号 #1316 找到 1 个可用overlap场景, 1 个可用between场景
2025-08-07 17:41:04,479 - INFO - 选择第一个overlap场景作为起点: scene_id=1513
2025-08-07 17:41:04,479 - INFO - 添加起点场景: scene_id=1513, 时长=1.60秒, 累计时长=1.60秒
2025-08-07 17:41:04,479 - INFO - 起点场景时长不足，需要延伸填充 3.00秒
2025-08-07 17:41:04,479 - INFO - 起点场景在原始列表中的索引: 1512
2025-08-07 17:41:04,479 - INFO - 延伸添加场景: scene_id=1514 (完整时长 2.08秒)
2025-08-07 17:41:04,479 - INFO - 累计时长: 3.68秒
2025-08-07 17:41:04,479 - INFO - 延伸添加场景: scene_id=1515 (裁剪至 0.92秒)
2025-08-07 17:41:04,479 - INFO - 累计时长: 4.60秒
2025-08-07 17:41:04,479 - INFO - 字幕序号 #1316 场景匹配完成，共选择 3 个场景，总时长: 4.60秒
2025-08-07 17:41:04,479 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-08-07 17:41:04,479 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-08-07 17:41:04,479 - INFO - 
--- 生成方案 #2：基于字幕序号 #1327 ---
2025-08-07 17:41:04,479 - INFO - 开始为单个字幕序号 #1327 匹配场景，目标时长: 4.60秒
2025-08-07 17:41:04,479 - INFO - 开始查找字幕序号 [1327] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:04,479 - INFO - 找到related_overlap场景: scene_id=1521, 字幕#1327
2025-08-07 17:41:04,480 - INFO - 找到related_between场景: scene_id=1522, 字幕#1327
2025-08-07 17:41:04,480 - INFO - 找到related_between场景: scene_id=1523, 字幕#1327
2025-08-07 17:41:04,480 - INFO - 字幕 #1327 找到 1 个overlap场景, 2 个between场景
2025-08-07 17:41:04,480 - INFO - 字幕序号 #1327 找到 1 个可用overlap场景, 2 个可用between场景
2025-08-07 17:41:04,480 - INFO - 选择第一个overlap场景作为起点: scene_id=1521
2025-08-07 17:41:04,480 - INFO - 添加起点场景: scene_id=1521, 时长=1.68秒, 累计时长=1.68秒
2025-08-07 17:41:04,480 - INFO - 起点场景时长不足，需要延伸填充 2.92秒
2025-08-07 17:41:04,480 - INFO - 起点场景在原始列表中的索引: 1520
2025-08-07 17:41:04,480 - INFO - 延伸添加场景: scene_id=1522 (完整时长 0.92秒)
2025-08-07 17:41:04,480 - INFO - 累计时长: 2.60秒
2025-08-07 17:41:04,480 - INFO - 延伸添加场景: scene_id=1523 (完整时长 1.28秒)
2025-08-07 17:41:04,480 - INFO - 累计时长: 3.88秒
2025-08-07 17:41:04,480 - INFO - 延伸添加场景: scene_id=1524 (裁剪至 0.72秒)
2025-08-07 17:41:04,480 - INFO - 累计时长: 4.60秒
2025-08-07 17:41:04,480 - INFO - 字幕序号 #1327 场景匹配完成，共选择 4 个场景，总时长: 4.60秒
2025-08-07 17:41:04,480 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-08-07 17:41:04,480 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:04,480 - INFO - ========== 当前模式：为字幕 #36 生成 1 套场景方案 ==========
2025-08-07 17:41:04,480 - INFO - 开始查找字幕序号 [1316, 1327] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:04,481 - INFO - 找到related_overlap场景: scene_id=1513, 字幕#1316
2025-08-07 17:41:04,481 - INFO - 找到related_overlap场景: scene_id=1521, 字幕#1327
2025-08-07 17:41:04,481 - INFO - 找到related_between场景: scene_id=1512, 字幕#1316
2025-08-07 17:41:04,481 - INFO - 找到related_between场景: scene_id=1522, 字幕#1327
2025-08-07 17:41:04,481 - INFO - 找到related_between场景: scene_id=1523, 字幕#1327
2025-08-07 17:41:04,481 - INFO - 字幕 #1316 找到 1 个overlap场景, 1 个between场景
2025-08-07 17:41:04,481 - INFO - 字幕 #1327 找到 1 个overlap场景, 2 个between场景
2025-08-07 17:41:04,481 - INFO - 共收集 2 个未使用的overlap场景和 3 个未使用的between场景
2025-08-07 17:41:04,481 - INFO - 开始生成方案 #1
2025-08-07 17:41:04,481 - INFO - 方案 #1: 为字幕#1316选择初始化overlap场景id=1513
2025-08-07 17:41:04,481 - INFO - 方案 #1: 为字幕#1327选择初始化overlap场景id=1521
2025-08-07 17:41:04,481 - INFO - 方案 #1: 初始选择后，当前总时长=3.28秒
2025-08-07 17:41:04,482 - INFO - 方案 #1: 额外between选择后，当前总时长=3.28秒
2025-08-07 17:41:04,482 - INFO - 方案 #1: 额外添加between场景id=1523, 当前总时长=4.56秒
2025-08-07 17:41:04,482 - INFO - 方案 #1: 额外添加between场景id=1522, 当前总时长=5.48秒
2025-08-07 17:41:04,482 - INFO - 方案 #1: 场景总时长(5.48秒)大于音频时长(4.60秒)，需要裁剪
2025-08-07 17:41:04,482 - INFO - 调整前总时长: 5.48秒, 目标时长: 4.60秒
2025-08-07 17:41:04,482 - INFO - 需要裁剪 0.88秒
2025-08-07 17:41:04,482 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-08-07 17:41:04,482 - INFO - 裁剪场景ID=1521：从1.68秒裁剪至1.00秒
2025-08-07 17:41:04,482 - INFO - 裁剪场景ID=1513：从1.60秒裁剪至1.40秒
2025-08-07 17:41:04,482 - INFO - 调整后总时长: 4.60秒，与目标时长差异: 0.00秒
2025-08-07 17:41:04,482 - INFO - 方案 #1 调整/填充后最终总时长: 4.60秒
2025-08-07 17:41:04,482 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:04,482 - INFO - ========== 当前模式：字幕 #36 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:04,482 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:04,482 - INFO - ========== 新模式：字幕 #36 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:04,482 - INFO - 
----- 处理字幕 #36 的方案 #1 -----
2025-08-07 17:41:04,482 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\36_1.mp4
2025-08-07 17:41:04,482 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6jc6vits
2025-08-07 17:41:04,483 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1513.mp4 (确认存在: True)
2025-08-07 17:41:04,483 - INFO - 添加场景ID=1513，时长=1.60秒，累计时长=1.60秒
2025-08-07 17:41:04,483 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1514.mp4 (确认存在: True)
2025-08-07 17:41:04,483 - INFO - 添加场景ID=1514，时长=2.08秒，累计时长=3.68秒
2025-08-07 17:41:04,483 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1515.mp4 (确认存在: True)
2025-08-07 17:41:04,483 - INFO - 添加场景ID=1515，时长=1.44秒，累计时长=5.12秒
2025-08-07 17:41:04,483 - INFO - 准备合并 3 个场景文件，总时长约 5.12秒
2025-08-07 17:41:04,483 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1513.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1514.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1515.mp4'

2025-08-07 17:41:04,483 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp6jc6vits\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp6jc6vits\temp_combined.mp4
2025-08-07 17:41:04,606 - INFO - 合并后的视频时长: 5.19秒，目标音频时长: 4.60秒
2025-08-07 17:41:04,606 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp6jc6vits\temp_combined.mp4 -ss 0 -to 4.597 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\36_1.mp4
2025-08-07 17:41:04,896 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:04,896 - INFO - 目标音频时长: 4.60秒
2025-08-07 17:41:04,896 - INFO - 实际视频时长: 4.62秒
2025-08-07 17:41:04,896 - INFO - 时长差异: 0.03秒 (0.57%)
2025-08-07 17:41:04,896 - INFO - ==========================================
2025-08-07 17:41:04,896 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:04,896 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\36_1.mp4
2025-08-07 17:41:04,896 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6jc6vits
2025-08-07 17:41:04,940 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:04,940 - INFO -   - 音频时长: 4.60秒
2025-08-07 17:41:04,940 - INFO -   - 视频时长: 4.62秒
2025-08-07 17:41:04,940 - INFO -   - 时长差异: 0.03秒 (0.57%)
2025-08-07 17:41:04,940 - INFO - 
----- 处理字幕 #36 的方案 #2 -----
2025-08-07 17:41:04,940 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\36_2.mp4
2025-08-07 17:41:04,941 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpe_ts1_4s
2025-08-07 17:41:04,941 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1521.mp4 (确认存在: True)
2025-08-07 17:41:04,941 - INFO - 添加场景ID=1521，时长=1.68秒，累计时长=1.68秒
2025-08-07 17:41:04,941 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1522.mp4 (确认存在: True)
2025-08-07 17:41:04,941 - INFO - 添加场景ID=1522，时长=0.92秒，累计时长=2.60秒
2025-08-07 17:41:04,941 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1523.mp4 (确认存在: True)
2025-08-07 17:41:04,941 - INFO - 添加场景ID=1523，时长=1.28秒，累计时长=3.88秒
2025-08-07 17:41:04,941 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1524.mp4 (确认存在: True)
2025-08-07 17:41:04,941 - INFO - 添加场景ID=1524，时长=1.16秒，累计时长=5.04秒
2025-08-07 17:41:04,941 - INFO - 准备合并 4 个场景文件，总时长约 5.04秒
2025-08-07 17:41:04,941 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1521.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1522.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1523.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1524.mp4'

2025-08-07 17:41:04,942 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpe_ts1_4s\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpe_ts1_4s\temp_combined.mp4
2025-08-07 17:41:05,109 - INFO - 合并后的视频时长: 5.13秒，目标音频时长: 4.60秒
2025-08-07 17:41:05,109 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpe_ts1_4s\temp_combined.mp4 -ss 0 -to 4.597 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\36_2.mp4
2025-08-07 17:41:05,421 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:05,421 - INFO - 目标音频时长: 4.60秒
2025-08-07 17:41:05,421 - INFO - 实际视频时长: 4.62秒
2025-08-07 17:41:05,421 - INFO - 时长差异: 0.03秒 (0.57%)
2025-08-07 17:41:05,421 - INFO - ==========================================
2025-08-07 17:41:05,421 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:05,421 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\36_2.mp4
2025-08-07 17:41:05,422 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpe_ts1_4s
2025-08-07 17:41:05,466 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:05,466 - INFO -   - 音频时长: 4.60秒
2025-08-07 17:41:05,466 - INFO -   - 视频时长: 4.62秒
2025-08-07 17:41:05,466 - INFO -   - 时长差异: 0.03秒 (0.57%)
2025-08-07 17:41:05,466 - INFO - 
----- 处理字幕 #36 的方案 #3 -----
2025-08-07 17:41:05,466 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\36_3.mp4
2025-08-07 17:41:05,467 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp3ssenj9b
2025-08-07 17:41:05,467 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1513.mp4 (确认存在: True)
2025-08-07 17:41:05,467 - INFO - 添加场景ID=1513，时长=1.60秒，累计时长=1.60秒
2025-08-07 17:41:05,467 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1521.mp4 (确认存在: True)
2025-08-07 17:41:05,467 - INFO - 添加场景ID=1521，时长=1.68秒，累计时长=3.28秒
2025-08-07 17:41:05,467 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1523.mp4 (确认存在: True)
2025-08-07 17:41:05,467 - INFO - 添加场景ID=1523，时长=1.28秒，累计时长=4.56秒
2025-08-07 17:41:05,467 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1522.mp4 (确认存在: True)
2025-08-07 17:41:05,467 - INFO - 添加场景ID=1522，时长=0.92秒，累计时长=5.48秒
2025-08-07 17:41:05,468 - INFO - 准备合并 4 个场景文件，总时长约 5.48秒
2025-08-07 17:41:05,468 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1513.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1521.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1523.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1522.mp4'

2025-08-07 17:41:05,468 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp3ssenj9b\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp3ssenj9b\temp_combined.mp4
2025-08-07 17:41:05,628 - INFO - 合并后的视频时长: 5.57秒，目标音频时长: 4.60秒
2025-08-07 17:41:05,628 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp3ssenj9b\temp_combined.mp4 -ss 0 -to 4.597 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\36_3.mp4
2025-08-07 17:41:05,923 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:05,923 - INFO - 目标音频时长: 4.60秒
2025-08-07 17:41:05,923 - INFO - 实际视频时长: 4.62秒
2025-08-07 17:41:05,923 - INFO - 时长差异: 0.03秒 (0.57%)
2025-08-07 17:41:05,923 - INFO - ==========================================
2025-08-07 17:41:05,923 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:05,923 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\36_3.mp4
2025-08-07 17:41:05,923 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp3ssenj9b
2025-08-07 17:41:05,967 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:05,967 - INFO -   - 音频时长: 4.60秒
2025-08-07 17:41:05,967 - INFO -   - 视频时长: 4.62秒
2025-08-07 17:41:05,967 - INFO -   - 时长差异: 0.03秒 (0.57%)
2025-08-07 17:41:05,967 - INFO - 
字幕 #36 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:05,967 - INFO - 生成的视频文件:
2025-08-07 17:41:05,967 - INFO -   1. F:/github/aicut_auto/newcut_ai\36_1.mp4
2025-08-07 17:41:05,967 - INFO -   2. F:/github/aicut_auto/newcut_ai\36_2.mp4
2025-08-07 17:41:05,967 - INFO -   3. F:/github/aicut_auto/newcut_ai\36_3.mp4
2025-08-07 17:41:05,967 - INFO - ========== 字幕 #36 处理结束 ==========

