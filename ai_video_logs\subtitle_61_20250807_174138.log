2025-08-07 17:41:38,997 - INFO - ========== 字幕 #61 处理开始 ==========
2025-08-07 17:41:38,997 - INFO - 字幕内容: 男人被人从二楼推下，身受重伤，女孩立刻上前施救。
2025-08-07 17:41:38,997 - INFO - 字幕序号: [2896, 2900]
2025-08-07 17:41:38,997 - INFO - 音频文件详情:
2025-08-07 17:41:38,997 - INFO -   - 路径: output\61.wav
2025-08-07 17:41:38,997 - INFO -   - 时长: 3.94秒
2025-08-07 17:41:38,998 - INFO -   - 验证音频时长: 3.94秒
2025-08-07 17:41:38,998 - INFO - 字幕时间戳信息:
2025-08-07 17:41:38,998 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:38,998 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:38,998 - INFO -   - 根据生成的音频时长(3.94秒)已调整字幕时间戳
2025-08-07 17:41:38,998 - INFO - ========== 新模式：为字幕 #61 生成4套场景方案 ==========
2025-08-07 17:41:38,998 - INFO - 字幕序号列表: [2896, 2900]
2025-08-07 17:41:38,998 - INFO - 
--- 生成方案 #1：基于字幕序号 #2896 ---
2025-08-07 17:41:38,998 - INFO - 开始为单个字幕序号 #2896 匹配场景，目标时长: 3.94秒
2025-08-07 17:41:38,998 - INFO - 开始查找字幕序号 [2896] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:38,999 - INFO - 找到related_overlap场景: scene_id=3121, 字幕#2896
2025-08-07 17:41:38,999 - INFO - 找到related_overlap场景: scene_id=3123, 字幕#2896
2025-08-07 17:41:39,000 - INFO - 找到related_between场景: scene_id=3115, 字幕#2896
2025-08-07 17:41:39,000 - INFO - 找到related_between场景: scene_id=3116, 字幕#2896
2025-08-07 17:41:39,000 - INFO - 找到related_between场景: scene_id=3117, 字幕#2896
2025-08-07 17:41:39,000 - INFO - 找到related_between场景: scene_id=3118, 字幕#2896
2025-08-07 17:41:39,000 - INFO - 找到related_between场景: scene_id=3119, 字幕#2896
2025-08-07 17:41:39,000 - INFO - 找到related_between场景: scene_id=3120, 字幕#2896
2025-08-07 17:41:39,000 - INFO - 字幕 #2896 找到 2 个overlap场景, 6 个between场景
2025-08-07 17:41:39,000 - INFO - 字幕序号 #2896 找到 2 个可用overlap场景, 6 个可用between场景
2025-08-07 17:41:39,000 - INFO - 选择第一个overlap场景作为起点: scene_id=3121
2025-08-07 17:41:39,000 - INFO - 添加起点场景: scene_id=3121, 时长=1.64秒, 累计时长=1.64秒
2025-08-07 17:41:39,000 - INFO - 起点场景时长不足，需要延伸填充 2.30秒
2025-08-07 17:41:39,000 - INFO - 起点场景在原始列表中的索引: 3120
2025-08-07 17:41:39,000 - INFO - 延伸添加场景: scene_id=3122 (完整时长 1.48秒)
2025-08-07 17:41:39,000 - INFO - 累计时长: 3.12秒
2025-08-07 17:41:39,000 - INFO - 延伸添加场景: scene_id=3123 (裁剪至 0.82秒)
2025-08-07 17:41:39,000 - INFO - 累计时长: 3.94秒
2025-08-07 17:41:39,000 - INFO - 字幕序号 #2896 场景匹配完成，共选择 3 个场景，总时长: 3.94秒
2025-08-07 17:41:39,000 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-08-07 17:41:39,000 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-08-07 17:41:39,000 - INFO - 
--- 生成方案 #2：基于字幕序号 #2900 ---
2025-08-07 17:41:39,000 - INFO - 开始为单个字幕序号 #2900 匹配场景，目标时长: 3.94秒
2025-08-07 17:41:39,000 - INFO - 开始查找字幕序号 [2900] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:39,001 - INFO - 找到related_overlap场景: scene_id=3123, 字幕#2900
2025-08-07 17:41:39,001 - INFO - 找到related_between场景: scene_id=3124, 字幕#2900
2025-08-07 17:41:39,001 - INFO - 找到related_between场景: scene_id=3125, 字幕#2900
2025-08-07 17:41:39,001 - INFO - 找到related_between场景: scene_id=3126, 字幕#2900
2025-08-07 17:41:39,002 - INFO - 字幕 #2900 找到 1 个overlap场景, 3 个between场景
2025-08-07 17:41:39,002 - INFO - 字幕序号 #2900 找到 0 个可用overlap场景, 3 个可用between场景
2025-08-07 17:41:39,002 - INFO - 没有overlap场景，选择第一个between场景作为起点: scene_id=3124
2025-08-07 17:41:39,002 - INFO - 添加起点场景: scene_id=3124, 时长=0.48秒, 累计时长=0.48秒
2025-08-07 17:41:39,002 - INFO - 起点场景时长不足，需要延伸填充 3.46秒
2025-08-07 17:41:39,002 - INFO - 起点场景在原始列表中的索引: 3123
2025-08-07 17:41:39,002 - INFO - 延伸添加场景: scene_id=3125 (完整时长 0.92秒)
2025-08-07 17:41:39,002 - INFO - 累计时长: 1.40秒
2025-08-07 17:41:39,002 - INFO - 延伸添加场景: scene_id=3126 (完整时长 0.72秒)
2025-08-07 17:41:39,002 - INFO - 累计时长: 2.12秒
2025-08-07 17:41:39,002 - INFO - 延伸添加场景: scene_id=3127 (完整时长 1.32秒)
2025-08-07 17:41:39,002 - INFO - 累计时长: 3.44秒
2025-08-07 17:41:39,002 - INFO - 延伸添加场景: scene_id=3128 (裁剪至 0.50秒)
2025-08-07 17:41:39,002 - INFO - 累计时长: 3.94秒
2025-08-07 17:41:39,002 - INFO - 字幕序号 #2900 场景匹配完成，共选择 5 个场景，总时长: 3.94秒
2025-08-07 17:41:39,002 - INFO - 方案 #2 生成成功，包含 5 个场景
2025-08-07 17:41:39,002 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:39,002 - INFO - ========== 当前模式：为字幕 #61 生成 1 套场景方案 ==========
2025-08-07 17:41:39,002 - INFO - 开始查找字幕序号 [2896, 2900] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:39,003 - INFO - 找到related_overlap场景: scene_id=3121, 字幕#2896
2025-08-07 17:41:39,003 - INFO - 找到related_overlap场景: scene_id=3123, 字幕#2896
2025-08-07 17:41:39,003 - INFO - 找到related_between场景: scene_id=3115, 字幕#2896
2025-08-07 17:41:39,003 - INFO - 找到related_between场景: scene_id=3116, 字幕#2896
2025-08-07 17:41:39,003 - INFO - 找到related_between场景: scene_id=3117, 字幕#2896
2025-08-07 17:41:39,003 - INFO - 找到related_between场景: scene_id=3118, 字幕#2896
2025-08-07 17:41:39,003 - INFO - 找到related_between场景: scene_id=3119, 字幕#2896
2025-08-07 17:41:39,003 - INFO - 找到related_between场景: scene_id=3120, 字幕#2896
2025-08-07 17:41:39,003 - INFO - 找到related_between场景: scene_id=3124, 字幕#2900
2025-08-07 17:41:39,003 - INFO - 找到related_between场景: scene_id=3125, 字幕#2900
2025-08-07 17:41:39,003 - INFO - 找到related_between场景: scene_id=3126, 字幕#2900
2025-08-07 17:41:39,003 - INFO - 字幕 #2896 找到 2 个overlap场景, 6 个between场景
2025-08-07 17:41:39,003 - INFO - 字幕 #2900 找到 0 个overlap场景, 3 个between场景
2025-08-07 17:41:39,003 - INFO - 共收集 2 个未使用的overlap场景和 9 个未使用的between场景
2025-08-07 17:41:39,003 - INFO - 开始生成方案 #1
2025-08-07 17:41:39,003 - INFO - 方案 #1: 为字幕#2896选择初始化overlap场景id=3123
2025-08-07 17:41:39,003 - INFO - 方案 #1: 初始选择后，当前总时长=6.88秒
2025-08-07 17:41:39,003 - INFO - 方案 #1: 为字幕#2900选择初始化between场景id=3126
2025-08-07 17:41:39,003 - INFO - 方案 #1: 额外between选择后，当前总时长=7.60秒
2025-08-07 17:41:39,003 - INFO - 方案 #1: 场景总时长(7.60秒)大于音频时长(3.94秒)，需要裁剪
2025-08-07 17:41:39,003 - INFO - 调整前总时长: 7.60秒, 目标时长: 3.94秒
2025-08-07 17:41:39,003 - INFO - 需要裁剪 3.66秒
2025-08-07 17:41:39,003 - INFO - 裁剪最长场景ID=3123：从6.88秒裁剪至3.22秒
2025-08-07 17:41:39,004 - INFO - 调整后总时长: 3.94秒，与目标时长差异: 0.00秒
2025-08-07 17:41:39,004 - INFO - 方案 #1 调整/填充后最终总时长: 3.94秒
2025-08-07 17:41:39,004 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:39,004 - INFO - ========== 当前模式：字幕 #61 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:39,004 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:39,004 - INFO - ========== 新模式：字幕 #61 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:39,004 - INFO - 
----- 处理字幕 #61 的方案 #1 -----
2025-08-07 17:41:39,004 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\61_1.mp4
2025-08-07 17:41:39,004 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpr9eml7bx
2025-08-07 17:41:39,005 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3121.mp4 (确认存在: True)
2025-08-07 17:41:39,005 - INFO - 添加场景ID=3121，时长=1.64秒，累计时长=1.64秒
2025-08-07 17:41:39,005 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3122.mp4 (确认存在: True)
2025-08-07 17:41:39,005 - INFO - 添加场景ID=3122，时长=1.48秒，累计时长=3.12秒
2025-08-07 17:41:39,005 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3123.mp4 (确认存在: True)
2025-08-07 17:41:39,005 - INFO - 添加场景ID=3123，时长=6.88秒，累计时长=10.00秒
2025-08-07 17:41:39,006 - INFO - 场景总时长(10.00秒)已达到音频时长(3.94秒)的1.5倍，停止添加场景
2025-08-07 17:41:39,006 - INFO - 准备合并 3 个场景文件，总时长约 10.00秒
2025-08-07 17:41:39,006 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3121.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3122.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3123.mp4'

2025-08-07 17:41:39,006 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpr9eml7bx\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpr9eml7bx\temp_combined.mp4
2025-08-07 17:41:39,160 - INFO - 合并后的视频时长: 10.07秒，目标音频时长: 3.94秒
2025-08-07 17:41:39,160 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpr9eml7bx\temp_combined.mp4 -ss 0 -to 3.937 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\61_1.mp4
2025-08-07 17:41:39,476 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:39,476 - INFO - 目标音频时长: 3.94秒
2025-08-07 17:41:39,476 - INFO - 实际视频时长: 3.98秒
2025-08-07 17:41:39,476 - INFO - 时长差异: 0.05秒 (1.17%)
2025-08-07 17:41:39,476 - INFO - ==========================================
2025-08-07 17:41:39,476 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:39,476 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\61_1.mp4
2025-08-07 17:41:39,477 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpr9eml7bx
2025-08-07 17:41:39,520 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:39,520 - INFO -   - 音频时长: 3.94秒
2025-08-07 17:41:39,520 - INFO -   - 视频时长: 3.98秒
2025-08-07 17:41:39,520 - INFO -   - 时长差异: 0.05秒 (1.17%)
2025-08-07 17:41:39,520 - INFO - 
----- 处理字幕 #61 的方案 #2 -----
2025-08-07 17:41:39,520 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\61_2.mp4
2025-08-07 17:41:39,521 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1l4sq024
2025-08-07 17:41:39,521 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3124.mp4 (确认存在: True)
2025-08-07 17:41:39,521 - INFO - 添加场景ID=3124，时长=0.48秒，累计时长=0.48秒
2025-08-07 17:41:39,521 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3125.mp4 (确认存在: True)
2025-08-07 17:41:39,521 - INFO - 添加场景ID=3125，时长=0.92秒，累计时长=1.40秒
2025-08-07 17:41:39,521 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3126.mp4 (确认存在: True)
2025-08-07 17:41:39,521 - INFO - 添加场景ID=3126，时长=0.72秒，累计时长=2.12秒
2025-08-07 17:41:39,521 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3127.mp4 (确认存在: True)
2025-08-07 17:41:39,521 - INFO - 添加场景ID=3127，时长=1.32秒，累计时长=3.44秒
2025-08-07 17:41:39,521 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3128.mp4 (确认存在: True)
2025-08-07 17:41:39,521 - INFO - 添加场景ID=3128，时长=0.84秒，累计时长=4.28秒
2025-08-07 17:41:39,522 - INFO - 准备合并 5 个场景文件，总时长约 4.28秒
2025-08-07 17:41:39,522 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3124.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3125.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3126.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3127.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3128.mp4'

2025-08-07 17:41:39,522 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp1l4sq024\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp1l4sq024\temp_combined.mp4
2025-08-07 17:41:39,677 - INFO - 合并后的视频时长: 4.40秒，目标音频时长: 3.94秒
2025-08-07 17:41:39,678 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp1l4sq024\temp_combined.mp4 -ss 0 -to 3.937 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\61_2.mp4
2025-08-07 17:41:39,947 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:39,947 - INFO - 目标音频时长: 3.94秒
2025-08-07 17:41:39,947 - INFO - 实际视频时长: 3.98秒
2025-08-07 17:41:39,947 - INFO - 时长差异: 0.05秒 (1.17%)
2025-08-07 17:41:39,947 - INFO - ==========================================
2025-08-07 17:41:39,947 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:39,947 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\61_2.mp4
2025-08-07 17:41:39,947 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1l4sq024
2025-08-07 17:41:39,990 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:39,990 - INFO -   - 音频时长: 3.94秒
2025-08-07 17:41:39,990 - INFO -   - 视频时长: 3.98秒
2025-08-07 17:41:39,990 - INFO -   - 时长差异: 0.05秒 (1.17%)
2025-08-07 17:41:39,990 - INFO - 
----- 处理字幕 #61 的方案 #3 -----
2025-08-07 17:41:39,990 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\61_3.mp4
2025-08-07 17:41:39,991 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp3wtukkaa
2025-08-07 17:41:39,991 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3123.mp4 (确认存在: True)
2025-08-07 17:41:39,991 - INFO - 添加场景ID=3123，时长=6.88秒，累计时长=6.88秒
2025-08-07 17:41:39,991 - INFO - 场景总时长(6.88秒)已达到音频时长(3.94秒)的1.5倍，停止添加场景
2025-08-07 17:41:39,991 - INFO - 准备合并 1 个场景文件，总时长约 6.88秒
2025-08-07 17:41:39,991 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3123.mp4'

2025-08-07 17:41:39,991 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp3wtukkaa\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp3wtukkaa\temp_combined.mp4
2025-08-07 17:41:40,098 - INFO - 合并后的视频时长: 6.90秒，目标音频时长: 3.94秒
2025-08-07 17:41:40,098 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp3wtukkaa\temp_combined.mp4 -ss 0 -to 3.937 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\61_3.mp4
2025-08-07 17:41:40,338 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:40,338 - INFO - 目标音频时长: 3.94秒
2025-08-07 17:41:40,338 - INFO - 实际视频时长: 3.98秒
2025-08-07 17:41:40,338 - INFO - 时长差异: 0.05秒 (1.17%)
2025-08-07 17:41:40,338 - INFO - ==========================================
2025-08-07 17:41:40,338 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:40,338 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\61_3.mp4
2025-08-07 17:41:40,339 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp3wtukkaa
2025-08-07 17:41:40,381 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:40,381 - INFO -   - 音频时长: 3.94秒
2025-08-07 17:41:40,381 - INFO -   - 视频时长: 3.98秒
2025-08-07 17:41:40,381 - INFO -   - 时长差异: 0.05秒 (1.17%)
2025-08-07 17:41:40,381 - INFO - 
字幕 #61 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:40,381 - INFO - 生成的视频文件:
2025-08-07 17:41:40,381 - INFO -   1. F:/github/aicut_auto/newcut_ai\61_1.mp4
2025-08-07 17:41:40,381 - INFO -   2. F:/github/aicut_auto/newcut_ai\61_2.mp4
2025-08-07 17:41:40,381 - INFO -   3. F:/github/aicut_auto/newcut_ai\61_3.mp4
2025-08-07 17:41:40,381 - INFO - ========== 字幕 #61 处理结束 ==========

