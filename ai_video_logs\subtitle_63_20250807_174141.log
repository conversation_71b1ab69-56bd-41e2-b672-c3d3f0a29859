2025-08-07 17:41:41,803 - INFO - ========== 字幕 #63 处理开始 ==========
2025-08-07 17:41:41,803 - INFO - 字幕内容: 杀手掏出手枪，以为稳操胜券，却没想到子弹在女孩面前，如同玩物。
2025-08-07 17:41:41,803 - INFO - 字幕序号: [2981, 2983]
2025-08-07 17:41:41,804 - INFO - 音频文件详情:
2025-08-07 17:41:41,804 - INFO -   - 路径: output\63.wav
2025-08-07 17:41:41,804 - INFO -   - 时长: 3.34秒
2025-08-07 17:41:41,804 - INFO -   - 验证音频时长: 3.34秒
2025-08-07 17:41:41,804 - INFO - 字幕时间戳信息:
2025-08-07 17:41:41,804 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:41,804 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:41,804 - INFO -   - 根据生成的音频时长(3.34秒)已调整字幕时间戳
2025-08-07 17:41:41,804 - INFO - ========== 新模式：为字幕 #63 生成4套场景方案 ==========
2025-08-07 17:41:41,804 - INFO - 字幕序号列表: [2981, 2983]
2025-08-07 17:41:41,804 - INFO - 
--- 生成方案 #1：基于字幕序号 #2981 ---
2025-08-07 17:41:41,804 - INFO - 开始为单个字幕序号 #2981 匹配场景，目标时长: 3.34秒
2025-08-07 17:41:41,804 - INFO - 开始查找字幕序号 [2981] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:41,805 - INFO - 找到related_overlap场景: scene_id=3247, 字幕#2981
2025-08-07 17:41:41,805 - INFO - 找到related_overlap场景: scene_id=3248, 字幕#2981
2025-08-07 17:41:41,805 - INFO - 字幕 #2981 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:41,805 - INFO - 字幕序号 #2981 找到 2 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:41,805 - INFO - 选择第一个overlap场景作为起点: scene_id=3247
2025-08-07 17:41:41,805 - INFO - 添加起点场景: scene_id=3247, 时长=0.76秒, 累计时长=0.76秒
2025-08-07 17:41:41,805 - INFO - 起点场景时长不足，需要延伸填充 2.58秒
2025-08-07 17:41:41,805 - INFO - 起点场景在原始列表中的索引: 3246
2025-08-07 17:41:41,805 - INFO - 延伸添加场景: scene_id=3248 (完整时长 1.48秒)
2025-08-07 17:41:41,805 - INFO - 累计时长: 2.24秒
2025-08-07 17:41:41,805 - INFO - 延伸添加场景: scene_id=3249 (裁剪至 1.10秒)
2025-08-07 17:41:41,805 - INFO - 累计时长: 3.34秒
2025-08-07 17:41:41,805 - INFO - 字幕序号 #2981 场景匹配完成，共选择 3 个场景，总时长: 3.34秒
2025-08-07 17:41:41,805 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-08-07 17:41:41,805 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-08-07 17:41:41,805 - INFO - 
--- 生成方案 #2：基于字幕序号 #2983 ---
2025-08-07 17:41:41,805 - INFO - 开始为单个字幕序号 #2983 匹配场景，目标时长: 3.34秒
2025-08-07 17:41:41,805 - INFO - 开始查找字幕序号 [2983] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:41,806 - INFO - 找到related_overlap场景: scene_id=3253, 字幕#2983
2025-08-07 17:41:41,806 - INFO - 找到related_between场景: scene_id=3250, 字幕#2983
2025-08-07 17:41:41,806 - INFO - 找到related_between场景: scene_id=3251, 字幕#2983
2025-08-07 17:41:41,806 - INFO - 找到related_between场景: scene_id=3252, 字幕#2983
2025-08-07 17:41:41,806 - INFO - 字幕 #2983 找到 1 个overlap场景, 3 个between场景
2025-08-07 17:41:41,806 - INFO - 字幕序号 #2983 找到 1 个可用overlap场景, 3 个可用between场景
2025-08-07 17:41:41,806 - INFO - 选择第一个overlap场景作为起点: scene_id=3253
2025-08-07 17:41:41,806 - INFO - 添加起点场景: scene_id=3253, 时长=2.44秒, 累计时长=2.44秒
2025-08-07 17:41:41,806 - INFO - 起点场景时长不足，需要延伸填充 0.90秒
2025-08-07 17:41:41,806 - INFO - 起点场景在原始列表中的索引: 3252
2025-08-07 17:41:41,806 - INFO - 延伸添加场景: scene_id=3254 (裁剪至 0.90秒)
2025-08-07 17:41:41,806 - INFO - 累计时长: 3.34秒
2025-08-07 17:41:41,806 - INFO - 字幕序号 #2983 场景匹配完成，共选择 2 个场景，总时长: 3.34秒
2025-08-07 17:41:41,806 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-08-07 17:41:41,806 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:41,806 - INFO - ========== 当前模式：为字幕 #63 生成 1 套场景方案 ==========
2025-08-07 17:41:41,806 - INFO - 开始查找字幕序号 [2981, 2983] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:41,808 - INFO - 找到related_overlap场景: scene_id=3247, 字幕#2981
2025-08-07 17:41:41,808 - INFO - 找到related_overlap场景: scene_id=3248, 字幕#2981
2025-08-07 17:41:41,808 - INFO - 找到related_overlap场景: scene_id=3253, 字幕#2983
2025-08-07 17:41:41,808 - INFO - 找到related_between场景: scene_id=3250, 字幕#2983
2025-08-07 17:41:41,808 - INFO - 找到related_between场景: scene_id=3251, 字幕#2983
2025-08-07 17:41:41,808 - INFO - 找到related_between场景: scene_id=3252, 字幕#2983
2025-08-07 17:41:41,808 - INFO - 字幕 #2981 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:41,808 - INFO - 字幕 #2983 找到 1 个overlap场景, 3 个between场景
2025-08-07 17:41:41,808 - INFO - 共收集 3 个未使用的overlap场景和 3 个未使用的between场景
2025-08-07 17:41:41,808 - INFO - 开始生成方案 #1
2025-08-07 17:41:41,808 - INFO - 方案 #1: 为字幕#2981选择初始化overlap场景id=3248
2025-08-07 17:41:41,809 - INFO - 方案 #1: 为字幕#2983选择初始化overlap场景id=3253
2025-08-07 17:41:41,809 - INFO - 方案 #1: 初始选择后，当前总时长=3.92秒
2025-08-07 17:41:41,809 - INFO - 方案 #1: 额外between选择后，当前总时长=3.92秒
2025-08-07 17:41:41,809 - INFO - 方案 #1: 场景总时长(3.92秒)大于音频时长(3.34秒)，需要裁剪
2025-08-07 17:41:41,809 - INFO - 调整前总时长: 3.92秒, 目标时长: 3.34秒
2025-08-07 17:41:41,809 - INFO - 需要裁剪 0.58秒
2025-08-07 17:41:41,809 - INFO - 裁剪最长场景ID=3253：从2.44秒裁剪至1.86秒
2025-08-07 17:41:41,809 - INFO - 调整后总时长: 3.34秒，与目标时长差异: 0.00秒
2025-08-07 17:41:41,809 - INFO - 方案 #1 调整/填充后最终总时长: 3.34秒
2025-08-07 17:41:41,809 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:41,809 - INFO - ========== 当前模式：字幕 #63 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:41,809 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:41,809 - INFO - ========== 新模式：字幕 #63 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:41,809 - INFO - 
----- 处理字幕 #63 的方案 #1 -----
2025-08-07 17:41:41,809 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\63_1.mp4
2025-08-07 17:41:41,809 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp7u2v4qk1
2025-08-07 17:41:41,810 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3247.mp4 (确认存在: True)
2025-08-07 17:41:41,810 - INFO - 添加场景ID=3247，时长=0.76秒，累计时长=0.76秒
2025-08-07 17:41:41,810 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3248.mp4 (确认存在: True)
2025-08-07 17:41:41,810 - INFO - 添加场景ID=3248，时长=1.48秒，累计时长=2.24秒
2025-08-07 17:41:41,810 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3249.mp4 (确认存在: True)
2025-08-07 17:41:41,810 - INFO - 添加场景ID=3249，时长=2.04秒，累计时长=4.28秒
2025-08-07 17:41:41,810 - INFO - 准备合并 3 个场景文件，总时长约 4.28秒
2025-08-07 17:41:41,810 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3247.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3248.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3249.mp4'

2025-08-07 17:41:41,810 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp7u2v4qk1\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp7u2v4qk1\temp_combined.mp4
2025-08-07 17:41:41,978 - INFO - 合并后的视频时长: 4.35秒，目标音频时长: 3.34秒
2025-08-07 17:41:41,979 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp7u2v4qk1\temp_combined.mp4 -ss 0 -to 3.343 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\63_1.mp4
2025-08-07 17:41:42,271 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:42,271 - INFO - 目标音频时长: 3.34秒
2025-08-07 17:41:42,271 - INFO - 实际视频时长: 3.38秒
2025-08-07 17:41:42,271 - INFO - 时长差异: 0.04秒 (1.20%)
2025-08-07 17:41:42,271 - INFO - ==========================================
2025-08-07 17:41:42,271 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:42,271 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\63_1.mp4
2025-08-07 17:41:42,272 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp7u2v4qk1
2025-08-07 17:41:42,315 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:42,315 - INFO -   - 音频时长: 3.34秒
2025-08-07 17:41:42,315 - INFO -   - 视频时长: 3.38秒
2025-08-07 17:41:42,315 - INFO -   - 时长差异: 0.04秒 (1.20%)
2025-08-07 17:41:42,315 - INFO - 
----- 处理字幕 #63 的方案 #2 -----
2025-08-07 17:41:42,315 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\63_2.mp4
2025-08-07 17:41:42,316 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmptwhmm6j_
2025-08-07 17:41:42,316 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3253.mp4 (确认存在: True)
2025-08-07 17:41:42,316 - INFO - 添加场景ID=3253，时长=2.44秒，累计时长=2.44秒
2025-08-07 17:41:42,316 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3254.mp4 (确认存在: True)
2025-08-07 17:41:42,316 - INFO - 添加场景ID=3254，时长=2.20秒，累计时长=4.64秒
2025-08-07 17:41:42,316 - INFO - 准备合并 2 个场景文件，总时长约 4.64秒
2025-08-07 17:41:42,316 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3253.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3254.mp4'

2025-08-07 17:41:42,317 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmptwhmm6j_\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmptwhmm6j_\temp_combined.mp4
2025-08-07 17:41:42,443 - INFO - 合并后的视频时长: 4.69秒，目标音频时长: 3.34秒
2025-08-07 17:41:42,443 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmptwhmm6j_\temp_combined.mp4 -ss 0 -to 3.343 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\63_2.mp4
2025-08-07 17:41:42,721 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:42,721 - INFO - 目标音频时长: 3.34秒
2025-08-07 17:41:42,721 - INFO - 实际视频时长: 3.38秒
2025-08-07 17:41:42,721 - INFO - 时长差异: 0.04秒 (1.20%)
2025-08-07 17:41:42,721 - INFO - ==========================================
2025-08-07 17:41:42,721 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:42,721 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\63_2.mp4
2025-08-07 17:41:42,722 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmptwhmm6j_
2025-08-07 17:41:42,767 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:42,767 - INFO -   - 音频时长: 3.34秒
2025-08-07 17:41:42,767 - INFO -   - 视频时长: 3.38秒
2025-08-07 17:41:42,767 - INFO -   - 时长差异: 0.04秒 (1.20%)
2025-08-07 17:41:42,767 - INFO - 
----- 处理字幕 #63 的方案 #3 -----
2025-08-07 17:41:42,767 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\63_3.mp4
2025-08-07 17:41:42,767 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp2g1pg9yb
2025-08-07 17:41:42,767 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3248.mp4 (确认存在: True)
2025-08-07 17:41:42,769 - INFO - 添加场景ID=3248，时长=1.48秒，累计时长=1.48秒
2025-08-07 17:41:42,769 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3253.mp4 (确认存在: True)
2025-08-07 17:41:42,769 - INFO - 添加场景ID=3253，时长=2.44秒，累计时长=3.92秒
2025-08-07 17:41:42,769 - INFO - 准备合并 2 个场景文件，总时长约 3.92秒
2025-08-07 17:41:42,769 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3248.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3253.mp4'

2025-08-07 17:41:42,769 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp2g1pg9yb\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp2g1pg9yb\temp_combined.mp4
2025-08-07 17:41:42,920 - INFO - 合并后的视频时长: 3.97秒，目标音频时长: 3.34秒
2025-08-07 17:41:42,920 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp2g1pg9yb\temp_combined.mp4 -ss 0 -to 3.343 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\63_3.mp4
2025-08-07 17:41:43,194 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:43,194 - INFO - 目标音频时长: 3.34秒
2025-08-07 17:41:43,194 - INFO - 实际视频时长: 3.38秒
2025-08-07 17:41:43,194 - INFO - 时长差异: 0.04秒 (1.20%)
2025-08-07 17:41:43,194 - INFO - ==========================================
2025-08-07 17:41:43,194 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:43,194 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\63_3.mp4
2025-08-07 17:41:43,195 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp2g1pg9yb
2025-08-07 17:41:43,241 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:43,241 - INFO -   - 音频时长: 3.34秒
2025-08-07 17:41:43,241 - INFO -   - 视频时长: 3.38秒
2025-08-07 17:41:43,241 - INFO -   - 时长差异: 0.04秒 (1.20%)
2025-08-07 17:41:43,241 - INFO - 
字幕 #63 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:43,241 - INFO - 生成的视频文件:
2025-08-07 17:41:43,241 - INFO -   1. F:/github/aicut_auto/newcut_ai\63_1.mp4
2025-08-07 17:41:43,241 - INFO -   2. F:/github/aicut_auto/newcut_ai\63_2.mp4
2025-08-07 17:41:43,241 - INFO -   3. F:/github/aicut_auto/newcut_ai\63_3.mp4
2025-08-07 17:41:43,241 - INFO - ========== 字幕 #63 处理结束 ==========

