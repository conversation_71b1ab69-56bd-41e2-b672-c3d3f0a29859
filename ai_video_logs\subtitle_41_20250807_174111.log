2025-08-07 17:41:11,427 - INFO - ========== 字幕 #41 处理开始 ==========
2025-08-07 17:41:11,427 - INFO - 字幕内容: 关键时刻，二弟竟让她假装怀孕，并假意给大哥敬茶道歉，实则在茶中下了剧毒。
2025-08-07 17:41:11,427 - INFO - 字幕序号: [1453, 1472]
2025-08-07 17:41:11,427 - INFO - 音频文件详情:
2025-08-07 17:41:11,427 - INFO -   - 路径: output\41.wav
2025-08-07 17:41:11,427 - INFO -   - 时长: 4.20秒
2025-08-07 17:41:11,427 - INFO -   - 验证音频时长: 4.20秒
2025-08-07 17:41:11,427 - INFO - 字幕时间戳信息:
2025-08-07 17:41:11,427 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:11,427 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:11,427 - INFO -   - 根据生成的音频时长(4.20秒)已调整字幕时间戳
2025-08-07 17:41:11,427 - INFO - ========== 新模式：为字幕 #41 生成4套场景方案 ==========
2025-08-07 17:41:11,427 - INFO - 字幕序号列表: [1453, 1472]
2025-08-07 17:41:11,427 - INFO - 
--- 生成方案 #1：基于字幕序号 #1453 ---
2025-08-07 17:41:11,427 - INFO - 开始为单个字幕序号 #1453 匹配场景，目标时长: 4.20秒
2025-08-07 17:41:11,427 - INFO - 开始查找字幕序号 [1453] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:11,428 - INFO - 找到related_overlap场景: scene_id=1671, 字幕#1453
2025-08-07 17:41:11,429 - INFO - 找到related_between场景: scene_id=1672, 字幕#1453
2025-08-07 17:41:11,429 - INFO - 字幕 #1453 找到 1 个overlap场景, 1 个between场景
2025-08-07 17:41:11,429 - INFO - 字幕序号 #1453 找到 1 个可用overlap场景, 1 个可用between场景
2025-08-07 17:41:11,429 - INFO - 选择第一个overlap场景作为起点: scene_id=1671
2025-08-07 17:41:11,429 - INFO - 添加起点场景: scene_id=1671, 时长=1.56秒, 累计时长=1.56秒
2025-08-07 17:41:11,429 - INFO - 起点场景时长不足，需要延伸填充 2.64秒
2025-08-07 17:41:11,429 - INFO - 起点场景在原始列表中的索引: 1670
2025-08-07 17:41:11,429 - INFO - 延伸添加场景: scene_id=1672 (完整时长 1.88秒)
2025-08-07 17:41:11,429 - INFO - 累计时长: 3.44秒
2025-08-07 17:41:11,429 - INFO - 延伸添加场景: scene_id=1673 (裁剪至 0.76秒)
2025-08-07 17:41:11,429 - INFO - 累计时长: 4.20秒
2025-08-07 17:41:11,429 - INFO - 字幕序号 #1453 场景匹配完成，共选择 3 个场景，总时长: 4.20秒
2025-08-07 17:41:11,429 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-08-07 17:41:11,429 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-08-07 17:41:11,429 - INFO - 
--- 生成方案 #2：基于字幕序号 #1472 ---
2025-08-07 17:41:11,429 - INFO - 开始为单个字幕序号 #1472 匹配场景，目标时长: 4.20秒
2025-08-07 17:41:11,429 - INFO - 开始查找字幕序号 [1472] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:11,430 - INFO - 找到related_overlap场景: scene_id=1695, 字幕#1472
2025-08-07 17:41:11,430 - INFO - 找到related_between场景: scene_id=1696, 字幕#1472
2025-08-07 17:41:11,430 - INFO - 找到related_between场景: scene_id=1697, 字幕#1472
2025-08-07 17:41:11,430 - INFO - 找到related_between场景: scene_id=1698, 字幕#1472
2025-08-07 17:41:11,430 - INFO - 找到related_between场景: scene_id=1699, 字幕#1472
2025-08-07 17:41:11,430 - INFO - 字幕 #1472 找到 1 个overlap场景, 4 个between场景
2025-08-07 17:41:11,430 - INFO - 字幕序号 #1472 找到 1 个可用overlap场景, 4 个可用between场景
2025-08-07 17:41:11,430 - INFO - 选择第一个overlap场景作为起点: scene_id=1695
2025-08-07 17:41:11,430 - INFO - 添加起点场景: scene_id=1695, 时长=1.76秒, 累计时长=1.76秒
2025-08-07 17:41:11,430 - INFO - 起点场景时长不足，需要延伸填充 2.44秒
2025-08-07 17:41:11,430 - INFO - 起点场景在原始列表中的索引: 1694
2025-08-07 17:41:11,430 - INFO - 延伸添加场景: scene_id=1696 (完整时长 1.48秒)
2025-08-07 17:41:11,430 - INFO - 累计时长: 3.24秒
2025-08-07 17:41:11,430 - INFO - 延伸添加场景: scene_id=1697 (裁剪至 0.96秒)
2025-08-07 17:41:11,430 - INFO - 累计时长: 4.20秒
2025-08-07 17:41:11,430 - INFO - 字幕序号 #1472 场景匹配完成，共选择 3 个场景，总时长: 4.20秒
2025-08-07 17:41:11,430 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-08-07 17:41:11,430 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:11,430 - INFO - ========== 当前模式：为字幕 #41 生成 1 套场景方案 ==========
2025-08-07 17:41:11,430 - INFO - 开始查找字幕序号 [1453, 1472] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:11,430 - INFO - 找到related_overlap场景: scene_id=1671, 字幕#1453
2025-08-07 17:41:11,432 - INFO - 找到related_overlap场景: scene_id=1695, 字幕#1472
2025-08-07 17:41:11,432 - INFO - 找到related_between场景: scene_id=1672, 字幕#1453
2025-08-07 17:41:11,432 - INFO - 找到related_between场景: scene_id=1696, 字幕#1472
2025-08-07 17:41:11,432 - INFO - 找到related_between场景: scene_id=1697, 字幕#1472
2025-08-07 17:41:11,432 - INFO - 找到related_between场景: scene_id=1698, 字幕#1472
2025-08-07 17:41:11,432 - INFO - 找到related_between场景: scene_id=1699, 字幕#1472
2025-08-07 17:41:11,432 - INFO - 字幕 #1453 找到 1 个overlap场景, 1 个between场景
2025-08-07 17:41:11,432 - INFO - 字幕 #1472 找到 1 个overlap场景, 4 个between场景
2025-08-07 17:41:11,432 - INFO - 共收集 2 个未使用的overlap场景和 5 个未使用的between场景
2025-08-07 17:41:11,432 - INFO - 开始生成方案 #1
2025-08-07 17:41:11,432 - INFO - 方案 #1: 为字幕#1453选择初始化overlap场景id=1671
2025-08-07 17:41:11,432 - INFO - 方案 #1: 为字幕#1472选择初始化overlap场景id=1695
2025-08-07 17:41:11,432 - INFO - 方案 #1: 初始选择后，当前总时长=3.32秒
2025-08-07 17:41:11,432 - INFO - 方案 #1: 额外between选择后，当前总时长=3.32秒
2025-08-07 17:41:11,432 - INFO - 方案 #1: 额外添加between场景id=1697, 当前总时长=7.32秒
2025-08-07 17:41:11,432 - INFO - 方案 #1: 场景总时长(7.32秒)大于音频时长(4.20秒)，需要裁剪
2025-08-07 17:41:11,432 - INFO - 调整前总时长: 7.32秒, 目标时长: 4.20秒
2025-08-07 17:41:11,433 - INFO - 需要裁剪 3.12秒
2025-08-07 17:41:11,433 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-08-07 17:41:11,433 - INFO - 裁剪场景ID=1697：从4.00秒裁剪至1.20秒
2025-08-07 17:41:11,433 - INFO - 裁剪场景ID=1695：从1.76秒裁剪至1.44秒
2025-08-07 17:41:11,433 - INFO - 调整后总时长: 4.20秒，与目标时长差异: 0.00秒
2025-08-07 17:41:11,433 - INFO - 方案 #1 调整/填充后最终总时长: 4.20秒
2025-08-07 17:41:11,433 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:11,433 - INFO - ========== 当前模式：字幕 #41 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:11,433 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:11,433 - INFO - ========== 新模式：字幕 #41 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:11,433 - INFO - 
----- 处理字幕 #41 的方案 #1 -----
2025-08-07 17:41:11,433 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\41_1.mp4
2025-08-07 17:41:11,433 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpfmakm0wd
2025-08-07 17:41:11,434 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1671.mp4 (确认存在: True)
2025-08-07 17:41:11,434 - INFO - 添加场景ID=1671，时长=1.56秒，累计时长=1.56秒
2025-08-07 17:41:11,434 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1672.mp4 (确认存在: True)
2025-08-07 17:41:11,434 - INFO - 添加场景ID=1672，时长=1.88秒，累计时长=3.44秒
2025-08-07 17:41:11,434 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1673.mp4 (确认存在: True)
2025-08-07 17:41:11,434 - INFO - 添加场景ID=1673，时长=1.44秒，累计时长=4.88秒
2025-08-07 17:41:11,434 - INFO - 准备合并 3 个场景文件，总时长约 4.88秒
2025-08-07 17:41:11,434 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1671.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1672.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1673.mp4'

2025-08-07 17:41:11,434 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpfmakm0wd\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpfmakm0wd\temp_combined.mp4
2025-08-07 17:41:11,581 - INFO - 合并后的视频时长: 4.95秒，目标音频时长: 4.20秒
2025-08-07 17:41:11,581 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpfmakm0wd\temp_combined.mp4 -ss 0 -to 4.203 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\41_1.mp4
2025-08-07 17:41:11,892 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:11,892 - INFO - 目标音频时长: 4.20秒
2025-08-07 17:41:11,892 - INFO - 实际视频时长: 4.26秒
2025-08-07 17:41:11,892 - INFO - 时长差异: 0.06秒 (1.43%)
2025-08-07 17:41:11,892 - INFO - ==========================================
2025-08-07 17:41:11,892 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:11,892 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\41_1.mp4
2025-08-07 17:41:11,893 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpfmakm0wd
2025-08-07 17:41:11,937 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:11,937 - INFO -   - 音频时长: 4.20秒
2025-08-07 17:41:11,937 - INFO -   - 视频时长: 4.26秒
2025-08-07 17:41:11,937 - INFO -   - 时长差异: 0.06秒 (1.43%)
2025-08-07 17:41:11,937 - INFO - 
----- 处理字幕 #41 的方案 #2 -----
2025-08-07 17:41:11,937 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\41_2.mp4
2025-08-07 17:41:11,938 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpe30nvlj7
2025-08-07 17:41:11,938 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1695.mp4 (确认存在: True)
2025-08-07 17:41:11,938 - INFO - 添加场景ID=1695，时长=1.76秒，累计时长=1.76秒
2025-08-07 17:41:11,938 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1696.mp4 (确认存在: True)
2025-08-07 17:41:11,938 - INFO - 添加场景ID=1696，时长=1.48秒，累计时长=3.24秒
2025-08-07 17:41:11,938 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1697.mp4 (确认存在: True)
2025-08-07 17:41:11,938 - INFO - 添加场景ID=1697，时长=4.00秒，累计时长=7.24秒
2025-08-07 17:41:11,938 - INFO - 场景总时长(7.24秒)已达到音频时长(4.20秒)的1.5倍，停止添加场景
2025-08-07 17:41:11,938 - INFO - 准备合并 3 个场景文件，总时长约 7.24秒
2025-08-07 17:41:11,938 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1695.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1696.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1697.mp4'

2025-08-07 17:41:11,939 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpe30nvlj7\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpe30nvlj7\temp_combined.mp4
2025-08-07 17:41:12,080 - INFO - 合并后的视频时长: 7.31秒，目标音频时长: 4.20秒
2025-08-07 17:41:12,080 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpe30nvlj7\temp_combined.mp4 -ss 0 -to 4.203 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\41_2.mp4
2025-08-07 17:41:12,374 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:12,374 - INFO - 目标音频时长: 4.20秒
2025-08-07 17:41:12,374 - INFO - 实际视频时长: 4.26秒
2025-08-07 17:41:12,374 - INFO - 时长差异: 0.06秒 (1.43%)
2025-08-07 17:41:12,374 - INFO - ==========================================
2025-08-07 17:41:12,374 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:12,374 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\41_2.mp4
2025-08-07 17:41:12,375 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpe30nvlj7
2025-08-07 17:41:12,418 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:12,418 - INFO -   - 音频时长: 4.20秒
2025-08-07 17:41:12,418 - INFO -   - 视频时长: 4.26秒
2025-08-07 17:41:12,418 - INFO -   - 时长差异: 0.06秒 (1.43%)
2025-08-07 17:41:12,418 - INFO - 
----- 处理字幕 #41 的方案 #3 -----
2025-08-07 17:41:12,418 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\41_3.mp4
2025-08-07 17:41:12,419 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppz_dw3da
2025-08-07 17:41:12,419 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1671.mp4 (确认存在: True)
2025-08-07 17:41:12,419 - INFO - 添加场景ID=1671，时长=1.56秒，累计时长=1.56秒
2025-08-07 17:41:12,419 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1695.mp4 (确认存在: True)
2025-08-07 17:41:12,419 - INFO - 添加场景ID=1695，时长=1.76秒，累计时长=3.32秒
2025-08-07 17:41:12,419 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1697.mp4 (确认存在: True)
2025-08-07 17:41:12,419 - INFO - 添加场景ID=1697，时长=4.00秒，累计时长=7.32秒
2025-08-07 17:41:12,419 - INFO - 场景总时长(7.32秒)已达到音频时长(4.20秒)的1.5倍，停止添加场景
2025-08-07 17:41:12,420 - INFO - 准备合并 3 个场景文件，总时长约 7.32秒
2025-08-07 17:41:12,420 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1671.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1695.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1697.mp4'

2025-08-07 17:41:12,420 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmppz_dw3da\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmppz_dw3da\temp_combined.mp4
2025-08-07 17:41:12,570 - INFO - 合并后的视频时长: 7.39秒，目标音频时长: 4.20秒
2025-08-07 17:41:12,570 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmppz_dw3da\temp_combined.mp4 -ss 0 -to 4.203 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\41_3.mp4
2025-08-07 17:41:12,871 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:12,871 - INFO - 目标音频时长: 4.20秒
2025-08-07 17:41:12,871 - INFO - 实际视频时长: 4.26秒
2025-08-07 17:41:12,871 - INFO - 时长差异: 0.06秒 (1.43%)
2025-08-07 17:41:12,871 - INFO - ==========================================
2025-08-07 17:41:12,871 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:12,871 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\41_3.mp4
2025-08-07 17:41:12,872 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmppz_dw3da
2025-08-07 17:41:12,914 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:12,914 - INFO -   - 音频时长: 4.20秒
2025-08-07 17:41:12,914 - INFO -   - 视频时长: 4.26秒
2025-08-07 17:41:12,914 - INFO -   - 时长差异: 0.06秒 (1.43%)
2025-08-07 17:41:12,914 - INFO - 
字幕 #41 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:12,914 - INFO - 生成的视频文件:
2025-08-07 17:41:12,914 - INFO -   1. F:/github/aicut_auto/newcut_ai\41_1.mp4
2025-08-07 17:41:12,914 - INFO -   2. F:/github/aicut_auto/newcut_ai\41_2.mp4
2025-08-07 17:41:12,914 - INFO -   3. F:/github/aicut_auto/newcut_ai\41_3.mp4
2025-08-07 17:41:12,914 - INFO - ========== 字幕 #41 处理结束 ==========

