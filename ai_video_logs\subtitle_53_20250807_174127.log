2025-08-07 17:41:27,997 - INFO - ========== 字幕 #53 处理开始 ==========
2025-08-07 17:41:27,997 - INFO - 字幕内容: 二弟狗急跳墙，拿出自己的“神药”，声称这才是真正的仙丹，能彻底根治会长的病。
2025-08-07 17:41:27,997 - INFO - 字幕序号: [2419, 2425]
2025-08-07 17:41:27,997 - INFO - 音频文件详情:
2025-08-07 17:41:27,997 - INFO -   - 路径: output\53.wav
2025-08-07 17:41:27,997 - INFO -   - 时长: 4.84秒
2025-08-07 17:41:27,997 - INFO -   - 验证音频时长: 4.84秒
2025-08-07 17:41:27,997 - INFO - 字幕时间戳信息:
2025-08-07 17:41:28,006 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:28,006 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:28,006 - INFO -   - 根据生成的音频时长(4.84秒)已调整字幕时间戳
2025-08-07 17:41:28,007 - INFO - ========== 新模式：为字幕 #53 生成4套场景方案 ==========
2025-08-07 17:41:28,007 - INFO - 字幕序号列表: [2419, 2425]
2025-08-07 17:41:28,007 - INFO - 
--- 生成方案 #1：基于字幕序号 #2419 ---
2025-08-07 17:41:28,007 - INFO - 开始为单个字幕序号 #2419 匹配场景，目标时长: 4.84秒
2025-08-07 17:41:28,007 - INFO - 开始查找字幕序号 [2419] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:28,007 - INFO - 找到related_overlap场景: scene_id=2673, 字幕#2419
2025-08-07 17:41:28,008 - INFO - 找到related_between场景: scene_id=2672, 字幕#2419
2025-08-07 17:41:28,008 - INFO - 字幕 #2419 找到 1 个overlap场景, 1 个between场景
2025-08-07 17:41:28,008 - INFO - 字幕序号 #2419 找到 1 个可用overlap场景, 1 个可用between场景
2025-08-07 17:41:28,008 - INFO - 选择第一个overlap场景作为起点: scene_id=2673
2025-08-07 17:41:28,008 - INFO - 添加起点场景: scene_id=2673, 时长=2.92秒, 累计时长=2.92秒
2025-08-07 17:41:28,008 - INFO - 起点场景时长不足，需要延伸填充 1.92秒
2025-08-07 17:41:28,008 - INFO - 起点场景在原始列表中的索引: 2672
2025-08-07 17:41:28,008 - INFO - 延伸添加场景: scene_id=2674 (完整时长 1.32秒)
2025-08-07 17:41:28,008 - INFO - 累计时长: 4.24秒
2025-08-07 17:41:28,008 - INFO - 延伸添加场景: scene_id=2675 (裁剪至 0.61秒)
2025-08-07 17:41:28,008 - INFO - 累计时长: 4.84秒
2025-08-07 17:41:28,008 - INFO - 字幕序号 #2419 场景匹配完成，共选择 3 个场景，总时长: 4.84秒
2025-08-07 17:41:28,009 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-08-07 17:41:28,009 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-08-07 17:41:28,009 - INFO - 
--- 生成方案 #2：基于字幕序号 #2425 ---
2025-08-07 17:41:28,009 - INFO - 开始为单个字幕序号 #2425 匹配场景，目标时长: 4.84秒
2025-08-07 17:41:28,009 - INFO - 开始查找字幕序号 [2425] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:28,009 - INFO - 找到related_overlap场景: scene_id=2676, 字幕#2425
2025-08-07 17:41:28,010 - INFO - 字幕 #2425 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:28,010 - INFO - 字幕序号 #2425 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:28,010 - INFO - 选择第一个overlap场景作为起点: scene_id=2676
2025-08-07 17:41:28,010 - INFO - 添加起点场景: scene_id=2676, 时长=2.56秒, 累计时长=2.56秒
2025-08-07 17:41:28,010 - INFO - 起点场景时长不足，需要延伸填充 2.28秒
2025-08-07 17:41:28,010 - INFO - 起点场景在原始列表中的索引: 2675
2025-08-07 17:41:28,010 - INFO - 延伸添加场景: scene_id=2677 (完整时长 1.48秒)
2025-08-07 17:41:28,010 - INFO - 累计时长: 4.04秒
2025-08-07 17:41:28,010 - INFO - 延伸添加场景: scene_id=2678 (裁剪至 0.81秒)
2025-08-07 17:41:28,010 - INFO - 累计时长: 4.84秒
2025-08-07 17:41:28,010 - INFO - 字幕序号 #2425 场景匹配完成，共选择 3 个场景，总时长: 4.84秒
2025-08-07 17:41:28,010 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-08-07 17:41:28,010 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:28,010 - INFO - ========== 当前模式：为字幕 #53 生成 1 套场景方案 ==========
2025-08-07 17:41:28,010 - INFO - 开始查找字幕序号 [2419, 2425] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:28,011 - INFO - 找到related_overlap场景: scene_id=2673, 字幕#2419
2025-08-07 17:41:28,011 - INFO - 找到related_overlap场景: scene_id=2676, 字幕#2425
2025-08-07 17:41:28,011 - INFO - 找到related_between场景: scene_id=2672, 字幕#2419
2025-08-07 17:41:28,012 - INFO - 字幕 #2419 找到 1 个overlap场景, 1 个between场景
2025-08-07 17:41:28,012 - INFO - 字幕 #2425 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:28,012 - INFO - 共收集 2 个未使用的overlap场景和 1 个未使用的between场景
2025-08-07 17:41:28,012 - INFO - 开始生成方案 #1
2025-08-07 17:41:28,012 - INFO - 方案 #1: 为字幕#2419选择初始化overlap场景id=2673
2025-08-07 17:41:28,012 - INFO - 方案 #1: 为字幕#2425选择初始化overlap场景id=2676
2025-08-07 17:41:28,012 - INFO - 方案 #1: 初始选择后，当前总时长=5.48秒
2025-08-07 17:41:28,012 - INFO - 方案 #1: 额外between选择后，当前总时长=5.48秒
2025-08-07 17:41:28,012 - INFO - 方案 #1: 场景总时长(5.48秒)大于音频时长(4.84秒)，需要裁剪
2025-08-07 17:41:28,012 - INFO - 调整前总时长: 5.48秒, 目标时长: 4.84秒
2025-08-07 17:41:28,012 - INFO - 需要裁剪 0.64秒
2025-08-07 17:41:28,012 - INFO - 裁剪最长场景ID=2673：从2.92秒裁剪至2.28秒
2025-08-07 17:41:28,012 - INFO - 调整后总时长: 4.84秒，与目标时长差异: 0.00秒
2025-08-07 17:41:28,012 - INFO - 方案 #1 调整/填充后最终总时长: 4.84秒
2025-08-07 17:41:28,012 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:28,012 - INFO - ========== 当前模式：字幕 #53 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:28,012 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:28,012 - INFO - ========== 新模式：字幕 #53 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:28,012 - INFO - 
----- 处理字幕 #53 的方案 #1 -----
2025-08-07 17:41:28,012 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\53_1.mp4
2025-08-07 17:41:28,012 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpe6txat72
2025-08-07 17:41:28,013 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2673.mp4 (确认存在: True)
2025-08-07 17:41:28,013 - INFO - 添加场景ID=2673，时长=2.92秒，累计时长=2.92秒
2025-08-07 17:41:28,013 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2674.mp4 (确认存在: True)
2025-08-07 17:41:28,013 - INFO - 添加场景ID=2674，时长=1.32秒，累计时长=4.24秒
2025-08-07 17:41:28,013 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2675.mp4 (确认存在: True)
2025-08-07 17:41:28,013 - INFO - 添加场景ID=2675，时长=2.28秒，累计时长=6.52秒
2025-08-07 17:41:28,013 - INFO - 准备合并 3 个场景文件，总时长约 6.52秒
2025-08-07 17:41:28,013 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2673.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2674.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2675.mp4'

2025-08-07 17:41:28,013 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpe6txat72\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpe6txat72\temp_combined.mp4
2025-08-07 17:41:28,155 - INFO - 合并后的视频时长: 6.59秒，目标音频时长: 4.84秒
2025-08-07 17:41:28,155 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpe6txat72\temp_combined.mp4 -ss 0 -to 4.845 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\53_1.mp4
2025-08-07 17:41:28,424 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:28,424 - INFO - 目标音频时长: 4.84秒
2025-08-07 17:41:28,424 - INFO - 实际视频时长: 4.90秒
2025-08-07 17:41:28,424 - INFO - 时长差异: 0.06秒 (1.20%)
2025-08-07 17:41:28,424 - INFO - ==========================================
2025-08-07 17:41:28,424 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:28,424 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\53_1.mp4
2025-08-07 17:41:28,425 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpe6txat72
2025-08-07 17:41:28,469 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:28,469 - INFO -   - 音频时长: 4.84秒
2025-08-07 17:41:28,469 - INFO -   - 视频时长: 4.90秒
2025-08-07 17:41:28,469 - INFO -   - 时长差异: 0.06秒 (1.20%)
2025-08-07 17:41:28,470 - INFO - 
----- 处理字幕 #53 的方案 #2 -----
2025-08-07 17:41:28,470 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\53_2.mp4
2025-08-07 17:41:28,470 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmps7wcq4ib
2025-08-07 17:41:28,470 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2676.mp4 (确认存在: True)
2025-08-07 17:41:28,470 - INFO - 添加场景ID=2676，时长=2.56秒，累计时长=2.56秒
2025-08-07 17:41:28,471 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2677.mp4 (确认存在: True)
2025-08-07 17:41:28,471 - INFO - 添加场景ID=2677，时长=1.48秒，累计时长=4.04秒
2025-08-07 17:41:28,471 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2678.mp4 (确认存在: True)
2025-08-07 17:41:28,471 - INFO - 添加场景ID=2678，时长=1.72秒，累计时长=5.76秒
2025-08-07 17:41:28,471 - INFO - 准备合并 3 个场景文件，总时长约 5.76秒
2025-08-07 17:41:28,471 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2676.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2677.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2678.mp4'

2025-08-07 17:41:28,471 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmps7wcq4ib\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmps7wcq4ib\temp_combined.mp4
2025-08-07 17:41:28,604 - INFO - 合并后的视频时长: 5.83秒，目标音频时长: 4.84秒
2025-08-07 17:41:28,604 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmps7wcq4ib\temp_combined.mp4 -ss 0 -to 4.845 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\53_2.mp4
2025-08-07 17:41:28,897 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:28,897 - INFO - 目标音频时长: 4.84秒
2025-08-07 17:41:28,897 - INFO - 实际视频时长: 4.90秒
2025-08-07 17:41:28,897 - INFO - 时长差异: 0.06秒 (1.20%)
2025-08-07 17:41:28,897 - INFO - ==========================================
2025-08-07 17:41:28,897 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:28,897 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\53_2.mp4
2025-08-07 17:41:28,898 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmps7wcq4ib
2025-08-07 17:41:28,952 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:28,952 - INFO -   - 音频时长: 4.84秒
2025-08-07 17:41:28,952 - INFO -   - 视频时长: 4.90秒
2025-08-07 17:41:28,952 - INFO -   - 时长差异: 0.06秒 (1.20%)
2025-08-07 17:41:28,952 - INFO - 
----- 处理字幕 #53 的方案 #3 -----
2025-08-07 17:41:28,952 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\53_3.mp4
2025-08-07 17:41:28,952 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpthlez_bk
2025-08-07 17:41:28,952 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2673.mp4 (确认存在: True)
2025-08-07 17:41:28,953 - INFO - 添加场景ID=2673，时长=2.92秒，累计时长=2.92秒
2025-08-07 17:41:28,953 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2676.mp4 (确认存在: True)
2025-08-07 17:41:28,953 - INFO - 添加场景ID=2676，时长=2.56秒，累计时长=5.48秒
2025-08-07 17:41:28,953 - INFO - 准备合并 2 个场景文件，总时长约 5.48秒
2025-08-07 17:41:28,953 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2673.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2676.mp4'

2025-08-07 17:41:28,953 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpthlez_bk\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpthlez_bk\temp_combined.mp4
2025-08-07 17:41:29,073 - INFO - 合并后的视频时长: 5.53秒，目标音频时长: 4.84秒
2025-08-07 17:41:29,073 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpthlez_bk\temp_combined.mp4 -ss 0 -to 4.845 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\53_3.mp4
2025-08-07 17:41:29,340 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:29,340 - INFO - 目标音频时长: 4.84秒
2025-08-07 17:41:29,340 - INFO - 实际视频时长: 4.90秒
2025-08-07 17:41:29,340 - INFO - 时长差异: 0.06秒 (1.20%)
2025-08-07 17:41:29,340 - INFO - ==========================================
2025-08-07 17:41:29,340 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:29,340 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\53_3.mp4
2025-08-07 17:41:29,340 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpthlez_bk
2025-08-07 17:41:29,384 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:29,384 - INFO -   - 音频时长: 4.84秒
2025-08-07 17:41:29,384 - INFO -   - 视频时长: 4.90秒
2025-08-07 17:41:29,384 - INFO -   - 时长差异: 0.06秒 (1.20%)
2025-08-07 17:41:29,384 - INFO - 
字幕 #53 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:29,384 - INFO - 生成的视频文件:
2025-08-07 17:41:29,384 - INFO -   1. F:/github/aicut_auto/newcut_ai\53_1.mp4
2025-08-07 17:41:29,384 - INFO -   2. F:/github/aicut_auto/newcut_ai\53_2.mp4
2025-08-07 17:41:29,384 - INFO -   3. F:/github/aicut_auto/newcut_ai\53_3.mp4
2025-08-07 17:41:29,384 - INFO - ========== 字幕 #53 处理结束 ==========

