2025-08-07 17:40:35,735 - INFO - ========== 字幕 #16 处理开始 ==========
2025-08-07 17:40:35,735 - INFO - 字幕内容: 女孩告知爷爷，家中有阴德亏损之人，需要他帮忙找出，于是爷爷决定大办寿宴，将所有楚家人聚集起来。
2025-08-07 17:40:35,735 - INFO - 字幕序号: [323, 341]
2025-08-07 17:40:35,735 - INFO - 音频文件详情:
2025-08-07 17:40:35,735 - INFO -   - 路径: output\16.wav
2025-08-07 17:40:35,735 - INFO -   - 时长: 7.20秒
2025-08-07 17:40:35,736 - INFO -   - 验证音频时长: 7.20秒
2025-08-07 17:40:35,736 - INFO - 字幕时间戳信息:
2025-08-07 17:40:35,736 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:35,736 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:35,736 - INFO -   - 根据生成的音频时长(7.20秒)已调整字幕时间戳
2025-08-07 17:40:35,736 - INFO - ========== 新模式：为字幕 #16 生成4套场景方案 ==========
2025-08-07 17:40:35,736 - INFO - 字幕序号列表: [323, 341]
2025-08-07 17:40:35,736 - INFO - 
--- 生成方案 #1：基于字幕序号 #323 ---
2025-08-07 17:40:35,736 - INFO - 开始为单个字幕序号 #323 匹配场景，目标时长: 7.20秒
2025-08-07 17:40:35,736 - INFO - 开始查找字幕序号 [323] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:35,736 - INFO - 找到related_overlap场景: scene_id=472, 字幕#323
2025-08-07 17:40:35,736 - INFO - 找到related_overlap场景: scene_id=473, 字幕#323
2025-08-07 17:40:35,737 - INFO - 字幕 #323 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:40:35,737 - INFO - 字幕序号 #323 找到 2 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:35,737 - INFO - 选择第一个overlap场景作为起点: scene_id=472
2025-08-07 17:40:35,737 - INFO - 添加起点场景: scene_id=472, 时长=2.84秒, 累计时长=2.84秒
2025-08-07 17:40:35,737 - INFO - 起点场景时长不足，需要延伸填充 4.36秒
2025-08-07 17:40:35,738 - INFO - 起点场景在原始列表中的索引: 471
2025-08-07 17:40:35,738 - INFO - 延伸添加场景: scene_id=473 (完整时长 2.16秒)
2025-08-07 17:40:35,738 - INFO - 累计时长: 5.00秒
2025-08-07 17:40:35,738 - INFO - 延伸添加场景: scene_id=474 (完整时长 1.96秒)
2025-08-07 17:40:35,738 - INFO - 累计时长: 6.96秒
2025-08-07 17:40:35,738 - INFO - 延伸添加场景: scene_id=475 (裁剪至 0.24秒)
2025-08-07 17:40:35,738 - INFO - 累计时长: 7.20秒
2025-08-07 17:40:35,738 - INFO - 字幕序号 #323 场景匹配完成，共选择 4 个场景，总时长: 7.20秒
2025-08-07 17:40:35,738 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-08-07 17:40:35,738 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-08-07 17:40:35,738 - INFO - 
--- 生成方案 #2：基于字幕序号 #341 ---
2025-08-07 17:40:35,738 - INFO - 开始为单个字幕序号 #341 匹配场景，目标时长: 7.20秒
2025-08-07 17:40:35,738 - INFO - 开始查找字幕序号 [341] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:35,738 - INFO - 找到related_overlap场景: scene_id=487, 字幕#341
2025-08-07 17:40:35,738 - INFO - 找到related_between场景: scene_id=488, 字幕#341
2025-08-07 17:40:35,740 - INFO - 字幕 #341 找到 1 个overlap场景, 1 个between场景
2025-08-07 17:40:35,740 - INFO - 字幕序号 #341 找到 1 个可用overlap场景, 1 个可用between场景
2025-08-07 17:40:35,740 - INFO - 选择第一个overlap场景作为起点: scene_id=487
2025-08-07 17:40:35,740 - INFO - 添加起点场景: scene_id=487, 时长=3.96秒, 累计时长=3.96秒
2025-08-07 17:40:35,740 - INFO - 起点场景时长不足，需要延伸填充 3.24秒
2025-08-07 17:40:35,740 - INFO - 起点场景在原始列表中的索引: 486
2025-08-07 17:40:35,740 - INFO - 延伸添加场景: scene_id=488 (完整时长 0.44秒)
2025-08-07 17:40:35,740 - INFO - 累计时长: 4.40秒
2025-08-07 17:40:35,740 - INFO - 延伸添加场景: scene_id=489 (裁剪至 2.80秒)
2025-08-07 17:40:35,740 - INFO - 累计时长: 7.20秒
2025-08-07 17:40:35,740 - INFO - 字幕序号 #341 场景匹配完成，共选择 3 个场景，总时长: 7.20秒
2025-08-07 17:40:35,740 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-08-07 17:40:35,740 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:35,740 - INFO - ========== 当前模式：为字幕 #16 生成 1 套场景方案 ==========
2025-08-07 17:40:35,740 - INFO - 开始查找字幕序号 [323, 341] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:35,740 - INFO - 找到related_overlap场景: scene_id=472, 字幕#323
2025-08-07 17:40:35,740 - INFO - 找到related_overlap场景: scene_id=473, 字幕#323
2025-08-07 17:40:35,740 - INFO - 找到related_overlap场景: scene_id=487, 字幕#341
2025-08-07 17:40:35,741 - INFO - 找到related_between场景: scene_id=488, 字幕#341
2025-08-07 17:40:35,741 - INFO - 字幕 #323 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:40:35,741 - INFO - 字幕 #341 找到 1 个overlap场景, 1 个between场景
2025-08-07 17:40:35,741 - INFO - 共收集 3 个未使用的overlap场景和 1 个未使用的between场景
2025-08-07 17:40:35,741 - INFO - 开始生成方案 #1
2025-08-07 17:40:35,741 - INFO - 方案 #1: 为字幕#323选择初始化overlap场景id=472
2025-08-07 17:40:35,741 - INFO - 方案 #1: 为字幕#341选择初始化overlap场景id=487
2025-08-07 17:40:35,741 - INFO - 方案 #1: 初始选择后，当前总时长=6.80秒
2025-08-07 17:40:35,741 - INFO - 方案 #1: 额外添加overlap场景id=473, 当前总时长=8.96秒
2025-08-07 17:40:35,741 - INFO - 方案 #1: 额外between选择后，当前总时长=8.96秒
2025-08-07 17:40:35,741 - INFO - 方案 #1: 场景总时长(8.96秒)大于音频时长(7.20秒)，需要裁剪
2025-08-07 17:40:35,741 - INFO - 调整前总时长: 8.96秒, 目标时长: 7.20秒
2025-08-07 17:40:35,741 - INFO - 需要裁剪 1.76秒
2025-08-07 17:40:35,741 - INFO - 裁剪最长场景ID=487：从3.96秒裁剪至2.20秒
2025-08-07 17:40:35,741 - INFO - 调整后总时长: 7.20秒，与目标时长差异: 0.00秒
2025-08-07 17:40:35,741 - INFO - 方案 #1 调整/填充后最终总时长: 7.20秒
2025-08-07 17:40:35,741 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:35,741 - INFO - ========== 当前模式：字幕 #16 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:35,741 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:35,741 - INFO - ========== 新模式：字幕 #16 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:35,741 - INFO - 
----- 处理字幕 #16 的方案 #1 -----
2025-08-07 17:40:35,741 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\16_1.mp4
2025-08-07 17:40:35,741 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplj8g2jzm
2025-08-07 17:40:35,743 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\472.mp4 (确认存在: True)
2025-08-07 17:40:35,743 - INFO - 添加场景ID=472，时长=2.84秒，累计时长=2.84秒
2025-08-07 17:40:35,743 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\473.mp4 (确认存在: True)
2025-08-07 17:40:35,743 - INFO - 添加场景ID=473，时长=2.16秒，累计时长=5.00秒
2025-08-07 17:40:35,743 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\474.mp4 (确认存在: True)
2025-08-07 17:40:35,743 - INFO - 添加场景ID=474，时长=1.96秒，累计时长=6.96秒
2025-08-07 17:40:35,743 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\475.mp4 (确认存在: True)
2025-08-07 17:40:35,743 - INFO - 添加场景ID=475，时长=2.36秒，累计时长=9.32秒
2025-08-07 17:40:35,743 - INFO - 准备合并 4 个场景文件，总时长约 9.32秒
2025-08-07 17:40:35,743 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/472.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/473.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/474.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/475.mp4'

2025-08-07 17:40:35,743 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmplj8g2jzm\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmplj8g2jzm\temp_combined.mp4
2025-08-07 17:40:35,900 - INFO - 合并后的视频时长: 9.41秒，目标音频时长: 7.20秒
2025-08-07 17:40:35,900 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmplj8g2jzm\temp_combined.mp4 -ss 0 -to 7.2 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\16_1.mp4
2025-08-07 17:40:36,268 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:36,268 - INFO - 目标音频时长: 7.20秒
2025-08-07 17:40:36,268 - INFO - 实际视频时长: 7.22秒
2025-08-07 17:40:36,268 - INFO - 时长差异: 0.02秒 (0.32%)
2025-08-07 17:40:36,268 - INFO - ==========================================
2025-08-07 17:40:36,268 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:36,268 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\16_1.mp4
2025-08-07 17:40:36,269 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplj8g2jzm
2025-08-07 17:40:36,313 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:36,313 - INFO -   - 音频时长: 7.20秒
2025-08-07 17:40:36,313 - INFO -   - 视频时长: 7.22秒
2025-08-07 17:40:36,313 - INFO -   - 时长差异: 0.02秒 (0.32%)
2025-08-07 17:40:36,313 - INFO - 
----- 处理字幕 #16 的方案 #2 -----
2025-08-07 17:40:36,313 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\16_2.mp4
2025-08-07 17:40:36,314 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpfmvg3w6h
2025-08-07 17:40:36,314 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\487.mp4 (确认存在: True)
2025-08-07 17:40:36,314 - INFO - 添加场景ID=487，时长=3.96秒，累计时长=3.96秒
2025-08-07 17:40:36,314 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\488.mp4 (确认存在: True)
2025-08-07 17:40:36,315 - INFO - 添加场景ID=488，时长=0.44秒，累计时长=4.40秒
2025-08-07 17:40:36,315 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\489.mp4 (确认存在: True)
2025-08-07 17:40:36,315 - INFO - 添加场景ID=489，时长=3.40秒，累计时长=7.80秒
2025-08-07 17:40:36,315 - INFO - 准备合并 3 个场景文件，总时长约 7.80秒
2025-08-07 17:40:36,315 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/487.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/488.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/489.mp4'

2025-08-07 17:40:36,315 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpfmvg3w6h\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpfmvg3w6h\temp_combined.mp4
2025-08-07 17:40:36,446 - INFO - 合并后的视频时长: 7.87秒，目标音频时长: 7.20秒
2025-08-07 17:40:36,446 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpfmvg3w6h\temp_combined.mp4 -ss 0 -to 7.2 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\16_2.mp4
2025-08-07 17:40:36,793 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:36,794 - INFO - 目标音频时长: 7.20秒
2025-08-07 17:40:36,794 - INFO - 实际视频时长: 7.22秒
2025-08-07 17:40:36,794 - INFO - 时长差异: 0.02秒 (0.32%)
2025-08-07 17:40:36,794 - INFO - ==========================================
2025-08-07 17:40:36,794 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:36,794 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\16_2.mp4
2025-08-07 17:40:36,794 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpfmvg3w6h
2025-08-07 17:40:36,837 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:36,838 - INFO -   - 音频时长: 7.20秒
2025-08-07 17:40:36,838 - INFO -   - 视频时长: 7.22秒
2025-08-07 17:40:36,838 - INFO -   - 时长差异: 0.02秒 (0.32%)
2025-08-07 17:40:36,838 - INFO - 
----- 处理字幕 #16 的方案 #3 -----
2025-08-07 17:40:36,838 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\16_3.mp4
2025-08-07 17:40:36,838 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp431va4n6
2025-08-07 17:40:36,839 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\472.mp4 (确认存在: True)
2025-08-07 17:40:36,839 - INFO - 添加场景ID=472，时长=2.84秒，累计时长=2.84秒
2025-08-07 17:40:36,839 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\487.mp4 (确认存在: True)
2025-08-07 17:40:36,839 - INFO - 添加场景ID=487，时长=3.96秒，累计时长=6.80秒
2025-08-07 17:40:36,839 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\473.mp4 (确认存在: True)
2025-08-07 17:40:36,839 - INFO - 添加场景ID=473，时长=2.16秒，累计时长=8.96秒
2025-08-07 17:40:36,839 - INFO - 准备合并 3 个场景文件，总时长约 8.96秒
2025-08-07 17:40:36,839 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/472.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/487.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/473.mp4'

2025-08-07 17:40:36,839 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp431va4n6\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp431va4n6\temp_combined.mp4
2025-08-07 17:40:36,990 - INFO - 合并后的视频时长: 9.03秒，目标音频时长: 7.20秒
2025-08-07 17:40:36,990 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp431va4n6\temp_combined.mp4 -ss 0 -to 7.2 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\16_3.mp4
2025-08-07 17:40:37,346 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:37,346 - INFO - 目标音频时长: 7.20秒
2025-08-07 17:40:37,346 - INFO - 实际视频时长: 7.22秒
2025-08-07 17:40:37,346 - INFO - 时长差异: 0.02秒 (0.32%)
2025-08-07 17:40:37,346 - INFO - ==========================================
2025-08-07 17:40:37,346 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:37,346 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\16_3.mp4
2025-08-07 17:40:37,347 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp431va4n6
2025-08-07 17:40:37,392 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:37,392 - INFO -   - 音频时长: 7.20秒
2025-08-07 17:40:37,392 - INFO -   - 视频时长: 7.22秒
2025-08-07 17:40:37,392 - INFO -   - 时长差异: 0.02秒 (0.32%)
2025-08-07 17:40:37,393 - INFO - 
字幕 #16 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:37,393 - INFO - 生成的视频文件:
2025-08-07 17:40:37,393 - INFO -   1. F:/github/aicut_auto/newcut_ai\16_1.mp4
2025-08-07 17:40:37,393 - INFO -   2. F:/github/aicut_auto/newcut_ai\16_2.mp4
2025-08-07 17:40:37,393 - INFO -   3. F:/github/aicut_auto/newcut_ai\16_3.mp4
2025-08-07 17:40:37,393 - INFO - ========== 字幕 #16 处理结束 ==========

