2025-08-07 17:41:00,459 - INFO - ========== 字幕 #33 处理开始 ==========
2025-08-07 17:41:00,459 - INFO - 字幕内容: 看着被吊在车里瑟瑟发抖的二弟，男人不禁嘲讽，原来是自己的好弟弟，对他如此深仇大恨。
2025-08-07 17:41:00,459 - INFO - 字幕序号: [981, 986]
2025-08-07 17:41:00,459 - INFO - 音频文件详情:
2025-08-07 17:41:00,459 - INFO -   - 路径: output\33.wav
2025-08-07 17:41:00,459 - INFO -   - 时长: 4.35秒
2025-08-07 17:41:00,459 - INFO -   - 验证音频时长: 4.35秒
2025-08-07 17:41:00,459 - INFO - 字幕时间戳信息:
2025-08-07 17:41:00,459 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:00,460 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:00,460 - INFO -   - 根据生成的音频时长(4.35秒)已调整字幕时间戳
2025-08-07 17:41:00,460 - INFO - ========== 新模式：为字幕 #33 生成4套场景方案 ==========
2025-08-07 17:41:00,460 - INFO - 字幕序号列表: [981, 986]
2025-08-07 17:41:00,460 - INFO - 
--- 生成方案 #1：基于字幕序号 #981 ---
2025-08-07 17:41:00,460 - INFO - 开始为单个字幕序号 #981 匹配场景，目标时长: 4.35秒
2025-08-07 17:41:00,460 - INFO - 开始查找字幕序号 [981] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:00,460 - INFO - 找到related_overlap场景: scene_id=1213, 字幕#981
2025-08-07 17:41:00,461 - INFO - 字幕 #981 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:00,461 - INFO - 字幕序号 #981 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:00,461 - INFO - 选择第一个overlap场景作为起点: scene_id=1213
2025-08-07 17:41:00,461 - INFO - 添加起点场景: scene_id=1213, 时长=1.36秒, 累计时长=1.36秒
2025-08-07 17:41:00,461 - INFO - 起点场景时长不足，需要延伸填充 2.99秒
2025-08-07 17:41:00,461 - INFO - 起点场景在原始列表中的索引: 1212
2025-08-07 17:41:00,461 - INFO - 延伸添加场景: scene_id=1214 (完整时长 1.44秒)
2025-08-07 17:41:00,461 - INFO - 累计时长: 2.80秒
2025-08-07 17:41:00,461 - INFO - 延伸添加场景: scene_id=1215 (完整时长 1.28秒)
2025-08-07 17:41:00,461 - INFO - 累计时长: 4.08秒
2025-08-07 17:41:00,461 - INFO - 延伸添加场景: scene_id=1216 (裁剪至 0.27秒)
2025-08-07 17:41:00,461 - INFO - 累计时长: 4.35秒
2025-08-07 17:41:00,461 - INFO - 字幕序号 #981 场景匹配完成，共选择 4 个场景，总时长: 4.35秒
2025-08-07 17:41:00,461 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-08-07 17:41:00,461 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-08-07 17:41:00,461 - INFO - 
--- 生成方案 #2：基于字幕序号 #986 ---
2025-08-07 17:41:00,461 - INFO - 开始为单个字幕序号 #986 匹配场景，目标时长: 4.35秒
2025-08-07 17:41:00,461 - INFO - 开始查找字幕序号 [986] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:00,462 - INFO - 找到related_overlap场景: scene_id=1217, 字幕#986
2025-08-07 17:41:00,462 - INFO - 找到related_between场景: scene_id=1218, 字幕#986
2025-08-07 17:41:00,462 - INFO - 字幕 #986 找到 1 个overlap场景, 1 个between场景
2025-08-07 17:41:00,462 - INFO - 字幕序号 #986 找到 1 个可用overlap场景, 1 个可用between场景
2025-08-07 17:41:00,462 - INFO - 选择第一个overlap场景作为起点: scene_id=1217
2025-08-07 17:41:00,462 - INFO - 添加起点场景: scene_id=1217, 时长=1.04秒, 累计时长=1.04秒
2025-08-07 17:41:00,462 - INFO - 起点场景时长不足，需要延伸填充 3.31秒
2025-08-07 17:41:00,462 - INFO - 起点场景在原始列表中的索引: 1216
2025-08-07 17:41:00,462 - INFO - 延伸添加场景: scene_id=1218 (完整时长 1.16秒)
2025-08-07 17:41:00,463 - INFO - 累计时长: 2.20秒
2025-08-07 17:41:00,463 - INFO - 延伸添加场景: scene_id=1219 (完整时长 1.36秒)
2025-08-07 17:41:00,463 - INFO - 累计时长: 3.56秒
2025-08-07 17:41:00,463 - INFO - 延伸添加场景: scene_id=1220 (裁剪至 0.79秒)
2025-08-07 17:41:00,463 - INFO - 累计时长: 4.35秒
2025-08-07 17:41:00,463 - INFO - 字幕序号 #986 场景匹配完成，共选择 4 个场景，总时长: 4.35秒
2025-08-07 17:41:00,463 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-08-07 17:41:00,463 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:00,463 - INFO - ========== 当前模式：为字幕 #33 生成 1 套场景方案 ==========
2025-08-07 17:41:00,463 - INFO - 开始查找字幕序号 [981, 986] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:00,463 - INFO - 找到related_overlap场景: scene_id=1213, 字幕#981
2025-08-07 17:41:00,463 - INFO - 找到related_overlap场景: scene_id=1217, 字幕#986
2025-08-07 17:41:00,463 - INFO - 找到related_between场景: scene_id=1218, 字幕#986
2025-08-07 17:41:00,464 - INFO - 字幕 #981 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:00,464 - INFO - 字幕 #986 找到 1 个overlap场景, 1 个between场景
2025-08-07 17:41:00,464 - INFO - 共收集 2 个未使用的overlap场景和 1 个未使用的between场景
2025-08-07 17:41:00,464 - INFO - 开始生成方案 #1
2025-08-07 17:41:00,464 - INFO - 方案 #1: 为字幕#981选择初始化overlap场景id=1213
2025-08-07 17:41:00,464 - INFO - 方案 #1: 为字幕#986选择初始化overlap场景id=1217
2025-08-07 17:41:00,464 - INFO - 方案 #1: 初始选择后，当前总时长=2.40秒
2025-08-07 17:41:00,464 - INFO - 方案 #1: 额外between选择后，当前总时长=2.40秒
2025-08-07 17:41:00,464 - INFO - 方案 #1: 额外添加between场景id=1218, 当前总时长=3.56秒
2025-08-07 17:41:00,464 - INFO - 方案 #1: 场景总时长(3.56秒)小于音频时长(4.35秒)，需要延伸填充
2025-08-07 17:41:00,464 - INFO - 方案 #1: 最后一个场景ID: 1218
2025-08-07 17:41:00,464 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 1217
2025-08-07 17:41:00,464 - INFO - 方案 #1: 需要填充时长: 0.79秒
2025-08-07 17:41:00,464 - INFO - 方案 #1: 追加场景 scene_id=1219 (裁剪至 0.79秒)
2025-08-07 17:41:00,464 - INFO - 方案 #1: 成功填充至目标时长
2025-08-07 17:41:00,464 - INFO - 方案 #1 调整/填充后最终总时长: 4.35秒
2025-08-07 17:41:00,464 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:00,464 - INFO - ========== 当前模式：字幕 #33 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:00,464 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:00,464 - INFO - ========== 新模式：字幕 #33 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:00,464 - INFO - 
----- 处理字幕 #33 的方案 #1 -----
2025-08-07 17:41:00,464 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\33_1.mp4
2025-08-07 17:41:00,464 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpapjjy37w
2025-08-07 17:41:00,466 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1213.mp4 (确认存在: True)
2025-08-07 17:41:00,466 - INFO - 添加场景ID=1213，时长=1.36秒，累计时长=1.36秒
2025-08-07 17:41:00,466 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1214.mp4 (确认存在: True)
2025-08-07 17:41:00,466 - INFO - 添加场景ID=1214，时长=1.44秒，累计时长=2.80秒
2025-08-07 17:41:00,466 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1215.mp4 (确认存在: True)
2025-08-07 17:41:00,466 - INFO - 添加场景ID=1215，时长=1.28秒，累计时长=4.08秒
2025-08-07 17:41:00,466 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1216.mp4 (确认存在: True)
2025-08-07 17:41:00,466 - INFO - 添加场景ID=1216，时长=1.44秒，累计时长=5.52秒
2025-08-07 17:41:00,466 - INFO - 准备合并 4 个场景文件，总时长约 5.52秒
2025-08-07 17:41:00,466 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1213.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1214.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1215.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1216.mp4'

2025-08-07 17:41:00,466 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpapjjy37w\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpapjjy37w\temp_combined.mp4
2025-08-07 17:41:00,626 - INFO - 合并后的视频时长: 5.61秒，目标音频时长: 4.35秒
2025-08-07 17:41:00,626 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpapjjy37w\temp_combined.mp4 -ss 0 -to 4.349 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\33_1.mp4
2025-08-07 17:41:00,918 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:00,919 - INFO - 目标音频时长: 4.35秒
2025-08-07 17:41:00,919 - INFO - 实际视频时长: 4.38秒
2025-08-07 17:41:00,919 - INFO - 时长差异: 0.03秒 (0.78%)
2025-08-07 17:41:00,919 - INFO - ==========================================
2025-08-07 17:41:00,919 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:00,919 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\33_1.mp4
2025-08-07 17:41:00,919 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpapjjy37w
2025-08-07 17:41:00,961 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:00,961 - INFO -   - 音频时长: 4.35秒
2025-08-07 17:41:00,961 - INFO -   - 视频时长: 4.38秒
2025-08-07 17:41:00,961 - INFO -   - 时长差异: 0.03秒 (0.78%)
2025-08-07 17:41:00,961 - INFO - 
----- 处理字幕 #33 的方案 #2 -----
2025-08-07 17:41:00,961 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\33_2.mp4
2025-08-07 17:41:00,962 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsdrfeb9x
2025-08-07 17:41:00,962 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1217.mp4 (确认存在: True)
2025-08-07 17:41:00,962 - INFO - 添加场景ID=1217，时长=1.04秒，累计时长=1.04秒
2025-08-07 17:41:00,962 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1218.mp4 (确认存在: True)
2025-08-07 17:41:00,962 - INFO - 添加场景ID=1218，时长=1.16秒，累计时长=2.20秒
2025-08-07 17:41:00,962 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1219.mp4 (确认存在: True)
2025-08-07 17:41:00,962 - INFO - 添加场景ID=1219，时长=1.36秒，累计时长=3.56秒
2025-08-07 17:41:00,962 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1220.mp4 (确认存在: True)
2025-08-07 17:41:00,962 - INFO - 添加场景ID=1220，时长=1.44秒，累计时长=5.00秒
2025-08-07 17:41:00,963 - INFO - 准备合并 4 个场景文件，总时长约 5.00秒
2025-08-07 17:41:00,963 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1217.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1218.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1219.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1220.mp4'

2025-08-07 17:41:00,963 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpsdrfeb9x\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpsdrfeb9x\temp_combined.mp4
2025-08-07 17:41:01,114 - INFO - 合并后的视频时长: 5.09秒，目标音频时长: 4.35秒
2025-08-07 17:41:01,114 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpsdrfeb9x\temp_combined.mp4 -ss 0 -to 4.349 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\33_2.mp4
2025-08-07 17:41:01,407 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:01,407 - INFO - 目标音频时长: 4.35秒
2025-08-07 17:41:01,407 - INFO - 实际视频时长: 4.38秒
2025-08-07 17:41:01,407 - INFO - 时长差异: 0.03秒 (0.78%)
2025-08-07 17:41:01,407 - INFO - ==========================================
2025-08-07 17:41:01,407 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:01,407 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\33_2.mp4
2025-08-07 17:41:01,407 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsdrfeb9x
2025-08-07 17:41:01,450 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:01,450 - INFO -   - 音频时长: 4.35秒
2025-08-07 17:41:01,450 - INFO -   - 视频时长: 4.38秒
2025-08-07 17:41:01,450 - INFO -   - 时长差异: 0.03秒 (0.78%)
2025-08-07 17:41:01,450 - INFO - 
----- 处理字幕 #33 的方案 #3 -----
2025-08-07 17:41:01,450 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\33_3.mp4
2025-08-07 17:41:01,450 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpzvuq04cn
2025-08-07 17:41:01,451 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1213.mp4 (确认存在: True)
2025-08-07 17:41:01,451 - INFO - 添加场景ID=1213，时长=1.36秒，累计时长=1.36秒
2025-08-07 17:41:01,451 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1217.mp4 (确认存在: True)
2025-08-07 17:41:01,451 - INFO - 添加场景ID=1217，时长=1.04秒，累计时长=2.40秒
2025-08-07 17:41:01,451 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1218.mp4 (确认存在: True)
2025-08-07 17:41:01,451 - INFO - 添加场景ID=1218，时长=1.16秒，累计时长=3.56秒
2025-08-07 17:41:01,451 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1219.mp4 (确认存在: True)
2025-08-07 17:41:01,451 - INFO - 添加场景ID=1219，时长=1.36秒，累计时长=4.92秒
2025-08-07 17:41:01,451 - INFO - 准备合并 4 个场景文件，总时长约 4.92秒
2025-08-07 17:41:01,451 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1213.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1217.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1218.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1219.mp4'

2025-08-07 17:41:01,451 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpzvuq04cn\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpzvuq04cn\temp_combined.mp4
2025-08-07 17:41:01,595 - INFO - 合并后的视频时长: 5.01秒，目标音频时长: 4.35秒
2025-08-07 17:41:01,595 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpzvuq04cn\temp_combined.mp4 -ss 0 -to 4.349 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\33_3.mp4
2025-08-07 17:41:01,873 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:01,873 - INFO - 目标音频时长: 4.35秒
2025-08-07 17:41:01,873 - INFO - 实际视频时长: 4.38秒
2025-08-07 17:41:01,873 - INFO - 时长差异: 0.03秒 (0.78%)
2025-08-07 17:41:01,873 - INFO - ==========================================
2025-08-07 17:41:01,873 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:01,873 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\33_3.mp4
2025-08-07 17:41:01,874 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpzvuq04cn
2025-08-07 17:41:01,917 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:01,917 - INFO -   - 音频时长: 4.35秒
2025-08-07 17:41:01,917 - INFO -   - 视频时长: 4.38秒
2025-08-07 17:41:01,917 - INFO -   - 时长差异: 0.03秒 (0.78%)
2025-08-07 17:41:01,917 - INFO - 
字幕 #33 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:01,917 - INFO - 生成的视频文件:
2025-08-07 17:41:01,917 - INFO -   1. F:/github/aicut_auto/newcut_ai\33_1.mp4
2025-08-07 17:41:01,917 - INFO -   2. F:/github/aicut_auto/newcut_ai\33_2.mp4
2025-08-07 17:41:01,917 - INFO -   3. F:/github/aicut_auto/newcut_ai\33_3.mp4
2025-08-07 17:41:01,917 - INFO - ========== 字幕 #33 处理结束 ==========

