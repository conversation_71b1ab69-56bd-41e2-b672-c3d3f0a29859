2025-08-07 17:41:29,385 - INFO - ========== 字幕 #54 处理开始 ==========
2025-08-07 17:41:29,385 - INFO - 字幕内容: 女孩好言相劝，这药吃下去，神仙难救，可利欲熏心的二弟根本听不进去。
2025-08-07 17:41:29,385 - INFO - 字幕序号: [2468, 2470]
2025-08-07 17:41:29,385 - INFO - 音频文件详情:
2025-08-07 17:41:29,385 - INFO -   - 路径: output\54.wav
2025-08-07 17:41:29,385 - INFO -   - 时长: 3.68秒
2025-08-07 17:41:29,385 - INFO -   - 验证音频时长: 3.68秒
2025-08-07 17:41:29,386 - INFO - 字幕时间戳信息:
2025-08-07 17:41:29,386 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:29,386 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:29,386 - INFO -   - 根据生成的音频时长(3.68秒)已调整字幕时间戳
2025-08-07 17:41:29,386 - INFO - ========== 新模式：为字幕 #54 生成4套场景方案 ==========
2025-08-07 17:41:29,386 - INFO - 字幕序号列表: [2468, 2470]
2025-08-07 17:41:29,386 - INFO - 
--- 生成方案 #1：基于字幕序号 #2468 ---
2025-08-07 17:41:29,386 - INFO - 开始为单个字幕序号 #2468 匹配场景，目标时长: 3.68秒
2025-08-07 17:41:29,386 - INFO - 开始查找字幕序号 [2468] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:29,386 - INFO - 找到related_overlap场景: scene_id=2715, 字幕#2468
2025-08-07 17:41:29,387 - INFO - 字幕 #2468 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:29,387 - INFO - 字幕序号 #2468 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:29,387 - INFO - 选择第一个overlap场景作为起点: scene_id=2715
2025-08-07 17:41:29,387 - INFO - 添加起点场景: scene_id=2715, 时长=2.16秒, 累计时长=2.16秒
2025-08-07 17:41:29,387 - INFO - 起点场景时长不足，需要延伸填充 1.53秒
2025-08-07 17:41:29,388 - INFO - 起点场景在原始列表中的索引: 2714
2025-08-07 17:41:29,388 - INFO - 延伸添加场景: scene_id=2716 (裁剪至 1.53秒)
2025-08-07 17:41:29,388 - INFO - 累计时长: 3.68秒
2025-08-07 17:41:29,388 - INFO - 字幕序号 #2468 场景匹配完成，共选择 2 个场景，总时长: 3.68秒
2025-08-07 17:41:29,388 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-08-07 17:41:29,388 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-08-07 17:41:29,388 - INFO - 
--- 生成方案 #2：基于字幕序号 #2470 ---
2025-08-07 17:41:29,388 - INFO - 开始为单个字幕序号 #2470 匹配场景，目标时长: 3.68秒
2025-08-07 17:41:29,388 - INFO - 开始查找字幕序号 [2470] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:29,388 - INFO - 找到related_overlap场景: scene_id=2717, 字幕#2470
2025-08-07 17:41:29,388 - INFO - 找到related_overlap场景: scene_id=2718, 字幕#2470
2025-08-07 17:41:29,389 - INFO - 找到related_between场景: scene_id=2719, 字幕#2470
2025-08-07 17:41:29,389 - INFO - 找到related_between场景: scene_id=2720, 字幕#2470
2025-08-07 17:41:29,389 - INFO - 找到related_between场景: scene_id=2721, 字幕#2470
2025-08-07 17:41:29,389 - INFO - 找到related_between场景: scene_id=2722, 字幕#2470
2025-08-07 17:41:29,389 - INFO - 字幕 #2470 找到 2 个overlap场景, 4 个between场景
2025-08-07 17:41:29,389 - INFO - 字幕序号 #2470 找到 2 个可用overlap场景, 4 个可用between场景
2025-08-07 17:41:29,389 - INFO - 选择第一个overlap场景作为起点: scene_id=2717
2025-08-07 17:41:29,389 - INFO - 添加起点场景: scene_id=2717, 时长=1.48秒, 累计时长=1.48秒
2025-08-07 17:41:29,389 - INFO - 起点场景时长不足，需要延伸填充 2.20秒
2025-08-07 17:41:29,389 - INFO - 起点场景在原始列表中的索引: 2716
2025-08-07 17:41:29,389 - INFO - 延伸添加场景: scene_id=2718 (完整时长 0.40秒)
2025-08-07 17:41:29,389 - INFO - 累计时长: 1.88秒
2025-08-07 17:41:29,389 - INFO - 延伸添加场景: scene_id=2719 (完整时长 1.00秒)
2025-08-07 17:41:29,389 - INFO - 累计时长: 2.88秒
2025-08-07 17:41:29,389 - INFO - 延伸添加场景: scene_id=2720 (裁剪至 0.81秒)
2025-08-07 17:41:29,389 - INFO - 累计时长: 3.68秒
2025-08-07 17:41:29,389 - INFO - 字幕序号 #2470 场景匹配完成，共选择 4 个场景，总时长: 3.68秒
2025-08-07 17:41:29,389 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-08-07 17:41:29,389 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:29,389 - INFO - ========== 当前模式：为字幕 #54 生成 1 套场景方案 ==========
2025-08-07 17:41:29,389 - INFO - 开始查找字幕序号 [2468, 2470] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:29,390 - INFO - 找到related_overlap场景: scene_id=2715, 字幕#2468
2025-08-07 17:41:29,390 - INFO - 找到related_overlap场景: scene_id=2717, 字幕#2470
2025-08-07 17:41:29,390 - INFO - 找到related_overlap场景: scene_id=2718, 字幕#2470
2025-08-07 17:41:29,391 - INFO - 找到related_between场景: scene_id=2719, 字幕#2470
2025-08-07 17:41:29,391 - INFO - 找到related_between场景: scene_id=2720, 字幕#2470
2025-08-07 17:41:29,391 - INFO - 找到related_between场景: scene_id=2721, 字幕#2470
2025-08-07 17:41:29,391 - INFO - 找到related_between场景: scene_id=2722, 字幕#2470
2025-08-07 17:41:29,391 - INFO - 字幕 #2468 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:29,391 - INFO - 字幕 #2470 找到 2 个overlap场景, 4 个between场景
2025-08-07 17:41:29,391 - INFO - 共收集 3 个未使用的overlap场景和 4 个未使用的between场景
2025-08-07 17:41:29,391 - INFO - 开始生成方案 #1
2025-08-07 17:41:29,391 - INFO - 方案 #1: 为字幕#2468选择初始化overlap场景id=2715
2025-08-07 17:41:29,391 - INFO - 方案 #1: 为字幕#2470选择初始化overlap场景id=2717
2025-08-07 17:41:29,391 - INFO - 方案 #1: 初始选择后，当前总时长=3.64秒
2025-08-07 17:41:29,391 - INFO - 方案 #1: 额外添加overlap场景id=2718, 当前总时长=4.04秒
2025-08-07 17:41:29,391 - INFO - 方案 #1: 额外between选择后，当前总时长=4.04秒
2025-08-07 17:41:29,391 - INFO - 方案 #1: 场景总时长(4.04秒)大于音频时长(3.68秒)，需要裁剪
2025-08-07 17:41:29,391 - INFO - 调整前总时长: 4.04秒, 目标时长: 3.68秒
2025-08-07 17:41:29,391 - INFO - 需要裁剪 0.35秒
2025-08-07 17:41:29,391 - INFO - 裁剪最长场景ID=2715：从2.16秒裁剪至1.80秒
2025-08-07 17:41:29,391 - INFO - 调整后总时长: 3.68秒，与目标时长差异: 0.00秒
2025-08-07 17:41:29,391 - INFO - 方案 #1 调整/填充后最终总时长: 3.68秒
2025-08-07 17:41:29,391 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:29,391 - INFO - ========== 当前模式：字幕 #54 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:29,391 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:29,391 - INFO - ========== 新模式：字幕 #54 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:29,391 - INFO - 
----- 处理字幕 #54 的方案 #1 -----
2025-08-07 17:41:29,391 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\54_1.mp4
2025-08-07 17:41:29,392 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsa_6m036
2025-08-07 17:41:29,392 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2715.mp4 (确认存在: True)
2025-08-07 17:41:29,392 - INFO - 添加场景ID=2715，时长=2.16秒，累计时长=2.16秒
2025-08-07 17:41:29,392 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2716.mp4 (确认存在: True)
2025-08-07 17:41:29,392 - INFO - 添加场景ID=2716，时长=1.76秒，累计时长=3.92秒
2025-08-07 17:41:29,392 - INFO - 准备合并 2 个场景文件，总时长约 3.92秒
2025-08-07 17:41:29,392 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2715.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2716.mp4'

2025-08-07 17:41:29,393 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpsa_6m036\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpsa_6m036\temp_combined.mp4
2025-08-07 17:41:29,503 - INFO - 合并后的视频时长: 3.97秒，目标音频时长: 3.68秒
2025-08-07 17:41:29,504 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpsa_6m036\temp_combined.mp4 -ss 0 -to 3.684 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\54_1.mp4
2025-08-07 17:41:29,761 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:29,761 - INFO - 目标音频时长: 3.68秒
2025-08-07 17:41:29,761 - INFO - 实际视频时长: 3.74秒
2025-08-07 17:41:29,761 - INFO - 时长差异: 0.06秒 (1.60%)
2025-08-07 17:41:29,761 - INFO - ==========================================
2025-08-07 17:41:29,761 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:29,761 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\54_1.mp4
2025-08-07 17:41:29,762 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsa_6m036
2025-08-07 17:41:29,804 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:29,804 - INFO -   - 音频时长: 3.68秒
2025-08-07 17:41:29,804 - INFO -   - 视频时长: 3.74秒
2025-08-07 17:41:29,804 - INFO -   - 时长差异: 0.06秒 (1.60%)
2025-08-07 17:41:29,804 - INFO - 
----- 处理字幕 #54 的方案 #2 -----
2025-08-07 17:41:29,804 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\54_2.mp4
2025-08-07 17:41:29,804 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpr9nbfy34
2025-08-07 17:41:29,805 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2717.mp4 (确认存在: True)
2025-08-07 17:41:29,805 - INFO - 添加场景ID=2717，时长=1.48秒，累计时长=1.48秒
2025-08-07 17:41:29,805 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2718.mp4 (确认存在: True)
2025-08-07 17:41:29,805 - INFO - 添加场景ID=2718，时长=0.40秒，累计时长=1.88秒
2025-08-07 17:41:29,806 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2719.mp4 (确认存在: True)
2025-08-07 17:41:29,806 - INFO - 添加场景ID=2719，时长=1.00秒，累计时长=2.88秒
2025-08-07 17:41:29,806 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2720.mp4 (确认存在: True)
2025-08-07 17:41:29,806 - INFO - 添加场景ID=2720，时长=1.40秒，累计时长=4.28秒
2025-08-07 17:41:29,806 - INFO - 准备合并 4 个场景文件，总时长约 4.28秒
2025-08-07 17:41:29,806 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2717.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2718.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2719.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2720.mp4'

2025-08-07 17:41:29,806 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpr9nbfy34\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpr9nbfy34\temp_combined.mp4
2025-08-07 17:41:29,963 - INFO - 合并后的视频时长: 4.37秒，目标音频时长: 3.68秒
2025-08-07 17:41:29,963 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpr9nbfy34\temp_combined.mp4 -ss 0 -to 3.684 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\54_2.mp4
2025-08-07 17:41:30,235 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:30,236 - INFO - 目标音频时长: 3.68秒
2025-08-07 17:41:30,236 - INFO - 实际视频时长: 3.74秒
2025-08-07 17:41:30,236 - INFO - 时长差异: 0.06秒 (1.60%)
2025-08-07 17:41:30,236 - INFO - ==========================================
2025-08-07 17:41:30,236 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:30,236 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\54_2.mp4
2025-08-07 17:41:30,236 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpr9nbfy34
2025-08-07 17:41:30,281 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:30,281 - INFO -   - 音频时长: 3.68秒
2025-08-07 17:41:30,281 - INFO -   - 视频时长: 3.74秒
2025-08-07 17:41:30,281 - INFO -   - 时长差异: 0.06秒 (1.60%)
2025-08-07 17:41:30,281 - INFO - 
----- 处理字幕 #54 的方案 #3 -----
2025-08-07 17:41:30,281 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\54_3.mp4
2025-08-07 17:41:30,281 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1t8k4tx6
2025-08-07 17:41:30,282 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2715.mp4 (确认存在: True)
2025-08-07 17:41:30,282 - INFO - 添加场景ID=2715，时长=2.16秒，累计时长=2.16秒
2025-08-07 17:41:30,282 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2717.mp4 (确认存在: True)
2025-08-07 17:41:30,282 - INFO - 添加场景ID=2717，时长=1.48秒，累计时长=3.64秒
2025-08-07 17:41:30,282 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2718.mp4 (确认存在: True)
2025-08-07 17:41:30,282 - INFO - 添加场景ID=2718，时长=0.40秒，累计时长=4.04秒
2025-08-07 17:41:30,282 - INFO - 准备合并 3 个场景文件，总时长约 4.04秒
2025-08-07 17:41:30,282 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2715.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2717.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2718.mp4'

2025-08-07 17:41:30,282 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp1t8k4tx6\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp1t8k4tx6\temp_combined.mp4
2025-08-07 17:41:30,423 - INFO - 合并后的视频时长: 4.09秒，目标音频时长: 3.68秒
2025-08-07 17:41:30,423 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp1t8k4tx6\temp_combined.mp4 -ss 0 -to 3.684 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\54_3.mp4
2025-08-07 17:41:30,703 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:30,703 - INFO - 目标音频时长: 3.68秒
2025-08-07 17:41:30,703 - INFO - 实际视频时长: 3.74秒
2025-08-07 17:41:30,703 - INFO - 时长差异: 0.06秒 (1.60%)
2025-08-07 17:41:30,703 - INFO - ==========================================
2025-08-07 17:41:30,703 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:30,703 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\54_3.mp4
2025-08-07 17:41:30,704 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1t8k4tx6
2025-08-07 17:41:30,746 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:30,746 - INFO -   - 音频时长: 3.68秒
2025-08-07 17:41:30,746 - INFO -   - 视频时长: 3.74秒
2025-08-07 17:41:30,747 - INFO -   - 时长差异: 0.06秒 (1.60%)
2025-08-07 17:41:30,747 - INFO - 
字幕 #54 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:30,747 - INFO - 生成的视频文件:
2025-08-07 17:41:30,747 - INFO -   1. F:/github/aicut_auto/newcut_ai\54_1.mp4
2025-08-07 17:41:30,747 - INFO -   2. F:/github/aicut_auto/newcut_ai\54_2.mp4
2025-08-07 17:41:30,747 - INFO -   3. F:/github/aicut_auto/newcut_ai\54_3.mp4
2025-08-07 17:41:30,747 - INFO - ========== 字幕 #54 处理结束 ==========

