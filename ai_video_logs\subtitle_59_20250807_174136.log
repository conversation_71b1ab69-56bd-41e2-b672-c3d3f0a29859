2025-08-07 17:41:36,115 - INFO - ========== 字幕 #59 处理开始 ==========
2025-08-07 17:41:36,115 - INFO - 字幕内容: 然而，在休息室内，二弟正与一个神秘人通话，原来他屡次失败，背后一直有人在指使。
2025-08-07 17:41:36,115 - INFO - 字幕序号: [2860, 2863]
2025-08-07 17:41:36,115 - INFO - 音频文件详情:
2025-08-07 17:41:36,115 - INFO -   - 路径: output\59.wav
2025-08-07 17:41:36,115 - INFO -   - 时长: 4.72秒
2025-08-07 17:41:36,116 - INFO -   - 验证音频时长: 4.72秒
2025-08-07 17:41:36,116 - INFO - 字幕时间戳信息:
2025-08-07 17:41:36,116 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:36,116 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:36,116 - INFO -   - 根据生成的音频时长(4.72秒)已调整字幕时间戳
2025-08-07 17:41:36,116 - INFO - ========== 新模式：为字幕 #59 生成4套场景方案 ==========
2025-08-07 17:41:36,116 - INFO - 字幕序号列表: [2860, 2863]
2025-08-07 17:41:36,116 - INFO - 
--- 生成方案 #1：基于字幕序号 #2860 ---
2025-08-07 17:41:36,116 - INFO - 开始为单个字幕序号 #2860 匹配场景，目标时长: 4.72秒
2025-08-07 17:41:36,116 - INFO - 开始查找字幕序号 [2860] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:36,117 - INFO - 找到related_overlap场景: scene_id=3079, 字幕#2860
2025-08-07 17:41:36,117 - INFO - 找到related_between场景: scene_id=3076, 字幕#2860
2025-08-07 17:41:36,117 - INFO - 找到related_between场景: scene_id=3077, 字幕#2860
2025-08-07 17:41:36,117 - INFO - 找到related_between场景: scene_id=3078, 字幕#2860
2025-08-07 17:41:36,117 - INFO - 找到related_between场景: scene_id=3080, 字幕#2860
2025-08-07 17:41:36,117 - INFO - 找到related_between场景: scene_id=3081, 字幕#2860
2025-08-07 17:41:36,117 - INFO - 找到related_between场景: scene_id=3082, 字幕#2860
2025-08-07 17:41:36,117 - INFO - 字幕 #2860 找到 1 个overlap场景, 6 个between场景
2025-08-07 17:41:36,117 - INFO - 字幕序号 #2860 找到 1 个可用overlap场景, 6 个可用between场景
2025-08-07 17:41:36,117 - INFO - 选择第一个overlap场景作为起点: scene_id=3079
2025-08-07 17:41:36,117 - INFO - 添加起点场景: scene_id=3079, 时长=0.84秒, 累计时长=0.84秒
2025-08-07 17:41:36,117 - INFO - 起点场景时长不足，需要延伸填充 3.88秒
2025-08-07 17:41:36,118 - INFO - 起点场景在原始列表中的索引: 3078
2025-08-07 17:41:36,118 - INFO - 延伸添加场景: scene_id=3080 (完整时长 2.20秒)
2025-08-07 17:41:36,118 - INFO - 累计时长: 3.04秒
2025-08-07 17:41:36,118 - INFO - 延伸添加场景: scene_id=3081 (完整时长 0.68秒)
2025-08-07 17:41:36,118 - INFO - 累计时长: 3.72秒
2025-08-07 17:41:36,118 - INFO - 延伸添加场景: scene_id=3082 (裁剪至 1.00秒)
2025-08-07 17:41:36,118 - INFO - 累计时长: 4.72秒
2025-08-07 17:41:36,118 - INFO - 字幕序号 #2860 场景匹配完成，共选择 4 个场景，总时长: 4.72秒
2025-08-07 17:41:36,118 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-08-07 17:41:36,118 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-08-07 17:41:36,118 - INFO - 
--- 生成方案 #2：基于字幕序号 #2863 ---
2025-08-07 17:41:36,118 - INFO - 开始为单个字幕序号 #2863 匹配场景，目标时长: 4.72秒
2025-08-07 17:41:36,118 - INFO - 开始查找字幕序号 [2863] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:36,118 - INFO - 找到related_overlap场景: scene_id=3083, 字幕#2863
2025-08-07 17:41:36,119 - INFO - 找到related_between场景: scene_id=3084, 字幕#2863
2025-08-07 17:41:36,119 - INFO - 找到related_between场景: scene_id=3085, 字幕#2863
2025-08-07 17:41:36,119 - INFO - 字幕 #2863 找到 1 个overlap场景, 2 个between场景
2025-08-07 17:41:36,119 - INFO - 字幕序号 #2863 找到 1 个可用overlap场景, 2 个可用between场景
2025-08-07 17:41:36,119 - INFO - 选择第一个overlap场景作为起点: scene_id=3083
2025-08-07 17:41:36,119 - INFO - 添加起点场景: scene_id=3083, 时长=6.08秒, 累计时长=6.08秒
2025-08-07 17:41:36,119 - INFO - 起点场景时长已满足要求，无需延伸
2025-08-07 17:41:36,119 - INFO - 方案 #2 生成成功，包含 1 个场景
2025-08-07 17:41:36,119 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:36,119 - INFO - ========== 当前模式：为字幕 #59 生成 1 套场景方案 ==========
2025-08-07 17:41:36,119 - INFO - 开始查找字幕序号 [2860, 2863] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:36,120 - INFO - 找到related_overlap场景: scene_id=3079, 字幕#2860
2025-08-07 17:41:36,120 - INFO - 找到related_overlap场景: scene_id=3083, 字幕#2863
2025-08-07 17:41:36,120 - INFO - 找到related_between场景: scene_id=3076, 字幕#2860
2025-08-07 17:41:36,120 - INFO - 找到related_between场景: scene_id=3077, 字幕#2860
2025-08-07 17:41:36,120 - INFO - 找到related_between场景: scene_id=3078, 字幕#2860
2025-08-07 17:41:36,120 - INFO - 找到related_between场景: scene_id=3080, 字幕#2860
2025-08-07 17:41:36,120 - INFO - 找到related_between场景: scene_id=3081, 字幕#2860
2025-08-07 17:41:36,120 - INFO - 找到related_between场景: scene_id=3082, 字幕#2860
2025-08-07 17:41:36,120 - INFO - 找到related_between场景: scene_id=3084, 字幕#2863
2025-08-07 17:41:36,120 - INFO - 找到related_between场景: scene_id=3085, 字幕#2863
2025-08-07 17:41:36,120 - INFO - 字幕 #2860 找到 1 个overlap场景, 6 个between场景
2025-08-07 17:41:36,120 - INFO - 字幕 #2863 找到 1 个overlap场景, 2 个between场景
2025-08-07 17:41:36,120 - INFO - 共收集 2 个未使用的overlap场景和 8 个未使用的between场景
2025-08-07 17:41:36,120 - INFO - 开始生成方案 #1
2025-08-07 17:41:36,120 - INFO - 方案 #1: 为字幕#2860选择初始化overlap场景id=3079
2025-08-07 17:41:36,120 - INFO - 方案 #1: 为字幕#2863选择初始化overlap场景id=3083
2025-08-07 17:41:36,120 - INFO - 方案 #1: 初始选择后，当前总时长=6.92秒
2025-08-07 17:41:36,120 - INFO - 方案 #1: 额外between选择后，当前总时长=6.92秒
2025-08-07 17:41:36,120 - INFO - 方案 #1: 场景总时长(6.92秒)大于音频时长(4.72秒)，需要裁剪
2025-08-07 17:41:36,120 - INFO - 调整前总时长: 6.92秒, 目标时长: 4.72秒
2025-08-07 17:41:36,120 - INFO - 需要裁剪 2.20秒
2025-08-07 17:41:36,120 - INFO - 裁剪最长场景ID=3083：从6.08秒裁剪至3.88秒
2025-08-07 17:41:36,120 - INFO - 调整后总时长: 4.72秒，与目标时长差异: 0.00秒
2025-08-07 17:41:36,120 - INFO - 方案 #1 调整/填充后最终总时长: 4.72秒
2025-08-07 17:41:36,120 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:36,120 - INFO - ========== 当前模式：字幕 #59 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:36,120 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:36,120 - INFO - ========== 新模式：字幕 #59 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:36,120 - INFO - 
----- 处理字幕 #59 的方案 #1 -----
2025-08-07 17:41:36,121 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\59_1.mp4
2025-08-07 17:41:36,121 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp2roe6lt9
2025-08-07 17:41:36,121 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3079.mp4 (确认存在: True)
2025-08-07 17:41:36,121 - INFO - 添加场景ID=3079，时长=0.84秒，累计时长=0.84秒
2025-08-07 17:41:36,121 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3080.mp4 (确认存在: True)
2025-08-07 17:41:36,121 - INFO - 添加场景ID=3080，时长=2.20秒，累计时长=3.04秒
2025-08-07 17:41:36,121 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3081.mp4 (确认存在: True)
2025-08-07 17:41:36,121 - INFO - 添加场景ID=3081，时长=0.68秒，累计时长=3.72秒
2025-08-07 17:41:36,121 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3082.mp4 (确认存在: True)
2025-08-07 17:41:36,121 - INFO - 添加场景ID=3082，时长=1.92秒，累计时长=5.64秒
2025-08-07 17:41:36,121 - INFO - 准备合并 4 个场景文件，总时长约 5.64秒
2025-08-07 17:41:36,121 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3079.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3080.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3081.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3082.mp4'

2025-08-07 17:41:36,121 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp2roe6lt9\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp2roe6lt9\temp_combined.mp4
2025-08-07 17:41:36,291 - INFO - 合并后的视频时长: 5.73秒，目标音频时长: 4.72秒
2025-08-07 17:41:36,291 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp2roe6lt9\temp_combined.mp4 -ss 0 -to 4.719 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\59_1.mp4
2025-08-07 17:41:36,600 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:36,600 - INFO - 目标音频时长: 4.72秒
2025-08-07 17:41:36,600 - INFO - 实际视频时长: 4.74秒
2025-08-07 17:41:36,600 - INFO - 时长差异: 0.02秒 (0.51%)
2025-08-07 17:41:36,600 - INFO - ==========================================
2025-08-07 17:41:36,600 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:36,601 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\59_1.mp4
2025-08-07 17:41:36,601 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp2roe6lt9
2025-08-07 17:41:36,644 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:36,644 - INFO -   - 音频时长: 4.72秒
2025-08-07 17:41:36,644 - INFO -   - 视频时长: 4.74秒
2025-08-07 17:41:36,644 - INFO -   - 时长差异: 0.02秒 (0.51%)
2025-08-07 17:41:36,644 - INFO - 
----- 处理字幕 #59 的方案 #2 -----
2025-08-07 17:41:36,645 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\59_2.mp4
2025-08-07 17:41:36,645 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkjevf7gb
2025-08-07 17:41:36,645 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3083.mp4 (确认存在: True)
2025-08-07 17:41:36,646 - INFO - 添加场景ID=3083，时长=6.08秒，累计时长=6.08秒
2025-08-07 17:41:36,646 - INFO - 准备合并 1 个场景文件，总时长约 6.08秒
2025-08-07 17:41:36,646 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3083.mp4'

2025-08-07 17:41:36,646 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpkjevf7gb\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpkjevf7gb\temp_combined.mp4
2025-08-07 17:41:36,747 - INFO - 合并后的视频时长: 6.10秒，目标音频时长: 4.72秒
2025-08-07 17:41:36,747 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpkjevf7gb\temp_combined.mp4 -ss 0 -to 4.719 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\59_2.mp4
2025-08-07 17:41:37,021 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:37,021 - INFO - 目标音频时长: 4.72秒
2025-08-07 17:41:37,021 - INFO - 实际视频时长: 4.74秒
2025-08-07 17:41:37,021 - INFO - 时长差异: 0.02秒 (0.51%)
2025-08-07 17:41:37,021 - INFO - ==========================================
2025-08-07 17:41:37,021 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:37,021 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\59_2.mp4
2025-08-07 17:41:37,022 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkjevf7gb
2025-08-07 17:41:37,063 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:37,063 - INFO -   - 音频时长: 4.72秒
2025-08-07 17:41:37,063 - INFO -   - 视频时长: 4.74秒
2025-08-07 17:41:37,063 - INFO -   - 时长差异: 0.02秒 (0.51%)
2025-08-07 17:41:37,063 - INFO - 
----- 处理字幕 #59 的方案 #3 -----
2025-08-07 17:41:37,063 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\59_3.mp4
2025-08-07 17:41:37,063 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxjox8bnl
2025-08-07 17:41:37,063 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3079.mp4 (确认存在: True)
2025-08-07 17:41:37,063 - INFO - 添加场景ID=3079，时长=0.84秒，累计时长=0.84秒
2025-08-07 17:41:37,063 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3083.mp4 (确认存在: True)
2025-08-07 17:41:37,063 - INFO - 添加场景ID=3083，时长=6.08秒，累计时长=6.92秒
2025-08-07 17:41:37,063 - INFO - 准备合并 2 个场景文件，总时长约 6.92秒
2025-08-07 17:41:37,063 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3079.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3083.mp4'

2025-08-07 17:41:37,063 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpxjox8bnl\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpxjox8bnl\temp_combined.mp4
2025-08-07 17:41:37,187 - INFO - 合并后的视频时长: 6.97秒，目标音频时长: 4.72秒
2025-08-07 17:41:37,187 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpxjox8bnl\temp_combined.mp4 -ss 0 -to 4.719 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\59_3.mp4
2025-08-07 17:41:37,459 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:37,459 - INFO - 目标音频时长: 4.72秒
2025-08-07 17:41:37,459 - INFO - 实际视频时长: 4.74秒
2025-08-07 17:41:37,459 - INFO - 时长差异: 0.02秒 (0.51%)
2025-08-07 17:41:37,459 - INFO - ==========================================
2025-08-07 17:41:37,459 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:37,459 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\59_3.mp4
2025-08-07 17:41:37,460 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxjox8bnl
2025-08-07 17:41:37,501 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:37,501 - INFO -   - 音频时长: 4.72秒
2025-08-07 17:41:37,501 - INFO -   - 视频时长: 4.74秒
2025-08-07 17:41:37,501 - INFO -   - 时长差异: 0.02秒 (0.51%)
2025-08-07 17:41:37,501 - INFO - 
字幕 #59 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:37,501 - INFO - 生成的视频文件:
2025-08-07 17:41:37,501 - INFO -   1. F:/github/aicut_auto/newcut_ai\59_1.mp4
2025-08-07 17:41:37,501 - INFO -   2. F:/github/aicut_auto/newcut_ai\59_2.mp4
2025-08-07 17:41:37,501 - INFO -   3. F:/github/aicut_auto/newcut_ai\59_3.mp4
2025-08-07 17:41:37,501 - INFO - ========== 字幕 #59 处理结束 ==========

