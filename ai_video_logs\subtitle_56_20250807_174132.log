2025-08-07 17:41:32,141 - INFO - ========== 字幕 #56 处理开始 ==========
2025-08-07 17:41:32,141 - INFO - 字幕内容: 会长孙女此时才明白自己错信了小人，她跪下来恳求女孩，救救自己的爷爷。
2025-08-07 17:41:32,141 - INFO - 字幕序号: [2695, 2698]
2025-08-07 17:41:32,141 - INFO - 音频文件详情:
2025-08-07 17:41:32,141 - INFO -   - 路径: output\56.wav
2025-08-07 17:41:32,141 - INFO -   - 时长: 4.55秒
2025-08-07 17:41:32,141 - INFO -   - 验证音频时长: 4.55秒
2025-08-07 17:41:32,141 - INFO - 字幕时间戳信息:
2025-08-07 17:41:32,141 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:32,141 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:32,141 - INFO -   - 根据生成的音频时长(4.55秒)已调整字幕时间戳
2025-08-07 17:41:32,141 - INFO - ========== 新模式：为字幕 #56 生成4套场景方案 ==========
2025-08-07 17:41:32,141 - INFO - 字幕序号列表: [2695, 2698]
2025-08-07 17:41:32,141 - INFO - 
--- 生成方案 #1：基于字幕序号 #2695 ---
2025-08-07 17:41:32,141 - INFO - 开始为单个字幕序号 #2695 匹配场景，目标时长: 4.55秒
2025-08-07 17:41:32,141 - INFO - 开始查找字幕序号 [2695] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:32,143 - INFO - 找到related_overlap场景: scene_id=2923, 字幕#2695
2025-08-07 17:41:32,143 - INFO - 找到related_between场景: scene_id=2922, 字幕#2695
2025-08-07 17:41:32,143 - INFO - 字幕 #2695 找到 1 个overlap场景, 1 个between场景
2025-08-07 17:41:32,143 - INFO - 字幕序号 #2695 找到 1 个可用overlap场景, 1 个可用between场景
2025-08-07 17:41:32,143 - INFO - 选择第一个overlap场景作为起点: scene_id=2923
2025-08-07 17:41:32,143 - INFO - 添加起点场景: scene_id=2923, 时长=3.16秒, 累计时长=3.16秒
2025-08-07 17:41:32,143 - INFO - 起点场景时长不足，需要延伸填充 1.39秒
2025-08-07 17:41:32,144 - INFO - 起点场景在原始列表中的索引: 2922
2025-08-07 17:41:32,144 - INFO - 延伸添加场景: scene_id=2924 (完整时长 0.84秒)
2025-08-07 17:41:32,144 - INFO - 累计时长: 4.00秒
2025-08-07 17:41:32,144 - INFO - 延伸添加场景: scene_id=2925 (裁剪至 0.55秒)
2025-08-07 17:41:32,144 - INFO - 累计时长: 4.55秒
2025-08-07 17:41:32,144 - INFO - 字幕序号 #2695 场景匹配完成，共选择 3 个场景，总时长: 4.55秒
2025-08-07 17:41:32,144 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-08-07 17:41:32,144 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-08-07 17:41:32,144 - INFO - 
--- 生成方案 #2：基于字幕序号 #2698 ---
2025-08-07 17:41:32,144 - INFO - 开始为单个字幕序号 #2698 匹配场景，目标时长: 4.55秒
2025-08-07 17:41:32,144 - INFO - 开始查找字幕序号 [2698] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:32,144 - INFO - 找到related_overlap场景: scene_id=2923, 字幕#2698
2025-08-07 17:41:32,144 - INFO - 找到related_overlap场景: scene_id=2924, 字幕#2698
2025-08-07 17:41:32,144 - INFO - 字幕 #2698 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:32,144 - INFO - 字幕序号 #2698 找到 0 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:32,144 - ERROR - 字幕序号 #2698 没有找到任何可用的匹配场景
2025-08-07 17:41:32,144 - WARNING - 方案 #2 生成失败，未找到合适的场景
2025-08-07 17:41:32,144 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-08-07 17:41:32,144 - INFO - ========== 当前模式：为字幕 #56 生成 1 套场景方案 ==========
2025-08-07 17:41:32,144 - INFO - 开始查找字幕序号 [2695, 2698] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:32,145 - INFO - 找到related_overlap场景: scene_id=2923, 字幕#2695
2025-08-07 17:41:32,145 - INFO - 找到related_overlap场景: scene_id=2924, 字幕#2698
2025-08-07 17:41:32,145 - INFO - 找到related_between场景: scene_id=2922, 字幕#2695
2025-08-07 17:41:32,145 - INFO - 字幕 #2695 找到 1 个overlap场景, 1 个between场景
2025-08-07 17:41:32,145 - INFO - 字幕 #2698 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:32,145 - INFO - 共收集 2 个未使用的overlap场景和 1 个未使用的between场景
2025-08-07 17:41:32,145 - INFO - 开始生成方案 #1
2025-08-07 17:41:32,145 - INFO - 方案 #1: 为字幕#2695选择初始化overlap场景id=2923
2025-08-07 17:41:32,145 - INFO - 方案 #1: 为字幕#2698选择初始化overlap场景id=2924
2025-08-07 17:41:32,145 - INFO - 方案 #1: 初始选择后，当前总时长=4.00秒
2025-08-07 17:41:32,145 - INFO - 方案 #1: 额外between选择后，当前总时长=4.00秒
2025-08-07 17:41:32,146 - INFO - 方案 #1: 额外添加between场景id=2922, 当前总时长=5.00秒
2025-08-07 17:41:32,146 - INFO - 方案 #1: 场景总时长(5.00秒)大于音频时长(4.55秒)，需要裁剪
2025-08-07 17:41:32,146 - INFO - 调整前总时长: 5.00秒, 目标时长: 4.55秒
2025-08-07 17:41:32,146 - INFO - 需要裁剪 0.45秒
2025-08-07 17:41:32,146 - INFO - 裁剪最长场景ID=2923：从3.16秒裁剪至2.71秒
2025-08-07 17:41:32,146 - INFO - 调整后总时长: 4.55秒，与目标时长差异: 0.00秒
2025-08-07 17:41:32,146 - INFO - 方案 #1 调整/填充后最终总时长: 4.55秒
2025-08-07 17:41:32,146 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:32,146 - INFO - ========== 当前模式：字幕 #56 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:32,146 - INFO - 方案 #2 (传统模式) 生成成功
2025-08-07 17:41:32,146 - INFO - ========== 新模式：字幕 #56 共生成 2 套有效场景方案 ==========
2025-08-07 17:41:32,146 - INFO - 
----- 处理字幕 #56 的方案 #1 -----
2025-08-07 17:41:32,146 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\56_1.mp4
2025-08-07 17:41:32,146 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp20rvtft1
2025-08-07 17:41:32,147 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2923.mp4 (确认存在: True)
2025-08-07 17:41:32,147 - INFO - 添加场景ID=2923，时长=3.16秒，累计时长=3.16秒
2025-08-07 17:41:32,147 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2924.mp4 (确认存在: True)
2025-08-07 17:41:32,147 - INFO - 添加场景ID=2924，时长=0.84秒，累计时长=4.00秒
2025-08-07 17:41:32,147 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2925.mp4 (确认存在: True)
2025-08-07 17:41:32,147 - INFO - 添加场景ID=2925，时长=2.32秒，累计时长=6.32秒
2025-08-07 17:41:32,147 - INFO - 准备合并 3 个场景文件，总时长约 6.32秒
2025-08-07 17:41:32,147 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2923.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2924.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2925.mp4'

2025-08-07 17:41:32,147 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp20rvtft1\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp20rvtft1\temp_combined.mp4
2025-08-07 17:41:32,286 - INFO - 合并后的视频时长: 6.39秒，目标音频时长: 4.55秒
2025-08-07 17:41:32,286 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp20rvtft1\temp_combined.mp4 -ss 0 -to 4.547 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\56_1.mp4
2025-08-07 17:41:32,575 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:32,575 - INFO - 目标音频时长: 4.55秒
2025-08-07 17:41:32,575 - INFO - 实际视频时长: 4.58秒
2025-08-07 17:41:32,575 - INFO - 时长差异: 0.04秒 (0.79%)
2025-08-07 17:41:32,575 - INFO - ==========================================
2025-08-07 17:41:32,575 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:32,575 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\56_1.mp4
2025-08-07 17:41:32,576 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp20rvtft1
2025-08-07 17:41:32,620 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:32,620 - INFO -   - 音频时长: 4.55秒
2025-08-07 17:41:32,620 - INFO -   - 视频时长: 4.58秒
2025-08-07 17:41:32,620 - INFO -   - 时长差异: 0.04秒 (0.79%)
2025-08-07 17:41:32,620 - INFO - 
----- 处理字幕 #56 的方案 #2 -----
2025-08-07 17:41:32,620 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\56_2.mp4
2025-08-07 17:41:32,621 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqobfujud
2025-08-07 17:41:32,621 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2923.mp4 (确认存在: True)
2025-08-07 17:41:32,621 - INFO - 添加场景ID=2923，时长=3.16秒，累计时长=3.16秒
2025-08-07 17:41:32,621 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2924.mp4 (确认存在: True)
2025-08-07 17:41:32,621 - INFO - 添加场景ID=2924，时长=0.84秒，累计时长=4.00秒
2025-08-07 17:41:32,621 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2922.mp4 (确认存在: True)
2025-08-07 17:41:32,621 - INFO - 添加场景ID=2922，时长=1.00秒，累计时长=5.00秒
2025-08-07 17:41:32,621 - INFO - 准备合并 3 个场景文件，总时长约 5.00秒
2025-08-07 17:41:32,621 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2923.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2924.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2922.mp4'

2025-08-07 17:41:32,622 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpqobfujud\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpqobfujud\temp_combined.mp4
2025-08-07 17:41:32,744 - INFO - 合并后的视频时长: 5.07秒，目标音频时长: 4.55秒
2025-08-07 17:41:32,744 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpqobfujud\temp_combined.mp4 -ss 0 -to 4.547 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\56_2.mp4
2025-08-07 17:41:33,024 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:33,024 - INFO - 目标音频时长: 4.55秒
2025-08-07 17:41:33,024 - INFO - 实际视频时长: 4.58秒
2025-08-07 17:41:33,024 - INFO - 时长差异: 0.04秒 (0.79%)
2025-08-07 17:41:33,024 - INFO - ==========================================
2025-08-07 17:41:33,024 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:33,024 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\56_2.mp4
2025-08-07 17:41:33,025 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqobfujud
2025-08-07 17:41:33,066 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:33,067 - INFO -   - 音频时长: 4.55秒
2025-08-07 17:41:33,067 - INFO -   - 视频时长: 4.58秒
2025-08-07 17:41:33,067 - INFO -   - 时长差异: 0.04秒 (0.79%)
2025-08-07 17:41:33,067 - INFO - 
字幕 #56 处理完成，成功生成 2/2 套方案
2025-08-07 17:41:33,067 - INFO - 生成的视频文件:
2025-08-07 17:41:33,067 - INFO -   1. F:/github/aicut_auto/newcut_ai\56_1.mp4
2025-08-07 17:41:33,067 - INFO -   2. F:/github/aicut_auto/newcut_ai\56_2.mp4
2025-08-07 17:41:33,067 - INFO - ========== 字幕 #56 处理结束 ==========

