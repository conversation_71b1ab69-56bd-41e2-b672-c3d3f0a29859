2025-08-07 17:41:33,067 - INFO - ========== 字幕 #57 处理开始 ==========
2025-08-07 17:41:33,067 - INFO - 字幕内容: 女孩不再计较，拿出银针，施展出失传已久的幽冥针法，再次将会长从鬼门关拉了回来。
2025-08-07 17:41:33,067 - INFO - 字幕序号: [2728, 2732]
2025-08-07 17:41:33,067 - INFO - 音频文件详情:
2025-08-07 17:41:33,068 - INFO -   - 路径: output\57.wav
2025-08-07 17:41:33,068 - INFO -   - 时长: 5.18秒
2025-08-07 17:41:33,068 - INFO -   - 验证音频时长: 5.18秒
2025-08-07 17:41:33,068 - INFO - 字幕时间戳信息:
2025-08-07 17:41:33,068 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:33,068 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:33,068 - INFO -   - 根据生成的音频时长(5.18秒)已调整字幕时间戳
2025-08-07 17:41:33,068 - INFO - ========== 新模式：为字幕 #57 生成4套场景方案 ==========
2025-08-07 17:41:33,068 - INFO - 字幕序号列表: [2728, 2732]
2025-08-07 17:41:33,068 - INFO - 
--- 生成方案 #1：基于字幕序号 #2728 ---
2025-08-07 17:41:33,068 - INFO - 开始为单个字幕序号 #2728 匹配场景，目标时长: 5.18秒
2025-08-07 17:41:33,068 - INFO - 开始查找字幕序号 [2728] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:33,069 - INFO - 找到related_overlap场景: scene_id=2958, 字幕#2728
2025-08-07 17:41:33,070 - INFO - 找到related_between场景: scene_id=2951, 字幕#2728
2025-08-07 17:41:33,070 - INFO - 找到related_between场景: scene_id=2952, 字幕#2728
2025-08-07 17:41:33,070 - INFO - 找到related_between场景: scene_id=2953, 字幕#2728
2025-08-07 17:41:33,070 - INFO - 找到related_between场景: scene_id=2954, 字幕#2728
2025-08-07 17:41:33,070 - INFO - 找到related_between场景: scene_id=2955, 字幕#2728
2025-08-07 17:41:33,070 - INFO - 找到related_between场景: scene_id=2956, 字幕#2728
2025-08-07 17:41:33,070 - INFO - 找到related_between场景: scene_id=2957, 字幕#2728
2025-08-07 17:41:33,070 - INFO - 字幕 #2728 找到 1 个overlap场景, 7 个between场景
2025-08-07 17:41:33,070 - INFO - 字幕序号 #2728 找到 1 个可用overlap场景, 7 个可用between场景
2025-08-07 17:41:33,070 - INFO - 选择第一个overlap场景作为起点: scene_id=2958
2025-08-07 17:41:33,070 - INFO - 添加起点场景: scene_id=2958, 时长=4.64秒, 累计时长=4.64秒
2025-08-07 17:41:33,070 - INFO - 起点场景时长不足，需要延伸填充 0.54秒
2025-08-07 17:41:33,070 - INFO - 起点场景在原始列表中的索引: 2957
2025-08-07 17:41:33,070 - INFO - 延伸添加场景: scene_id=2959 (裁剪至 0.54秒)
2025-08-07 17:41:33,070 - INFO - 累计时长: 5.18秒
2025-08-07 17:41:33,070 - INFO - 字幕序号 #2728 场景匹配完成，共选择 2 个场景，总时长: 5.18秒
2025-08-07 17:41:33,070 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-08-07 17:41:33,070 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-08-07 17:41:33,070 - INFO - 
--- 生成方案 #2：基于字幕序号 #2732 ---
2025-08-07 17:41:33,070 - INFO - 开始为单个字幕序号 #2732 匹配场景，目标时长: 5.18秒
2025-08-07 17:41:33,070 - INFO - 开始查找字幕序号 [2732] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:33,071 - INFO - 找到related_overlap场景: scene_id=2959, 字幕#2732
2025-08-07 17:41:33,071 - INFO - 找到related_between场景: scene_id=2960, 字幕#2732
2025-08-07 17:41:33,071 - INFO - 字幕 #2732 找到 1 个overlap场景, 1 个between场景
2025-08-07 17:41:33,071 - INFO - 字幕序号 #2732 找到 0 个可用overlap场景, 1 个可用between场景
2025-08-07 17:41:33,071 - INFO - 没有overlap场景，选择第一个between场景作为起点: scene_id=2960
2025-08-07 17:41:33,071 - INFO - 添加起点场景: scene_id=2960, 时长=2.28秒, 累计时长=2.28秒
2025-08-07 17:41:33,071 - INFO - 起点场景时长不足，需要延伸填充 2.90秒
2025-08-07 17:41:33,072 - INFO - 起点场景在原始列表中的索引: 2959
2025-08-07 17:41:33,072 - INFO - 延伸添加场景: scene_id=2961 (完整时长 1.24秒)
2025-08-07 17:41:33,072 - INFO - 累计时长: 3.52秒
2025-08-07 17:41:33,072 - INFO - 延伸添加场景: scene_id=2962 (裁剪至 1.66秒)
2025-08-07 17:41:33,072 - INFO - 累计时长: 5.18秒
2025-08-07 17:41:33,072 - INFO - 字幕序号 #2732 场景匹配完成，共选择 3 个场景，总时长: 5.18秒
2025-08-07 17:41:33,072 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-08-07 17:41:33,072 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:33,072 - INFO - ========== 当前模式：为字幕 #57 生成 1 套场景方案 ==========
2025-08-07 17:41:33,072 - INFO - 开始查找字幕序号 [2728, 2732] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:33,072 - INFO - 找到related_overlap场景: scene_id=2958, 字幕#2728
2025-08-07 17:41:33,072 - INFO - 找到related_overlap场景: scene_id=2959, 字幕#2732
2025-08-07 17:41:33,073 - INFO - 找到related_between场景: scene_id=2951, 字幕#2728
2025-08-07 17:41:33,073 - INFO - 找到related_between场景: scene_id=2952, 字幕#2728
2025-08-07 17:41:33,073 - INFO - 找到related_between场景: scene_id=2953, 字幕#2728
2025-08-07 17:41:33,073 - INFO - 找到related_between场景: scene_id=2954, 字幕#2728
2025-08-07 17:41:33,073 - INFO - 找到related_between场景: scene_id=2955, 字幕#2728
2025-08-07 17:41:33,073 - INFO - 找到related_between场景: scene_id=2956, 字幕#2728
2025-08-07 17:41:33,073 - INFO - 找到related_between场景: scene_id=2957, 字幕#2728
2025-08-07 17:41:33,073 - INFO - 找到related_between场景: scene_id=2960, 字幕#2732
2025-08-07 17:41:33,073 - INFO - 字幕 #2728 找到 1 个overlap场景, 7 个between场景
2025-08-07 17:41:33,073 - INFO - 字幕 #2732 找到 1 个overlap场景, 1 个between场景
2025-08-07 17:41:33,073 - INFO - 共收集 2 个未使用的overlap场景和 8 个未使用的between场景
2025-08-07 17:41:33,073 - INFO - 开始生成方案 #1
2025-08-07 17:41:33,073 - INFO - 方案 #1: 为字幕#2728选择初始化overlap场景id=2958
2025-08-07 17:41:33,073 - INFO - 方案 #1: 为字幕#2732选择初始化overlap场景id=2959
2025-08-07 17:41:33,073 - INFO - 方案 #1: 初始选择后，当前总时长=7.68秒
2025-08-07 17:41:33,073 - INFO - 方案 #1: 额外between选择后，当前总时长=7.68秒
2025-08-07 17:41:33,073 - INFO - 方案 #1: 场景总时长(7.68秒)大于音频时长(5.18秒)，需要裁剪
2025-08-07 17:41:33,073 - INFO - 调整前总时长: 7.68秒, 目标时长: 5.18秒
2025-08-07 17:41:33,073 - INFO - 需要裁剪 2.50秒
2025-08-07 17:41:33,073 - INFO - 裁剪最长场景ID=2958：从4.64秒裁剪至2.14秒
2025-08-07 17:41:33,073 - INFO - 调整后总时长: 5.18秒，与目标时长差异: 0.00秒
2025-08-07 17:41:33,073 - INFO - 方案 #1 调整/填充后最终总时长: 5.18秒
2025-08-07 17:41:33,073 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:33,073 - INFO - ========== 当前模式：字幕 #57 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:33,073 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:33,073 - INFO - ========== 新模式：字幕 #57 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:33,073 - INFO - 
----- 处理字幕 #57 的方案 #1 -----
2025-08-07 17:41:33,073 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\57_1.mp4
2025-08-07 17:41:33,074 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpwsjanmkd
2025-08-07 17:41:33,074 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2958.mp4 (确认存在: True)
2025-08-07 17:41:33,074 - INFO - 添加场景ID=2958，时长=4.64秒，累计时长=4.64秒
2025-08-07 17:41:33,074 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2959.mp4 (确认存在: True)
2025-08-07 17:41:33,074 - INFO - 添加场景ID=2959，时长=3.04秒，累计时长=7.68秒
2025-08-07 17:41:33,074 - INFO - 准备合并 2 个场景文件，总时长约 7.68秒
2025-08-07 17:41:33,075 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2958.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2959.mp4'

2025-08-07 17:41:33,075 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpwsjanmkd\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpwsjanmkd\temp_combined.mp4
2025-08-07 17:41:33,186 - INFO - 合并后的视频时长: 7.73秒，目标音频时长: 5.18秒
2025-08-07 17:41:33,186 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpwsjanmkd\temp_combined.mp4 -ss 0 -to 5.181 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\57_1.mp4
2025-08-07 17:41:33,508 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:33,508 - INFO - 目标音频时长: 5.18秒
2025-08-07 17:41:33,508 - INFO - 实际视频时长: 5.22秒
2025-08-07 17:41:33,508 - INFO - 时长差异: 0.04秒 (0.81%)
2025-08-07 17:41:33,508 - INFO - ==========================================
2025-08-07 17:41:33,508 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:33,508 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\57_1.mp4
2025-08-07 17:41:33,516 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpwsjanmkd
2025-08-07 17:41:33,560 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:33,560 - INFO -   - 音频时长: 5.18秒
2025-08-07 17:41:33,560 - INFO -   - 视频时长: 5.22秒
2025-08-07 17:41:33,560 - INFO -   - 时长差异: 0.04秒 (0.81%)
2025-08-07 17:41:33,560 - INFO - 
----- 处理字幕 #57 的方案 #2 -----
2025-08-07 17:41:33,560 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\57_2.mp4
2025-08-07 17:41:33,560 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpd8o_mav6
2025-08-07 17:41:33,561 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2960.mp4 (确认存在: True)
2025-08-07 17:41:33,561 - INFO - 添加场景ID=2960，时长=2.28秒，累计时长=2.28秒
2025-08-07 17:41:33,562 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2961.mp4 (确认存在: True)
2025-08-07 17:41:33,562 - INFO - 添加场景ID=2961，时长=1.24秒，累计时长=3.52秒
2025-08-07 17:41:33,562 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2962.mp4 (确认存在: True)
2025-08-07 17:41:33,562 - INFO - 添加场景ID=2962，时长=1.92秒，累计时长=5.44秒
2025-08-07 17:41:33,562 - INFO - 准备合并 3 个场景文件，总时长约 5.44秒
2025-08-07 17:41:33,562 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2960.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2961.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2962.mp4'

2025-08-07 17:41:33,562 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpd8o_mav6\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpd8o_mav6\temp_combined.mp4
2025-08-07 17:41:33,702 - INFO - 合并后的视频时长: 5.51秒，目标音频时长: 5.18秒
2025-08-07 17:41:33,702 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpd8o_mav6\temp_combined.mp4 -ss 0 -to 5.181 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\57_2.mp4
2025-08-07 17:41:34,044 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:34,044 - INFO - 目标音频时长: 5.18秒
2025-08-07 17:41:34,044 - INFO - 实际视频时长: 5.22秒
2025-08-07 17:41:34,044 - INFO - 时长差异: 0.04秒 (0.81%)
2025-08-07 17:41:34,044 - INFO - ==========================================
2025-08-07 17:41:34,044 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:34,044 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\57_2.mp4
2025-08-07 17:41:34,045 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpd8o_mav6
2025-08-07 17:41:34,088 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:34,088 - INFO -   - 音频时长: 5.18秒
2025-08-07 17:41:34,088 - INFO -   - 视频时长: 5.22秒
2025-08-07 17:41:34,088 - INFO -   - 时长差异: 0.04秒 (0.81%)
2025-08-07 17:41:34,088 - INFO - 
----- 处理字幕 #57 的方案 #3 -----
2025-08-07 17:41:34,088 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\57_3.mp4
2025-08-07 17:41:34,088 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp59tq5j57
2025-08-07 17:41:34,089 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2958.mp4 (确认存在: True)
2025-08-07 17:41:34,089 - INFO - 添加场景ID=2958，时长=4.64秒，累计时长=4.64秒
2025-08-07 17:41:34,089 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2959.mp4 (确认存在: True)
2025-08-07 17:41:34,089 - INFO - 添加场景ID=2959，时长=3.04秒，累计时长=7.68秒
2025-08-07 17:41:34,089 - INFO - 准备合并 2 个场景文件，总时长约 7.68秒
2025-08-07 17:41:34,089 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2958.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2959.mp4'

2025-08-07 17:41:34,089 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp59tq5j57\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp59tq5j57\temp_combined.mp4
2025-08-07 17:41:34,214 - INFO - 合并后的视频时长: 7.73秒，目标音频时长: 5.18秒
2025-08-07 17:41:34,214 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp59tq5j57\temp_combined.mp4 -ss 0 -to 5.181 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\57_3.mp4
2025-08-07 17:41:34,532 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:34,532 - INFO - 目标音频时长: 5.18秒
2025-08-07 17:41:34,532 - INFO - 实际视频时长: 5.22秒
2025-08-07 17:41:34,532 - INFO - 时长差异: 0.04秒 (0.81%)
2025-08-07 17:41:34,532 - INFO - ==========================================
2025-08-07 17:41:34,532 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:34,532 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\57_3.mp4
2025-08-07 17:41:34,533 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp59tq5j57
2025-08-07 17:41:34,574 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:34,574 - INFO -   - 音频时长: 5.18秒
2025-08-07 17:41:34,574 - INFO -   - 视频时长: 5.22秒
2025-08-07 17:41:34,574 - INFO -   - 时长差异: 0.04秒 (0.81%)
2025-08-07 17:41:34,574 - INFO - 
字幕 #57 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:34,574 - INFO - 生成的视频文件:
2025-08-07 17:41:34,574 - INFO -   1. F:/github/aicut_auto/newcut_ai\57_1.mp4
2025-08-07 17:41:34,574 - INFO -   2. F:/github/aicut_auto/newcut_ai\57_2.mp4
2025-08-07 17:41:34,574 - INFO -   3. F:/github/aicut_auto/newcut_ai\57_3.mp4
2025-08-07 17:41:34,574 - INFO - ========== 字幕 #57 处理结束 ==========

