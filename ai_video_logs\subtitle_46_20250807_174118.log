2025-08-07 17:41:18,207 - INFO - ========== 字幕 #46 处理开始 ==========
2025-08-07 17:41:18,207 - INFO - 字幕内容: 凭借出色的能力，男人毫无悬念地拿下项目，就在即将签约时，二弟再次出现搅局。
2025-08-07 17:41:18,207 - INFO - 字幕序号: [2027, 2033]
2025-08-07 17:41:18,207 - INFO - 音频文件详情:
2025-08-07 17:41:18,207 - INFO -   - 路径: output\46.wav
2025-08-07 17:41:18,207 - INFO -   - 时长: 5.15秒
2025-08-07 17:41:18,207 - INFO -   - 验证音频时长: 5.15秒
2025-08-07 17:41:18,207 - INFO - 字幕时间戳信息:
2025-08-07 17:41:18,208 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:18,208 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:18,208 - INFO -   - 根据生成的音频时长(5.15秒)已调整字幕时间戳
2025-08-07 17:41:18,208 - INFO - ========== 新模式：为字幕 #46 生成4套场景方案 ==========
2025-08-07 17:41:18,208 - INFO - 字幕序号列表: [2027, 2033]
2025-08-07 17:41:18,208 - INFO - 
--- 生成方案 #1：基于字幕序号 #2027 ---
2025-08-07 17:41:18,208 - INFO - 开始为单个字幕序号 #2027 匹配场景，目标时长: 5.15秒
2025-08-07 17:41:18,208 - INFO - 开始查找字幕序号 [2027] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:18,208 - INFO - 找到related_overlap场景: scene_id=2297, 字幕#2027
2025-08-07 17:41:18,209 - INFO - 字幕 #2027 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:18,209 - INFO - 字幕序号 #2027 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:18,209 - INFO - 选择第一个overlap场景作为起点: scene_id=2297
2025-08-07 17:41:18,209 - INFO - 添加起点场景: scene_id=2297, 时长=4.84秒, 累计时长=4.84秒
2025-08-07 17:41:18,209 - INFO - 起点场景时长不足，需要延伸填充 0.31秒
2025-08-07 17:41:18,209 - INFO - 起点场景在原始列表中的索引: 2296
2025-08-07 17:41:18,209 - INFO - 延伸添加场景: scene_id=2298 (裁剪至 0.31秒)
2025-08-07 17:41:18,209 - INFO - 累计时长: 5.15秒
2025-08-07 17:41:18,209 - INFO - 字幕序号 #2027 场景匹配完成，共选择 2 个场景，总时长: 5.15秒
2025-08-07 17:41:18,209 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-08-07 17:41:18,209 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-08-07 17:41:18,209 - INFO - 
--- 生成方案 #2：基于字幕序号 #2033 ---
2025-08-07 17:41:18,209 - INFO - 开始为单个字幕序号 #2033 匹配场景，目标时长: 5.15秒
2025-08-07 17:41:18,209 - INFO - 开始查找字幕序号 [2033] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:18,210 - INFO - 找到related_overlap场景: scene_id=2308, 字幕#2033
2025-08-07 17:41:18,210 - INFO - 找到related_between场景: scene_id=2305, 字幕#2033
2025-08-07 17:41:18,210 - INFO - 找到related_between场景: scene_id=2306, 字幕#2033
2025-08-07 17:41:18,210 - INFO - 找到related_between场景: scene_id=2307, 字幕#2033
2025-08-07 17:41:18,210 - INFO - 找到related_between场景: scene_id=2309, 字幕#2033
2025-08-07 17:41:18,210 - INFO - 找到related_between场景: scene_id=2310, 字幕#2033
2025-08-07 17:41:18,210 - INFO - 字幕 #2033 找到 1 个overlap场景, 5 个between场景
2025-08-07 17:41:18,210 - INFO - 字幕序号 #2033 找到 1 个可用overlap场景, 5 个可用between场景
2025-08-07 17:41:18,210 - INFO - 选择第一个overlap场景作为起点: scene_id=2308
2025-08-07 17:41:18,210 - INFO - 添加起点场景: scene_id=2308, 时长=2.24秒, 累计时长=2.24秒
2025-08-07 17:41:18,210 - INFO - 起点场景时长不足，需要延伸填充 2.91秒
2025-08-07 17:41:18,211 - INFO - 起点场景在原始列表中的索引: 2307
2025-08-07 17:41:18,211 - INFO - 延伸添加场景: scene_id=2309 (完整时长 1.20秒)
2025-08-07 17:41:18,211 - INFO - 累计时长: 3.44秒
2025-08-07 17:41:18,211 - INFO - 延伸添加场景: scene_id=2310 (完整时长 1.60秒)
2025-08-07 17:41:18,211 - INFO - 累计时长: 5.04秒
2025-08-07 17:41:18,211 - INFO - 延伸添加场景: scene_id=2311 (裁剪至 0.11秒)
2025-08-07 17:41:18,211 - INFO - 累计时长: 5.15秒
2025-08-07 17:41:18,211 - INFO - 字幕序号 #2033 场景匹配完成，共选择 4 个场景，总时长: 5.15秒
2025-08-07 17:41:18,211 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-08-07 17:41:18,211 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:18,211 - INFO - ========== 当前模式：为字幕 #46 生成 1 套场景方案 ==========
2025-08-07 17:41:18,211 - INFO - 开始查找字幕序号 [2027, 2033] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:18,211 - INFO - 找到related_overlap场景: scene_id=2297, 字幕#2027
2025-08-07 17:41:18,211 - INFO - 找到related_overlap场景: scene_id=2308, 字幕#2033
2025-08-07 17:41:18,212 - INFO - 找到related_between场景: scene_id=2305, 字幕#2033
2025-08-07 17:41:18,212 - INFO - 找到related_between场景: scene_id=2306, 字幕#2033
2025-08-07 17:41:18,212 - INFO - 找到related_between场景: scene_id=2307, 字幕#2033
2025-08-07 17:41:18,212 - INFO - 找到related_between场景: scene_id=2309, 字幕#2033
2025-08-07 17:41:18,212 - INFO - 找到related_between场景: scene_id=2310, 字幕#2033
2025-08-07 17:41:18,212 - INFO - 字幕 #2027 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:18,212 - INFO - 字幕 #2033 找到 1 个overlap场景, 5 个between场景
2025-08-07 17:41:18,212 - INFO - 共收集 2 个未使用的overlap场景和 5 个未使用的between场景
2025-08-07 17:41:18,212 - INFO - 开始生成方案 #1
2025-08-07 17:41:18,212 - INFO - 方案 #1: 为字幕#2027选择初始化overlap场景id=2297
2025-08-07 17:41:18,212 - INFO - 方案 #1: 为字幕#2033选择初始化overlap场景id=2308
2025-08-07 17:41:18,212 - INFO - 方案 #1: 初始选择后，当前总时长=7.08秒
2025-08-07 17:41:18,212 - INFO - 方案 #1: 额外between选择后，当前总时长=7.08秒
2025-08-07 17:41:18,212 - INFO - 方案 #1: 场景总时长(7.08秒)大于音频时长(5.15秒)，需要裁剪
2025-08-07 17:41:18,212 - INFO - 调整前总时长: 7.08秒, 目标时长: 5.15秒
2025-08-07 17:41:18,212 - INFO - 需要裁剪 1.93秒
2025-08-07 17:41:18,212 - INFO - 裁剪最长场景ID=2297：从4.84秒裁剪至2.91秒
2025-08-07 17:41:18,212 - INFO - 调整后总时长: 5.15秒，与目标时长差异: 0.00秒
2025-08-07 17:41:18,212 - INFO - 方案 #1 调整/填充后最终总时长: 5.15秒
2025-08-07 17:41:18,212 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:18,212 - INFO - ========== 当前模式：字幕 #46 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:18,212 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:18,212 - INFO - ========== 新模式：字幕 #46 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:18,212 - INFO - 
----- 处理字幕 #46 的方案 #1 -----
2025-08-07 17:41:18,212 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\46_1.mp4
2025-08-07 17:41:18,213 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplrm1j7ui
2025-08-07 17:41:18,213 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2297.mp4 (确认存在: True)
2025-08-07 17:41:18,213 - INFO - 添加场景ID=2297，时长=4.84秒，累计时长=4.84秒
2025-08-07 17:41:18,213 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2298.mp4 (确认存在: True)
2025-08-07 17:41:18,213 - INFO - 添加场景ID=2298，时长=1.28秒，累计时长=6.12秒
2025-08-07 17:41:18,213 - INFO - 准备合并 2 个场景文件，总时长约 6.12秒
2025-08-07 17:41:18,213 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2297.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2298.mp4'

2025-08-07 17:41:18,213 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmplrm1j7ui\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmplrm1j7ui\temp_combined.mp4
2025-08-07 17:41:18,330 - INFO - 合并后的视频时长: 6.17秒，目标音频时长: 5.15秒
2025-08-07 17:41:18,330 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmplrm1j7ui\temp_combined.mp4 -ss 0 -to 5.152 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\46_1.mp4
2025-08-07 17:41:18,670 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:18,670 - INFO - 目标音频时长: 5.15秒
2025-08-07 17:41:18,670 - INFO - 实际视频时长: 5.18秒
2025-08-07 17:41:18,670 - INFO - 时长差异: 0.03秒 (0.60%)
2025-08-07 17:41:18,670 - INFO - ==========================================
2025-08-07 17:41:18,670 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:18,670 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\46_1.mp4
2025-08-07 17:41:18,670 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplrm1j7ui
2025-08-07 17:41:18,714 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:18,714 - INFO -   - 音频时长: 5.15秒
2025-08-07 17:41:18,714 - INFO -   - 视频时长: 5.18秒
2025-08-07 17:41:18,714 - INFO -   - 时长差异: 0.03秒 (0.60%)
2025-08-07 17:41:18,714 - INFO - 
----- 处理字幕 #46 的方案 #2 -----
2025-08-07 17:41:18,714 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\46_2.mp4
2025-08-07 17:41:18,714 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6ogipdft
2025-08-07 17:41:18,715 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2308.mp4 (确认存在: True)
2025-08-07 17:41:18,715 - INFO - 添加场景ID=2308，时长=2.24秒，累计时长=2.24秒
2025-08-07 17:41:18,715 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2309.mp4 (确认存在: True)
2025-08-07 17:41:18,715 - INFO - 添加场景ID=2309，时长=1.20秒，累计时长=3.44秒
2025-08-07 17:41:18,715 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2310.mp4 (确认存在: True)
2025-08-07 17:41:18,715 - INFO - 添加场景ID=2310，时长=1.60秒，累计时长=5.04秒
2025-08-07 17:41:18,715 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2311.mp4 (确认存在: True)
2025-08-07 17:41:18,715 - INFO - 添加场景ID=2311，时长=0.96秒，累计时长=6.00秒
2025-08-07 17:41:18,715 - INFO - 准备合并 4 个场景文件，总时长约 6.00秒
2025-08-07 17:41:18,715 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2308.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2309.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2310.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2311.mp4'

2025-08-07 17:41:18,715 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp6ogipdft\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp6ogipdft\temp_combined.mp4
2025-08-07 17:41:18,869 - INFO - 合并后的视频时长: 6.09秒，目标音频时长: 5.15秒
2025-08-07 17:41:18,869 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp6ogipdft\temp_combined.mp4 -ss 0 -to 5.152 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\46_2.mp4
2025-08-07 17:41:19,184 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:19,186 - INFO - 目标音频时长: 5.15秒
2025-08-07 17:41:19,186 - INFO - 实际视频时长: 5.18秒
2025-08-07 17:41:19,186 - INFO - 时长差异: 0.03秒 (0.60%)
2025-08-07 17:41:19,186 - INFO - ==========================================
2025-08-07 17:41:19,186 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:19,186 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\46_2.mp4
2025-08-07 17:41:19,186 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6ogipdft
2025-08-07 17:41:19,230 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:19,230 - INFO -   - 音频时长: 5.15秒
2025-08-07 17:41:19,230 - INFO -   - 视频时长: 5.18秒
2025-08-07 17:41:19,230 - INFO -   - 时长差异: 0.03秒 (0.60%)
2025-08-07 17:41:19,230 - INFO - 
----- 处理字幕 #46 的方案 #3 -----
2025-08-07 17:41:19,230 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\46_3.mp4
2025-08-07 17:41:19,230 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpc6yjkf97
2025-08-07 17:41:19,231 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2297.mp4 (确认存在: True)
2025-08-07 17:41:19,231 - INFO - 添加场景ID=2297，时长=4.84秒，累计时长=4.84秒
2025-08-07 17:41:19,231 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2308.mp4 (确认存在: True)
2025-08-07 17:41:19,231 - INFO - 添加场景ID=2308，时长=2.24秒，累计时长=7.08秒
2025-08-07 17:41:19,231 - INFO - 准备合并 2 个场景文件，总时长约 7.08秒
2025-08-07 17:41:19,231 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2297.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2308.mp4'

2025-08-07 17:41:19,231 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpc6yjkf97\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpc6yjkf97\temp_combined.mp4
2025-08-07 17:41:19,352 - INFO - 合并后的视频时长: 7.13秒，目标音频时长: 5.15秒
2025-08-07 17:41:19,352 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpc6yjkf97\temp_combined.mp4 -ss 0 -to 5.152 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\46_3.mp4
2025-08-07 17:41:19,671 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:19,671 - INFO - 目标音频时长: 5.15秒
2025-08-07 17:41:19,671 - INFO - 实际视频时长: 5.18秒
2025-08-07 17:41:19,671 - INFO - 时长差异: 0.03秒 (0.60%)
2025-08-07 17:41:19,671 - INFO - ==========================================
2025-08-07 17:41:19,671 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:19,671 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\46_3.mp4
2025-08-07 17:41:19,672 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpc6yjkf97
2025-08-07 17:41:19,713 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:19,713 - INFO -   - 音频时长: 5.15秒
2025-08-07 17:41:19,713 - INFO -   - 视频时长: 5.18秒
2025-08-07 17:41:19,713 - INFO -   - 时长差异: 0.03秒 (0.60%)
2025-08-07 17:41:19,714 - INFO - 
字幕 #46 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:19,714 - INFO - 生成的视频文件:
2025-08-07 17:41:19,714 - INFO -   1. F:/github/aicut_auto/newcut_ai\46_1.mp4
2025-08-07 17:41:19,714 - INFO -   2. F:/github/aicut_auto/newcut_ai\46_2.mp4
2025-08-07 17:41:19,714 - INFO -   3. F:/github/aicut_auto/newcut_ai\46_3.mp4
2025-08-07 17:41:19,714 - INFO - ========== 字幕 #46 处理结束 ==========

