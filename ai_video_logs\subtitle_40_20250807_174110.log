2025-08-07 17:41:10,062 - INFO - ========== 字幕 #40 处理开始 ==========
2025-08-07 17:41:10,062 - INFO - 字幕内容: 爷爷大怒，要将这个蛇蝎女人送到警局，心机女罪行败露。
2025-08-07 17:41:10,062 - INFO - 字幕序号: [1426, 1431]
2025-08-07 17:41:10,062 - INFO - 音频文件详情:
2025-08-07 17:41:10,062 - INFO -   - 路径: output\40.wav
2025-08-07 17:41:10,062 - INFO -   - 时长: 3.83秒
2025-08-07 17:41:10,063 - INFO -   - 验证音频时长: 3.83秒
2025-08-07 17:41:10,063 - INFO - 字幕时间戳信息:
2025-08-07 17:41:10,063 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:10,063 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:10,063 - INFO -   - 根据生成的音频时长(3.83秒)已调整字幕时间戳
2025-08-07 17:41:10,063 - INFO - ========== 新模式：为字幕 #40 生成4套场景方案 ==========
2025-08-07 17:41:10,063 - INFO - 字幕序号列表: [1426, 1431]
2025-08-07 17:41:10,063 - INFO - 
--- 生成方案 #1：基于字幕序号 #1426 ---
2025-08-07 17:41:10,063 - INFO - 开始为单个字幕序号 #1426 匹配场景，目标时长: 3.83秒
2025-08-07 17:41:10,063 - INFO - 开始查找字幕序号 [1426] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:10,063 - INFO - 找到related_overlap场景: scene_id=1641, 字幕#1426
2025-08-07 17:41:10,064 - INFO - 字幕 #1426 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:10,064 - INFO - 字幕序号 #1426 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:10,064 - INFO - 选择第一个overlap场景作为起点: scene_id=1641
2025-08-07 17:41:10,064 - INFO - 添加起点场景: scene_id=1641, 时长=2.56秒, 累计时长=2.56秒
2025-08-07 17:41:10,064 - INFO - 起点场景时长不足，需要延伸填充 1.27秒
2025-08-07 17:41:10,064 - INFO - 起点场景在原始列表中的索引: 1640
2025-08-07 17:41:10,064 - INFO - 延伸添加场景: scene_id=1642 (裁剪至 1.27秒)
2025-08-07 17:41:10,064 - INFO - 累计时长: 3.83秒
2025-08-07 17:41:10,064 - INFO - 字幕序号 #1426 场景匹配完成，共选择 2 个场景，总时长: 3.83秒
2025-08-07 17:41:10,064 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-08-07 17:41:10,064 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-08-07 17:41:10,064 - INFO - 
--- 生成方案 #2：基于字幕序号 #1431 ---
2025-08-07 17:41:10,064 - INFO - 开始为单个字幕序号 #1431 匹配场景，目标时长: 3.83秒
2025-08-07 17:41:10,064 - INFO - 开始查找字幕序号 [1431] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:10,064 - INFO - 找到related_overlap场景: scene_id=1644, 字幕#1431
2025-08-07 17:41:10,066 - INFO - 找到related_between场景: scene_id=1645, 字幕#1431
2025-08-07 17:41:10,066 - INFO - 找到related_between场景: scene_id=1646, 字幕#1431
2025-08-07 17:41:10,066 - INFO - 找到related_between场景: scene_id=1647, 字幕#1431
2025-08-07 17:41:10,066 - INFO - 找到related_between场景: scene_id=1648, 字幕#1431
2025-08-07 17:41:10,066 - INFO - 找到related_between场景: scene_id=1649, 字幕#1431
2025-08-07 17:41:10,066 - INFO - 字幕 #1431 找到 1 个overlap场景, 5 个between场景
2025-08-07 17:41:10,066 - INFO - 字幕序号 #1431 找到 1 个可用overlap场景, 5 个可用between场景
2025-08-07 17:41:10,066 - INFO - 选择第一个overlap场景作为起点: scene_id=1644
2025-08-07 17:41:10,066 - INFO - 添加起点场景: scene_id=1644, 时长=3.12秒, 累计时长=3.12秒
2025-08-07 17:41:10,066 - INFO - 起点场景时长不足，需要延伸填充 0.71秒
2025-08-07 17:41:10,066 - INFO - 起点场景在原始列表中的索引: 1643
2025-08-07 17:41:10,066 - INFO - 延伸添加场景: scene_id=1645 (裁剪至 0.71秒)
2025-08-07 17:41:10,066 - INFO - 累计时长: 3.83秒
2025-08-07 17:41:10,066 - INFO - 字幕序号 #1431 场景匹配完成，共选择 2 个场景，总时长: 3.83秒
2025-08-07 17:41:10,066 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-08-07 17:41:10,066 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:10,066 - INFO - ========== 当前模式：为字幕 #40 生成 1 套场景方案 ==========
2025-08-07 17:41:10,066 - INFO - 开始查找字幕序号 [1426, 1431] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:10,067 - INFO - 找到related_overlap场景: scene_id=1641, 字幕#1426
2025-08-07 17:41:10,067 - INFO - 找到related_overlap场景: scene_id=1644, 字幕#1431
2025-08-07 17:41:10,067 - INFO - 找到related_between场景: scene_id=1645, 字幕#1431
2025-08-07 17:41:10,067 - INFO - 找到related_between场景: scene_id=1646, 字幕#1431
2025-08-07 17:41:10,067 - INFO - 找到related_between场景: scene_id=1647, 字幕#1431
2025-08-07 17:41:10,067 - INFO - 找到related_between场景: scene_id=1648, 字幕#1431
2025-08-07 17:41:10,067 - INFO - 找到related_between场景: scene_id=1649, 字幕#1431
2025-08-07 17:41:10,068 - INFO - 字幕 #1426 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:10,068 - INFO - 字幕 #1431 找到 1 个overlap场景, 5 个between场景
2025-08-07 17:41:10,068 - INFO - 共收集 2 个未使用的overlap场景和 5 个未使用的between场景
2025-08-07 17:41:10,068 - INFO - 开始生成方案 #1
2025-08-07 17:41:10,068 - INFO - 方案 #1: 为字幕#1426选择初始化overlap场景id=1641
2025-08-07 17:41:10,068 - INFO - 方案 #1: 为字幕#1431选择初始化overlap场景id=1644
2025-08-07 17:41:10,068 - INFO - 方案 #1: 初始选择后，当前总时长=5.68秒
2025-08-07 17:41:10,068 - INFO - 方案 #1: 额外between选择后，当前总时长=5.68秒
2025-08-07 17:41:10,068 - INFO - 方案 #1: 场景总时长(5.68秒)大于音频时长(3.83秒)，需要裁剪
2025-08-07 17:41:10,068 - INFO - 调整前总时长: 5.68秒, 目标时长: 3.83秒
2025-08-07 17:41:10,068 - INFO - 需要裁剪 1.85秒
2025-08-07 17:41:10,068 - INFO - 裁剪最长场景ID=1644：从3.12秒裁剪至1.27秒
2025-08-07 17:41:10,068 - INFO - 调整后总时长: 3.83秒，与目标时长差异: 0.00秒
2025-08-07 17:41:10,068 - INFO - 方案 #1 调整/填充后最终总时长: 3.83秒
2025-08-07 17:41:10,068 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:10,068 - INFO - ========== 当前模式：字幕 #40 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:10,068 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:10,068 - INFO - ========== 新模式：字幕 #40 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:10,068 - INFO - 
----- 处理字幕 #40 的方案 #1 -----
2025-08-07 17:41:10,068 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\40_1.mp4
2025-08-07 17:41:10,068 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbxy42oxa
2025-08-07 17:41:10,069 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1641.mp4 (确认存在: True)
2025-08-07 17:41:10,069 - INFO - 添加场景ID=1641，时长=2.56秒，累计时长=2.56秒
2025-08-07 17:41:10,069 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1642.mp4 (确认存在: True)
2025-08-07 17:41:10,069 - INFO - 添加场景ID=1642，时长=1.80秒，累计时长=4.36秒
2025-08-07 17:41:10,069 - INFO - 准备合并 2 个场景文件，总时长约 4.36秒
2025-08-07 17:41:10,069 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1641.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1642.mp4'

2025-08-07 17:41:10,069 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpbxy42oxa\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpbxy42oxa\temp_combined.mp4
2025-08-07 17:41:10,186 - INFO - 合并后的视频时长: 4.41秒，目标音频时长: 3.83秒
2025-08-07 17:41:10,186 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpbxy42oxa\temp_combined.mp4 -ss 0 -to 3.834 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\40_1.mp4
2025-08-07 17:41:10,456 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:10,456 - INFO - 目标音频时长: 3.83秒
2025-08-07 17:41:10,456 - INFO - 实际视频时长: 3.86秒
2025-08-07 17:41:10,456 - INFO - 时长差异: 0.03秒 (0.76%)
2025-08-07 17:41:10,456 - INFO - ==========================================
2025-08-07 17:41:10,456 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:10,456 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\40_1.mp4
2025-08-07 17:41:10,457 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbxy42oxa
2025-08-07 17:41:10,500 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:10,500 - INFO -   - 音频时长: 3.83秒
2025-08-07 17:41:10,500 - INFO -   - 视频时长: 3.86秒
2025-08-07 17:41:10,500 - INFO -   - 时长差异: 0.03秒 (0.76%)
2025-08-07 17:41:10,501 - INFO - 
----- 处理字幕 #40 的方案 #2 -----
2025-08-07 17:41:10,501 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\40_2.mp4
2025-08-07 17:41:10,501 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpyy1mo216
2025-08-07 17:41:10,501 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1644.mp4 (确认存在: True)
2025-08-07 17:41:10,502 - INFO - 添加场景ID=1644，时长=3.12秒，累计时长=3.12秒
2025-08-07 17:41:10,502 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1645.mp4 (确认存在: True)
2025-08-07 17:41:10,502 - INFO - 添加场景ID=1645，时长=1.56秒，累计时长=4.68秒
2025-08-07 17:41:10,502 - INFO - 准备合并 2 个场景文件，总时长约 4.68秒
2025-08-07 17:41:10,502 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1644.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1645.mp4'

2025-08-07 17:41:10,502 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpyy1mo216\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpyy1mo216\temp_combined.mp4
2025-08-07 17:41:10,630 - INFO - 合并后的视频时长: 4.73秒，目标音频时长: 3.83秒
2025-08-07 17:41:10,630 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpyy1mo216\temp_combined.mp4 -ss 0 -to 3.834 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\40_2.mp4
2025-08-07 17:41:10,927 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:10,927 - INFO - 目标音频时长: 3.83秒
2025-08-07 17:41:10,928 - INFO - 实际视频时长: 3.86秒
2025-08-07 17:41:10,928 - INFO - 时长差异: 0.03秒 (0.76%)
2025-08-07 17:41:10,928 - INFO - ==========================================
2025-08-07 17:41:10,928 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:10,928 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\40_2.mp4
2025-08-07 17:41:10,928 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpyy1mo216
2025-08-07 17:41:10,971 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:10,971 - INFO -   - 音频时长: 3.83秒
2025-08-07 17:41:10,971 - INFO -   - 视频时长: 3.86秒
2025-08-07 17:41:10,971 - INFO -   - 时长差异: 0.03秒 (0.76%)
2025-08-07 17:41:10,971 - INFO - 
----- 处理字幕 #40 的方案 #3 -----
2025-08-07 17:41:10,971 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\40_3.mp4
2025-08-07 17:41:10,972 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpd7j5cr3g
2025-08-07 17:41:10,973 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1641.mp4 (确认存在: True)
2025-08-07 17:41:10,973 - INFO - 添加场景ID=1641，时长=2.56秒，累计时长=2.56秒
2025-08-07 17:41:10,973 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1644.mp4 (确认存在: True)
2025-08-07 17:41:10,973 - INFO - 添加场景ID=1644，时长=3.12秒，累计时长=5.68秒
2025-08-07 17:41:10,973 - INFO - 准备合并 2 个场景文件，总时长约 5.68秒
2025-08-07 17:41:10,973 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1641.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1644.mp4'

2025-08-07 17:41:10,973 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpd7j5cr3g\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpd7j5cr3g\temp_combined.mp4
2025-08-07 17:41:11,106 - INFO - 合并后的视频时长: 5.73秒，目标音频时长: 3.83秒
2025-08-07 17:41:11,106 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpd7j5cr3g\temp_combined.mp4 -ss 0 -to 3.834 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\40_3.mp4
2025-08-07 17:41:11,382 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:11,382 - INFO - 目标音频时长: 3.83秒
2025-08-07 17:41:11,382 - INFO - 实际视频时长: 3.86秒
2025-08-07 17:41:11,382 - INFO - 时长差异: 0.03秒 (0.76%)
2025-08-07 17:41:11,382 - INFO - ==========================================
2025-08-07 17:41:11,382 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:11,382 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\40_3.mp4
2025-08-07 17:41:11,383 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpd7j5cr3g
2025-08-07 17:41:11,426 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:11,426 - INFO -   - 音频时长: 3.83秒
2025-08-07 17:41:11,426 - INFO -   - 视频时长: 3.86秒
2025-08-07 17:41:11,426 - INFO -   - 时长差异: 0.03秒 (0.76%)
2025-08-07 17:41:11,426 - INFO - 
字幕 #40 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:11,426 - INFO - 生成的视频文件:
2025-08-07 17:41:11,426 - INFO -   1. F:/github/aicut_auto/newcut_ai\40_1.mp4
2025-08-07 17:41:11,426 - INFO -   2. F:/github/aicut_auto/newcut_ai\40_2.mp4
2025-08-07 17:41:11,426 - INFO -   3. F:/github/aicut_auto/newcut_ai\40_3.mp4
2025-08-07 17:41:11,426 - INFO - ========== 字幕 #40 处理结束 ==========

