2025-08-07 17:41:25,287 - INFO - ========== 字幕 #51 处理开始 ==========
2025-08-07 17:41:25,287 - INFO - 字幕内容: 她拿出自己的奶瓶，要给会长喂下灵芝水，这离奇的治疗方式让所有人瞠目结舌，但她却说这灵芝水包治百病。
2025-08-07 17:41:25,287 - INFO - 字幕序号: [2258, 2273]
2025-08-07 17:41:25,287 - INFO - 音频文件详情:
2025-08-07 17:41:25,287 - INFO -   - 路径: output\51.wav
2025-08-07 17:41:25,287 - INFO -   - 时长: 6.00秒
2025-08-07 17:41:25,287 - INFO -   - 验证音频时长: 6.00秒
2025-08-07 17:41:25,288 - INFO - 字幕时间戳信息:
2025-08-07 17:41:25,288 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:25,288 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:25,288 - INFO -   - 根据生成的音频时长(6.00秒)已调整字幕时间戳
2025-08-07 17:41:25,288 - INFO - ========== 新模式：为字幕 #51 生成4套场景方案 ==========
2025-08-07 17:41:25,288 - INFO - 字幕序号列表: [2258, 2273]
2025-08-07 17:41:25,288 - INFO - 
--- 生成方案 #1：基于字幕序号 #2258 ---
2025-08-07 17:41:25,288 - INFO - 开始为单个字幕序号 #2258 匹配场景，目标时长: 6.00秒
2025-08-07 17:41:25,288 - INFO - 开始查找字幕序号 [2258] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:25,289 - INFO - 找到related_overlap场景: scene_id=2513, 字幕#2258
2025-08-07 17:41:25,289 - INFO - 找到related_overlap场景: scene_id=2514, 字幕#2258
2025-08-07 17:41:25,290 - INFO - 字幕 #2258 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:25,290 - INFO - 字幕序号 #2258 找到 2 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:25,290 - INFO - 选择第一个overlap场景作为起点: scene_id=2513
2025-08-07 17:41:25,290 - INFO - 添加起点场景: scene_id=2513, 时长=0.64秒, 累计时长=0.64秒
2025-08-07 17:41:25,290 - INFO - 起点场景时长不足，需要延伸填充 5.36秒
2025-08-07 17:41:25,290 - INFO - 起点场景在原始列表中的索引: 2512
2025-08-07 17:41:25,290 - INFO - 延伸添加场景: scene_id=2514 (完整时长 0.92秒)
2025-08-07 17:41:25,290 - INFO - 累计时长: 1.56秒
2025-08-07 17:41:25,290 - INFO - 延伸添加场景: scene_id=2515 (完整时长 1.08秒)
2025-08-07 17:41:25,290 - INFO - 累计时长: 2.64秒
2025-08-07 17:41:25,290 - INFO - 延伸添加场景: scene_id=2516 (完整时长 0.72秒)
2025-08-07 17:41:25,290 - INFO - 累计时长: 3.36秒
2025-08-07 17:41:25,290 - INFO - 延伸添加场景: scene_id=2517 (完整时长 1.48秒)
2025-08-07 17:41:25,290 - INFO - 累计时长: 4.84秒
2025-08-07 17:41:25,290 - INFO - 延伸添加场景: scene_id=2518 (裁剪至 1.16秒)
2025-08-07 17:41:25,290 - INFO - 累计时长: 6.00秒
2025-08-07 17:41:25,290 - INFO - 字幕序号 #2258 场景匹配完成，共选择 6 个场景，总时长: 6.00秒
2025-08-07 17:41:25,290 - INFO - 方案 #1 生成成功，包含 6 个场景
2025-08-07 17:41:25,290 - INFO - 新模式：第1套方案的 6 个场景已加入全局已使用集合
2025-08-07 17:41:25,290 - INFO - 
--- 生成方案 #2：基于字幕序号 #2273 ---
2025-08-07 17:41:25,290 - INFO - 开始为单个字幕序号 #2273 匹配场景，目标时长: 6.00秒
2025-08-07 17:41:25,290 - INFO - 开始查找字幕序号 [2273] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:25,291 - INFO - 找到related_overlap场景: scene_id=2526, 字幕#2273
2025-08-07 17:41:25,291 - INFO - 找到related_overlap场景: scene_id=2527, 字幕#2273
2025-08-07 17:41:25,291 - INFO - 字幕 #2273 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:25,291 - INFO - 字幕序号 #2273 找到 2 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:25,291 - INFO - 选择第一个overlap场景作为起点: scene_id=2526
2025-08-07 17:41:25,291 - INFO - 添加起点场景: scene_id=2526, 时长=1.36秒, 累计时长=1.36秒
2025-08-07 17:41:25,291 - INFO - 起点场景时长不足，需要延伸填充 4.64秒
2025-08-07 17:41:25,291 - INFO - 起点场景在原始列表中的索引: 2525
2025-08-07 17:41:25,291 - INFO - 延伸添加场景: scene_id=2527 (完整时长 1.84秒)
2025-08-07 17:41:25,291 - INFO - 累计时长: 3.20秒
2025-08-07 17:41:25,291 - INFO - 延伸添加场景: scene_id=2528 (完整时长 2.36秒)
2025-08-07 17:41:25,291 - INFO - 累计时长: 5.56秒
2025-08-07 17:41:25,291 - INFO - 延伸添加场景: scene_id=2529 (裁剪至 0.44秒)
2025-08-07 17:41:25,292 - INFO - 累计时长: 6.00秒
2025-08-07 17:41:25,292 - INFO - 字幕序号 #2273 场景匹配完成，共选择 4 个场景，总时长: 6.00秒
2025-08-07 17:41:25,292 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-08-07 17:41:25,292 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:25,292 - INFO - ========== 当前模式：为字幕 #51 生成 1 套场景方案 ==========
2025-08-07 17:41:25,292 - INFO - 开始查找字幕序号 [2258, 2273] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:25,292 - INFO - 找到related_overlap场景: scene_id=2513, 字幕#2258
2025-08-07 17:41:25,292 - INFO - 找到related_overlap场景: scene_id=2514, 字幕#2258
2025-08-07 17:41:25,292 - INFO - 找到related_overlap场景: scene_id=2526, 字幕#2273
2025-08-07 17:41:25,292 - INFO - 找到related_overlap场景: scene_id=2527, 字幕#2273
2025-08-07 17:41:25,293 - INFO - 字幕 #2258 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:25,293 - INFO - 字幕 #2273 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:25,293 - INFO - 共收集 4 个未使用的overlap场景和 0 个未使用的between场景
2025-08-07 17:41:25,293 - INFO - 开始生成方案 #1
2025-08-07 17:41:25,293 - INFO - 方案 #1: 为字幕#2258选择初始化overlap场景id=2513
2025-08-07 17:41:25,293 - INFO - 方案 #1: 为字幕#2273选择初始化overlap场景id=2526
2025-08-07 17:41:25,293 - INFO - 方案 #1: 初始选择后，当前总时长=2.00秒
2025-08-07 17:41:25,293 - INFO - 方案 #1: 额外添加overlap场景id=2514, 当前总时长=2.92秒
2025-08-07 17:41:25,293 - INFO - 方案 #1: 额外添加overlap场景id=2527, 当前总时长=4.76秒
2025-08-07 17:41:25,293 - INFO - 方案 #1: 额外between选择后，当前总时长=4.76秒
2025-08-07 17:41:25,293 - INFO - 方案 #1: 场景总时长(4.76秒)小于音频时长(6.00秒)，需要延伸填充
2025-08-07 17:41:25,293 - INFO - 方案 #1: 最后一个场景ID: 2527
2025-08-07 17:41:25,293 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 2526
2025-08-07 17:41:25,293 - INFO - 方案 #1: 需要填充时长: 1.24秒
2025-08-07 17:41:25,293 - INFO - 方案 #1: 追加场景 scene_id=2528 (裁剪至 1.24秒)
2025-08-07 17:41:25,293 - INFO - 方案 #1: 成功填充至目标时长
2025-08-07 17:41:25,293 - INFO - 方案 #1 调整/填充后最终总时长: 6.00秒
2025-08-07 17:41:25,293 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:25,293 - INFO - ========== 当前模式：字幕 #51 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:25,293 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:25,293 - INFO - ========== 新模式：字幕 #51 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:25,293 - INFO - 
----- 处理字幕 #51 的方案 #1 -----
2025-08-07 17:41:25,293 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\51_1.mp4
2025-08-07 17:41:25,294 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpztez_6yp
2025-08-07 17:41:25,294 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2513.mp4 (确认存在: True)
2025-08-07 17:41:25,294 - INFO - 添加场景ID=2513，时长=0.64秒，累计时长=0.64秒
2025-08-07 17:41:25,294 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2514.mp4 (确认存在: True)
2025-08-07 17:41:25,294 - INFO - 添加场景ID=2514，时长=0.92秒，累计时长=1.56秒
2025-08-07 17:41:25,294 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2515.mp4 (确认存在: True)
2025-08-07 17:41:25,294 - INFO - 添加场景ID=2515，时长=1.08秒，累计时长=2.64秒
2025-08-07 17:41:25,294 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2516.mp4 (确认存在: True)
2025-08-07 17:41:25,294 - INFO - 添加场景ID=2516，时长=0.72秒，累计时长=3.36秒
2025-08-07 17:41:25,294 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2517.mp4 (确认存在: True)
2025-08-07 17:41:25,294 - INFO - 添加场景ID=2517，时长=1.48秒，累计时长=4.84秒
2025-08-07 17:41:25,294 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2518.mp4 (确认存在: True)
2025-08-07 17:41:25,294 - INFO - 添加场景ID=2518，时长=1.24秒，累计时长=6.08秒
2025-08-07 17:41:25,294 - INFO - 准备合并 6 个场景文件，总时长约 6.08秒
2025-08-07 17:41:25,294 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2513.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2514.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2515.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2516.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2517.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2518.mp4'

2025-08-07 17:41:25,294 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpztez_6yp\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpztez_6yp\temp_combined.mp4
2025-08-07 17:41:25,458 - INFO - 合并后的视频时长: 6.22秒，目标音频时长: 6.00秒
2025-08-07 17:41:25,458 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpztez_6yp\temp_combined.mp4 -ss 0 -to 5.998 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\51_1.mp4
2025-08-07 17:41:25,766 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:25,766 - INFO - 目标音频时长: 6.00秒
2025-08-07 17:41:25,766 - INFO - 实际视频时长: 6.02秒
2025-08-07 17:41:25,766 - INFO - 时长差异: 0.02秒 (0.42%)
2025-08-07 17:41:25,766 - INFO - ==========================================
2025-08-07 17:41:25,766 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:25,766 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\51_1.mp4
2025-08-07 17:41:25,767 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpztez_6yp
2025-08-07 17:41:25,808 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:25,808 - INFO -   - 音频时长: 6.00秒
2025-08-07 17:41:25,808 - INFO -   - 视频时长: 6.02秒
2025-08-07 17:41:25,809 - INFO -   - 时长差异: 0.02秒 (0.42%)
2025-08-07 17:41:25,809 - INFO - 
----- 处理字幕 #51 的方案 #2 -----
2025-08-07 17:41:25,809 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\51_2.mp4
2025-08-07 17:41:25,809 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpho0p78r9
2025-08-07 17:41:25,809 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2526.mp4 (确认存在: True)
2025-08-07 17:41:25,809 - INFO - 添加场景ID=2526，时长=1.36秒，累计时长=1.36秒
2025-08-07 17:41:25,810 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2527.mp4 (确认存在: True)
2025-08-07 17:41:25,810 - INFO - 添加场景ID=2527，时长=1.84秒，累计时长=3.20秒
2025-08-07 17:41:25,810 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2528.mp4 (确认存在: True)
2025-08-07 17:41:25,810 - INFO - 添加场景ID=2528，时长=2.36秒，累计时长=5.56秒
2025-08-07 17:41:25,810 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2529.mp4 (确认存在: True)
2025-08-07 17:41:25,810 - INFO - 添加场景ID=2529，时长=1.12秒，累计时长=6.68秒
2025-08-07 17:41:25,810 - INFO - 准备合并 4 个场景文件，总时长约 6.68秒
2025-08-07 17:41:25,810 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2526.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2527.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2528.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2529.mp4'

2025-08-07 17:41:25,810 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpho0p78r9\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpho0p78r9\temp_combined.mp4
2025-08-07 17:41:25,957 - INFO - 合并后的视频时长: 6.77秒，目标音频时长: 6.00秒
2025-08-07 17:41:25,957 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpho0p78r9\temp_combined.mp4 -ss 0 -to 5.998 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\51_2.mp4
2025-08-07 17:41:26,313 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:26,313 - INFO - 目标音频时长: 6.00秒
2025-08-07 17:41:26,313 - INFO - 实际视频时长: 6.02秒
2025-08-07 17:41:26,313 - INFO - 时长差异: 0.02秒 (0.42%)
2025-08-07 17:41:26,313 - INFO - ==========================================
2025-08-07 17:41:26,313 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:26,313 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\51_2.mp4
2025-08-07 17:41:26,313 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpho0p78r9
2025-08-07 17:41:26,356 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:26,356 - INFO -   - 音频时长: 6.00秒
2025-08-07 17:41:26,356 - INFO -   - 视频时长: 6.02秒
2025-08-07 17:41:26,356 - INFO -   - 时长差异: 0.02秒 (0.42%)
2025-08-07 17:41:26,356 - INFO - 
----- 处理字幕 #51 的方案 #3 -----
2025-08-07 17:41:26,356 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\51_3.mp4
2025-08-07 17:41:26,357 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpoep82wuy
2025-08-07 17:41:26,357 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2513.mp4 (确认存在: True)
2025-08-07 17:41:26,357 - INFO - 添加场景ID=2513，时长=0.64秒，累计时长=0.64秒
2025-08-07 17:41:26,357 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2526.mp4 (确认存在: True)
2025-08-07 17:41:26,357 - INFO - 添加场景ID=2526，时长=1.36秒，累计时长=2.00秒
2025-08-07 17:41:26,357 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2514.mp4 (确认存在: True)
2025-08-07 17:41:26,357 - INFO - 添加场景ID=2514，时长=0.92秒，累计时长=2.92秒
2025-08-07 17:41:26,357 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2527.mp4 (确认存在: True)
2025-08-07 17:41:26,358 - INFO - 添加场景ID=2527，时长=1.84秒，累计时长=4.76秒
2025-08-07 17:41:26,358 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2528.mp4 (确认存在: True)
2025-08-07 17:41:26,358 - INFO - 添加场景ID=2528，时长=2.36秒，累计时长=7.12秒
2025-08-07 17:41:26,358 - INFO - 准备合并 5 个场景文件，总时长约 7.12秒
2025-08-07 17:41:26,358 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2513.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2526.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2514.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2527.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2528.mp4'

2025-08-07 17:41:26,358 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpoep82wuy\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpoep82wuy\temp_combined.mp4
2025-08-07 17:41:26,504 - INFO - 合并后的视频时长: 7.24秒，目标音频时长: 6.00秒
2025-08-07 17:41:26,504 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpoep82wuy\temp_combined.mp4 -ss 0 -to 5.998 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\51_3.mp4
2025-08-07 17:41:26,819 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:26,819 - INFO - 目标音频时长: 6.00秒
2025-08-07 17:41:26,819 - INFO - 实际视频时长: 6.02秒
2025-08-07 17:41:26,819 - INFO - 时长差异: 0.02秒 (0.42%)
2025-08-07 17:41:26,819 - INFO - ==========================================
2025-08-07 17:41:26,819 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:26,819 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\51_3.mp4
2025-08-07 17:41:26,819 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpoep82wuy
2025-08-07 17:41:26,862 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:26,862 - INFO -   - 音频时长: 6.00秒
2025-08-07 17:41:26,862 - INFO -   - 视频时长: 6.02秒
2025-08-07 17:41:26,862 - INFO -   - 时长差异: 0.02秒 (0.42%)
2025-08-07 17:41:26,862 - INFO - 
字幕 #51 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:26,862 - INFO - 生成的视频文件:
2025-08-07 17:41:26,862 - INFO -   1. F:/github/aicut_auto/newcut_ai\51_1.mp4
2025-08-07 17:41:26,862 - INFO -   2. F:/github/aicut_auto/newcut_ai\51_2.mp4
2025-08-07 17:41:26,862 - INFO -   3. F:/github/aicut_auto/newcut_ai\51_3.mp4
2025-08-07 17:41:26,862 - INFO - ========== 字幕 #51 处理结束 ==========

