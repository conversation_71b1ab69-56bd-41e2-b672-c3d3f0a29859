2025-08-07 17:40:31,085 - INFO - ========== 字幕 #13 处理开始 ==========
2025-08-07 17:40:31,085 - INFO - 字幕内容: 男人早已心灰意冷，访遍名医都无济于事，根本不信一个小孩子能治好他的腿。
2025-08-07 17:40:31,085 - INFO - 字幕序号: [285, 289]
2025-08-07 17:40:31,085 - INFO - 音频文件详情:
2025-08-07 17:40:31,085 - INFO -   - 路径: output\13.wav
2025-08-07 17:40:31,085 - INFO -   - 时长: 4.77秒
2025-08-07 17:40:31,085 - INFO -   - 验证音频时长: 4.77秒
2025-08-07 17:40:31,085 - INFO - 字幕时间戳信息:
2025-08-07 17:40:31,085 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:31,085 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:31,085 - INFO -   - 根据生成的音频时长(4.77秒)已调整字幕时间戳
2025-08-07 17:40:31,085 - INFO - ========== 新模式：为字幕 #13 生成4套场景方案 ==========
2025-08-07 17:40:31,085 - INFO - 字幕序号列表: [285, 289]
2025-08-07 17:40:31,085 - INFO - 
--- 生成方案 #1：基于字幕序号 #285 ---
2025-08-07 17:40:31,085 - INFO - 开始为单个字幕序号 #285 匹配场景，目标时长: 4.77秒
2025-08-07 17:40:31,085 - INFO - 开始查找字幕序号 [285] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:31,085 - INFO - 找到related_overlap场景: scene_id=420, 字幕#285
2025-08-07 17:40:31,087 - INFO - 字幕 #285 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:31,087 - INFO - 字幕序号 #285 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:31,087 - INFO - 选择第一个overlap场景作为起点: scene_id=420
2025-08-07 17:40:31,087 - INFO - 添加起点场景: scene_id=420, 时长=4.32秒, 累计时长=4.32秒
2025-08-07 17:40:31,087 - INFO - 起点场景时长不足，需要延伸填充 0.45秒
2025-08-07 17:40:31,087 - INFO - 起点场景在原始列表中的索引: 419
2025-08-07 17:40:31,087 - INFO - 延伸添加场景: scene_id=421 (裁剪至 0.45秒)
2025-08-07 17:40:31,087 - INFO - 累计时长: 4.77秒
2025-08-07 17:40:31,087 - INFO - 字幕序号 #285 场景匹配完成，共选择 2 个场景，总时长: 4.77秒
2025-08-07 17:40:31,087 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-08-07 17:40:31,087 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-08-07 17:40:31,087 - INFO - 
--- 生成方案 #2：基于字幕序号 #289 ---
2025-08-07 17:40:31,087 - INFO - 开始为单个字幕序号 #289 匹配场景，目标时长: 4.77秒
2025-08-07 17:40:31,087 - INFO - 开始查找字幕序号 [289] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:31,087 - INFO - 找到related_overlap场景: scene_id=422, 字幕#289
2025-08-07 17:40:31,088 - INFO - 字幕 #289 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:31,088 - INFO - 字幕序号 #289 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:31,088 - INFO - 选择第一个overlap场景作为起点: scene_id=422
2025-08-07 17:40:31,088 - INFO - 添加起点场景: scene_id=422, 时长=1.72秒, 累计时长=1.72秒
2025-08-07 17:40:31,088 - INFO - 起点场景时长不足，需要延伸填充 3.05秒
2025-08-07 17:40:31,088 - INFO - 起点场景在原始列表中的索引: 421
2025-08-07 17:40:31,088 - INFO - 延伸添加场景: scene_id=423 (完整时长 1.72秒)
2025-08-07 17:40:31,088 - INFO - 累计时长: 3.44秒
2025-08-07 17:40:31,088 - INFO - 延伸添加场景: scene_id=424 (完整时长 1.24秒)
2025-08-07 17:40:31,088 - INFO - 累计时长: 4.68秒
2025-08-07 17:40:31,088 - INFO - 延伸添加场景: scene_id=425 (裁剪至 0.09秒)
2025-08-07 17:40:31,088 - INFO - 累计时长: 4.77秒
2025-08-07 17:40:31,088 - INFO - 字幕序号 #289 场景匹配完成，共选择 4 个场景，总时长: 4.77秒
2025-08-07 17:40:31,088 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-08-07 17:40:31,088 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:31,088 - INFO - ========== 当前模式：为字幕 #13 生成 1 套场景方案 ==========
2025-08-07 17:40:31,088 - INFO - 开始查找字幕序号 [285, 289] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:31,088 - INFO - 找到related_overlap场景: scene_id=420, 字幕#285
2025-08-07 17:40:31,088 - INFO - 找到related_overlap场景: scene_id=422, 字幕#289
2025-08-07 17:40:31,089 - INFO - 字幕 #285 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:31,089 - INFO - 字幕 #289 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:31,089 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-08-07 17:40:31,089 - INFO - 开始生成方案 #1
2025-08-07 17:40:31,090 - INFO - 方案 #1: 为字幕#285选择初始化overlap场景id=420
2025-08-07 17:40:31,090 - INFO - 方案 #1: 为字幕#289选择初始化overlap场景id=422
2025-08-07 17:40:31,090 - INFO - 方案 #1: 初始选择后，当前总时长=6.04秒
2025-08-07 17:40:31,090 - INFO - 方案 #1: 额外between选择后，当前总时长=6.04秒
2025-08-07 17:40:31,090 - INFO - 方案 #1: 场景总时长(6.04秒)大于音频时长(4.77秒)，需要裁剪
2025-08-07 17:40:31,090 - INFO - 调整前总时长: 6.04秒, 目标时长: 4.77秒
2025-08-07 17:40:31,090 - INFO - 需要裁剪 1.27秒
2025-08-07 17:40:31,090 - INFO - 裁剪最长场景ID=420：从4.32秒裁剪至3.05秒
2025-08-07 17:40:31,090 - INFO - 调整后总时长: 4.77秒，与目标时长差异: 0.00秒
2025-08-07 17:40:31,090 - INFO - 方案 #1 调整/填充后最终总时长: 4.77秒
2025-08-07 17:40:31,090 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:31,090 - INFO - ========== 当前模式：字幕 #13 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:31,090 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:31,090 - INFO - ========== 新模式：字幕 #13 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:31,090 - INFO - 
----- 处理字幕 #13 的方案 #1 -----
2025-08-07 17:40:31,090 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\13_1.mp4
2025-08-07 17:40:31,090 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpiutmqr46
2025-08-07 17:40:31,091 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\420.mp4 (确认存在: True)
2025-08-07 17:40:31,091 - INFO - 添加场景ID=420，时长=4.32秒，累计时长=4.32秒
2025-08-07 17:40:31,091 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\421.mp4 (确认存在: True)
2025-08-07 17:40:31,091 - INFO - 添加场景ID=421，时长=2.28秒，累计时长=6.60秒
2025-08-07 17:40:31,091 - INFO - 准备合并 2 个场景文件，总时长约 6.60秒
2025-08-07 17:40:31,091 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/420.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/421.mp4'

2025-08-07 17:40:31,091 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpiutmqr46\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpiutmqr46\temp_combined.mp4
2025-08-07 17:40:31,227 - INFO - 合并后的视频时长: 6.65秒，目标音频时长: 4.77秒
2025-08-07 17:40:31,227 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpiutmqr46\temp_combined.mp4 -ss 0 -to 4.767 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\13_1.mp4
2025-08-07 17:40:31,494 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:31,494 - INFO - 目标音频时长: 4.77秒
2025-08-07 17:40:31,494 - INFO - 实际视频时长: 4.82秒
2025-08-07 17:40:31,494 - INFO - 时长差异: 0.06秒 (1.17%)
2025-08-07 17:40:31,494 - INFO - ==========================================
2025-08-07 17:40:31,494 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:31,494 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\13_1.mp4
2025-08-07 17:40:31,495 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpiutmqr46
2025-08-07 17:40:31,542 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:31,542 - INFO -   - 音频时长: 4.77秒
2025-08-07 17:40:31,542 - INFO -   - 视频时长: 4.82秒
2025-08-07 17:40:31,542 - INFO -   - 时长差异: 0.06秒 (1.17%)
2025-08-07 17:40:31,542 - INFO - 
----- 处理字幕 #13 的方案 #2 -----
2025-08-07 17:40:31,542 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\13_2.mp4
2025-08-07 17:40:31,542 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp504upc0f
2025-08-07 17:40:31,543 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\422.mp4 (确认存在: True)
2025-08-07 17:40:31,543 - INFO - 添加场景ID=422，时长=1.72秒，累计时长=1.72秒
2025-08-07 17:40:31,543 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\423.mp4 (确认存在: True)
2025-08-07 17:40:31,543 - INFO - 添加场景ID=423，时长=1.72秒，累计时长=3.44秒
2025-08-07 17:40:31,543 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\424.mp4 (确认存在: True)
2025-08-07 17:40:31,543 - INFO - 添加场景ID=424，时长=1.24秒，累计时长=4.68秒
2025-08-07 17:40:31,543 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\425.mp4 (确认存在: True)
2025-08-07 17:40:31,543 - INFO - 添加场景ID=425，时长=1.56秒，累计时长=6.24秒
2025-08-07 17:40:31,543 - INFO - 准备合并 4 个场景文件，总时长约 6.24秒
2025-08-07 17:40:31,543 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/422.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/423.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/424.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/425.mp4'

2025-08-07 17:40:31,543 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp504upc0f\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp504upc0f\temp_combined.mp4
2025-08-07 17:40:31,698 - INFO - 合并后的视频时长: 6.33秒，目标音频时长: 4.77秒
2025-08-07 17:40:31,698 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp504upc0f\temp_combined.mp4 -ss 0 -to 4.767 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\13_2.mp4
2025-08-07 17:40:32,005 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:32,005 - INFO - 目标音频时长: 4.77秒
2025-08-07 17:40:32,005 - INFO - 实际视频时长: 4.82秒
2025-08-07 17:40:32,007 - INFO - 时长差异: 0.06秒 (1.17%)
2025-08-07 17:40:32,007 - INFO - ==========================================
2025-08-07 17:40:32,007 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:32,007 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\13_2.mp4
2025-08-07 17:40:32,007 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp504upc0f
2025-08-07 17:40:32,050 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:32,050 - INFO -   - 音频时长: 4.77秒
2025-08-07 17:40:32,050 - INFO -   - 视频时长: 4.82秒
2025-08-07 17:40:32,050 - INFO -   - 时长差异: 0.06秒 (1.17%)
2025-08-07 17:40:32,050 - INFO - 
----- 处理字幕 #13 的方案 #3 -----
2025-08-07 17:40:32,050 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\13_3.mp4
2025-08-07 17:40:32,052 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpl5cebd06
2025-08-07 17:40:32,052 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\420.mp4 (确认存在: True)
2025-08-07 17:40:32,052 - INFO - 添加场景ID=420，时长=4.32秒，累计时长=4.32秒
2025-08-07 17:40:32,052 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\422.mp4 (确认存在: True)
2025-08-07 17:40:32,052 - INFO - 添加场景ID=422，时长=1.72秒，累计时长=6.04秒
2025-08-07 17:40:32,052 - INFO - 准备合并 2 个场景文件，总时长约 6.04秒
2025-08-07 17:40:32,052 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/420.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/422.mp4'

2025-08-07 17:40:32,052 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpl5cebd06\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpl5cebd06\temp_combined.mp4
2025-08-07 17:40:32,193 - INFO - 合并后的视频时长: 6.09秒，目标音频时长: 4.77秒
2025-08-07 17:40:32,193 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpl5cebd06\temp_combined.mp4 -ss 0 -to 4.767 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\13_3.mp4
2025-08-07 17:40:32,480 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:32,481 - INFO - 目标音频时长: 4.77秒
2025-08-07 17:40:32,481 - INFO - 实际视频时长: 4.82秒
2025-08-07 17:40:32,481 - INFO - 时长差异: 0.06秒 (1.17%)
2025-08-07 17:40:32,481 - INFO - ==========================================
2025-08-07 17:40:32,481 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:32,481 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\13_3.mp4
2025-08-07 17:40:32,481 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpl5cebd06
2025-08-07 17:40:32,529 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:32,529 - INFO -   - 音频时长: 4.77秒
2025-08-07 17:40:32,529 - INFO -   - 视频时长: 4.82秒
2025-08-07 17:40:32,529 - INFO -   - 时长差异: 0.06秒 (1.17%)
2025-08-07 17:40:32,530 - INFO - 
字幕 #13 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:32,530 - INFO - 生成的视频文件:
2025-08-07 17:40:32,530 - INFO -   1. F:/github/aicut_auto/newcut_ai\13_1.mp4
2025-08-07 17:40:32,530 - INFO -   2. F:/github/aicut_auto/newcut_ai\13_2.mp4
2025-08-07 17:40:32,531 - INFO -   3. F:/github/aicut_auto/newcut_ai\13_3.mp4
2025-08-07 17:40:32,531 - INFO - ========== 字幕 #13 处理结束 ==========

