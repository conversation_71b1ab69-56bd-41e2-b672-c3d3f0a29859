2025-08-07 17:40:58,976 - INFO - ========== 字幕 #32 处理开始 ==========
2025-08-07 17:40:58,976 - INFO - 字幕内容: 就在汽车即将撞上的一瞬间，女孩再次出手，直接让整辆汽车凭空飞起，悬在半空。
2025-08-07 17:40:58,976 - INFO - 字幕序号: [969, 971]
2025-08-07 17:40:58,976 - INFO - 音频文件详情:
2025-08-07 17:40:58,976 - INFO -   - 路径: output\32.wav
2025-08-07 17:40:58,976 - INFO -   - 时长: 3.77秒
2025-08-07 17:40:58,976 - INFO -   - 验证音频时长: 3.77秒
2025-08-07 17:40:58,976 - INFO - 字幕时间戳信息:
2025-08-07 17:40:58,978 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:58,978 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:58,978 - INFO -   - 根据生成的音频时长(3.77秒)已调整字幕时间戳
2025-08-07 17:40:58,978 - INFO - ========== 新模式：为字幕 #32 生成4套场景方案 ==========
2025-08-07 17:40:58,978 - INFO - 字幕序号列表: [969, 971]
2025-08-07 17:40:58,978 - INFO - 
--- 生成方案 #1：基于字幕序号 #969 ---
2025-08-07 17:40:58,978 - INFO - 开始为单个字幕序号 #969 匹配场景，目标时长: 3.77秒
2025-08-07 17:40:58,978 - INFO - 开始查找字幕序号 [969] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:58,978 - INFO - 找到related_overlap场景: scene_id=1188, 字幕#969
2025-08-07 17:40:58,979 - INFO - 找到related_between场景: scene_id=1182, 字幕#969
2025-08-07 17:40:58,979 - INFO - 找到related_between场景: scene_id=1183, 字幕#969
2025-08-07 17:40:58,979 - INFO - 找到related_between场景: scene_id=1184, 字幕#969
2025-08-07 17:40:58,979 - INFO - 找到related_between场景: scene_id=1185, 字幕#969
2025-08-07 17:40:58,979 - INFO - 找到related_between场景: scene_id=1186, 字幕#969
2025-08-07 17:40:58,979 - INFO - 找到related_between场景: scene_id=1187, 字幕#969
2025-08-07 17:40:58,979 - INFO - 找到related_between场景: scene_id=1189, 字幕#969
2025-08-07 17:40:58,979 - INFO - 找到related_between场景: scene_id=1190, 字幕#969
2025-08-07 17:40:58,979 - INFO - 找到related_between场景: scene_id=1191, 字幕#969
2025-08-07 17:40:58,979 - INFO - 找到related_between场景: scene_id=1192, 字幕#969
2025-08-07 17:40:58,979 - INFO - 找到related_between场景: scene_id=1193, 字幕#969
2025-08-07 17:40:58,979 - INFO - 找到related_between场景: scene_id=1194, 字幕#969
2025-08-07 17:40:58,979 - INFO - 找到related_between场景: scene_id=1195, 字幕#969
2025-08-07 17:40:58,979 - INFO - 找到related_between场景: scene_id=1196, 字幕#969
2025-08-07 17:40:58,979 - INFO - 找到related_between场景: scene_id=1197, 字幕#969
2025-08-07 17:40:58,979 - INFO - 找到related_between场景: scene_id=1198, 字幕#969
2025-08-07 17:40:58,979 - INFO - 找到related_between场景: scene_id=1199, 字幕#969
2025-08-07 17:40:58,979 - INFO - 字幕 #969 找到 1 个overlap场景, 17 个between场景
2025-08-07 17:40:58,979 - INFO - 字幕序号 #969 找到 1 个可用overlap场景, 17 个可用between场景
2025-08-07 17:40:58,979 - INFO - 选择第一个overlap场景作为起点: scene_id=1188
2025-08-07 17:40:58,979 - INFO - 添加起点场景: scene_id=1188, 时长=1.56秒, 累计时长=1.56秒
2025-08-07 17:40:58,979 - INFO - 起点场景时长不足，需要延伸填充 2.21秒
2025-08-07 17:40:58,980 - INFO - 起点场景在原始列表中的索引: 1187
2025-08-07 17:40:58,980 - INFO - 延伸添加场景: scene_id=1189 (完整时长 1.48秒)
2025-08-07 17:40:58,980 - INFO - 累计时长: 3.04秒
2025-08-07 17:40:58,980 - INFO - 延伸添加场景: scene_id=1190 (完整时长 0.40秒)
2025-08-07 17:40:58,980 - INFO - 累计时长: 3.44秒
2025-08-07 17:40:58,980 - INFO - 延伸添加场景: scene_id=1191 (裁剪至 0.33秒)
2025-08-07 17:40:58,980 - INFO - 累计时长: 3.77秒
2025-08-07 17:40:58,980 - INFO - 字幕序号 #969 场景匹配完成，共选择 4 个场景，总时长: 3.77秒
2025-08-07 17:40:58,980 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-08-07 17:40:58,980 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-08-07 17:40:58,980 - INFO - 
--- 生成方案 #2：基于字幕序号 #971 ---
2025-08-07 17:40:58,980 - INFO - 开始为单个字幕序号 #971 匹配场景，目标时长: 3.77秒
2025-08-07 17:40:58,980 - INFO - 开始查找字幕序号 [971] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:58,980 - INFO - 找到related_overlap场景: scene_id=1201, 字幕#971
2025-08-07 17:40:58,981 - INFO - 字幕 #971 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:58,981 - INFO - 字幕序号 #971 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:58,981 - INFO - 选择第一个overlap场景作为起点: scene_id=1201
2025-08-07 17:40:58,981 - INFO - 添加起点场景: scene_id=1201, 时长=1.72秒, 累计时长=1.72秒
2025-08-07 17:40:58,981 - INFO - 起点场景时长不足，需要延伸填充 2.05秒
2025-08-07 17:40:58,981 - INFO - 起点场景在原始列表中的索引: 1200
2025-08-07 17:40:58,981 - INFO - 延伸添加场景: scene_id=1202 (完整时长 1.32秒)
2025-08-07 17:40:58,981 - INFO - 累计时长: 3.04秒
2025-08-07 17:40:58,981 - INFO - 延伸添加场景: scene_id=1203 (裁剪至 0.73秒)
2025-08-07 17:40:58,981 - INFO - 累计时长: 3.77秒
2025-08-07 17:40:58,981 - INFO - 字幕序号 #971 场景匹配完成，共选择 3 个场景，总时长: 3.77秒
2025-08-07 17:40:58,981 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-08-07 17:40:58,981 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:58,981 - INFO - ========== 当前模式：为字幕 #32 生成 1 套场景方案 ==========
2025-08-07 17:40:58,981 - INFO - 开始查找字幕序号 [969, 971] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:58,981 - INFO - 找到related_overlap场景: scene_id=1188, 字幕#969
2025-08-07 17:40:58,981 - INFO - 找到related_overlap场景: scene_id=1201, 字幕#971
2025-08-07 17:40:58,982 - INFO - 找到related_between场景: scene_id=1182, 字幕#969
2025-08-07 17:40:58,982 - INFO - 找到related_between场景: scene_id=1183, 字幕#969
2025-08-07 17:40:58,982 - INFO - 找到related_between场景: scene_id=1184, 字幕#969
2025-08-07 17:40:58,982 - INFO - 找到related_between场景: scene_id=1185, 字幕#969
2025-08-07 17:40:58,982 - INFO - 找到related_between场景: scene_id=1186, 字幕#969
2025-08-07 17:40:58,982 - INFO - 找到related_between场景: scene_id=1187, 字幕#969
2025-08-07 17:40:58,982 - INFO - 找到related_between场景: scene_id=1189, 字幕#969
2025-08-07 17:40:58,982 - INFO - 找到related_between场景: scene_id=1190, 字幕#969
2025-08-07 17:40:58,982 - INFO - 找到related_between场景: scene_id=1191, 字幕#969
2025-08-07 17:40:58,982 - INFO - 找到related_between场景: scene_id=1192, 字幕#969
2025-08-07 17:40:58,982 - INFO - 找到related_between场景: scene_id=1193, 字幕#969
2025-08-07 17:40:58,982 - INFO - 找到related_between场景: scene_id=1194, 字幕#969
2025-08-07 17:40:58,982 - INFO - 找到related_between场景: scene_id=1195, 字幕#969
2025-08-07 17:40:58,982 - INFO - 找到related_between场景: scene_id=1196, 字幕#969
2025-08-07 17:40:58,982 - INFO - 找到related_between场景: scene_id=1197, 字幕#969
2025-08-07 17:40:58,982 - INFO - 找到related_between场景: scene_id=1198, 字幕#969
2025-08-07 17:40:58,982 - INFO - 找到related_between场景: scene_id=1199, 字幕#969
2025-08-07 17:40:58,982 - INFO - 字幕 #969 找到 1 个overlap场景, 17 个between场景
2025-08-07 17:40:58,982 - INFO - 字幕 #971 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:58,982 - INFO - 共收集 2 个未使用的overlap场景和 17 个未使用的between场景
2025-08-07 17:40:58,982 - INFO - 开始生成方案 #1
2025-08-07 17:40:58,982 - INFO - 方案 #1: 为字幕#969选择初始化overlap场景id=1188
2025-08-07 17:40:58,982 - INFO - 方案 #1: 为字幕#971选择初始化overlap场景id=1201
2025-08-07 17:40:58,982 - INFO - 方案 #1: 初始选择后，当前总时长=3.28秒
2025-08-07 17:40:58,982 - INFO - 方案 #1: 额外between选择后，当前总时长=3.28秒
2025-08-07 17:40:58,983 - INFO - 方案 #1: 额外添加between场景id=1193, 当前总时长=4.32秒
2025-08-07 17:40:58,983 - INFO - 方案 #1: 场景总时长(4.32秒)大于音频时长(3.77秒)，需要裁剪
2025-08-07 17:40:58,983 - INFO - 调整前总时长: 4.32秒, 目标时长: 3.77秒
2025-08-07 17:40:58,983 - INFO - 需要裁剪 0.55秒
2025-08-07 17:40:58,983 - INFO - 裁剪最长场景ID=1201：从1.72秒裁剪至1.17秒
2025-08-07 17:40:58,983 - INFO - 调整后总时长: 3.77秒，与目标时长差异: 0.00秒
2025-08-07 17:40:58,983 - INFO - 方案 #1 调整/填充后最终总时长: 3.77秒
2025-08-07 17:40:58,983 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:58,983 - INFO - ========== 当前模式：字幕 #32 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:58,983 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:58,983 - INFO - ========== 新模式：字幕 #32 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:58,983 - INFO - 
----- 处理字幕 #32 的方案 #1 -----
2025-08-07 17:40:58,983 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\32_1.mp4
2025-08-07 17:40:58,983 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmps22b1103
2025-08-07 17:40:58,984 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1188.mp4 (确认存在: True)
2025-08-07 17:40:58,984 - INFO - 添加场景ID=1188，时长=1.56秒，累计时长=1.56秒
2025-08-07 17:40:58,984 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1189.mp4 (确认存在: True)
2025-08-07 17:40:58,984 - INFO - 添加场景ID=1189，时长=1.48秒，累计时长=3.04秒
2025-08-07 17:40:58,984 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1190.mp4 (确认存在: True)
2025-08-07 17:40:58,984 - INFO - 添加场景ID=1190，时长=0.40秒，累计时长=3.44秒
2025-08-07 17:40:58,984 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1191.mp4 (确认存在: True)
2025-08-07 17:40:58,984 - INFO - 添加场景ID=1191，时长=0.52秒，累计时长=3.96秒
2025-08-07 17:40:58,984 - INFO - 准备合并 4 个场景文件，总时长约 3.96秒
2025-08-07 17:40:58,984 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1188.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1189.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1190.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1191.mp4'

2025-08-07 17:40:58,984 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmps22b1103\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmps22b1103\temp_combined.mp4
2025-08-07 17:40:59,137 - INFO - 合并后的视频时长: 4.05秒，目标音频时长: 3.77秒
2025-08-07 17:40:59,137 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmps22b1103\temp_combined.mp4 -ss 0 -to 3.772 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\32_1.mp4
2025-08-07 17:40:59,434 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:59,434 - INFO - 目标音频时长: 3.77秒
2025-08-07 17:40:59,434 - INFO - 实际视频时长: 3.82秒
2025-08-07 17:40:59,434 - INFO - 时长差异: 0.05秒 (1.35%)
2025-08-07 17:40:59,434 - INFO - ==========================================
2025-08-07 17:40:59,434 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:59,434 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\32_1.mp4
2025-08-07 17:40:59,434 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmps22b1103
2025-08-07 17:40:59,479 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:59,479 - INFO -   - 音频时长: 3.77秒
2025-08-07 17:40:59,479 - INFO -   - 视频时长: 3.82秒
2025-08-07 17:40:59,479 - INFO -   - 时长差异: 0.05秒 (1.35%)
2025-08-07 17:40:59,479 - INFO - 
----- 处理字幕 #32 的方案 #2 -----
2025-08-07 17:40:59,479 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\32_2.mp4
2025-08-07 17:40:59,480 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbpx7i71t
2025-08-07 17:40:59,480 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1201.mp4 (确认存在: True)
2025-08-07 17:40:59,480 - INFO - 添加场景ID=1201，时长=1.72秒，累计时长=1.72秒
2025-08-07 17:40:59,480 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1202.mp4 (确认存在: True)
2025-08-07 17:40:59,480 - INFO - 添加场景ID=1202，时长=1.32秒，累计时长=3.04秒
2025-08-07 17:40:59,480 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1203.mp4 (确认存在: True)
2025-08-07 17:40:59,480 - INFO - 添加场景ID=1203，时长=1.84秒，累计时长=4.88秒
2025-08-07 17:40:59,480 - INFO - 准备合并 3 个场景文件，总时长约 4.88秒
2025-08-07 17:40:59,481 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1201.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1202.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1203.mp4'

2025-08-07 17:40:59,481 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpbpx7i71t\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpbpx7i71t\temp_combined.mp4
2025-08-07 17:40:59,639 - INFO - 合并后的视频时长: 4.95秒，目标音频时长: 3.77秒
2025-08-07 17:40:59,639 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpbpx7i71t\temp_combined.mp4 -ss 0 -to 3.772 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\32_2.mp4
2025-08-07 17:40:59,924 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:59,924 - INFO - 目标音频时长: 3.77秒
2025-08-07 17:40:59,924 - INFO - 实际视频时长: 3.82秒
2025-08-07 17:40:59,924 - INFO - 时长差异: 0.05秒 (1.35%)
2025-08-07 17:40:59,925 - INFO - ==========================================
2025-08-07 17:40:59,925 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:59,925 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\32_2.mp4
2025-08-07 17:40:59,925 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbpx7i71t
2025-08-07 17:40:59,968 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:59,968 - INFO -   - 音频时长: 3.77秒
2025-08-07 17:40:59,968 - INFO -   - 视频时长: 3.82秒
2025-08-07 17:40:59,968 - INFO -   - 时长差异: 0.05秒 (1.35%)
2025-08-07 17:40:59,968 - INFO - 
----- 处理字幕 #32 的方案 #3 -----
2025-08-07 17:40:59,968 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\32_3.mp4
2025-08-07 17:40:59,968 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpdx7nylvq
2025-08-07 17:40:59,968 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1188.mp4 (确认存在: True)
2025-08-07 17:40:59,968 - INFO - 添加场景ID=1188，时长=1.56秒，累计时长=1.56秒
2025-08-07 17:40:59,969 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1201.mp4 (确认存在: True)
2025-08-07 17:40:59,969 - INFO - 添加场景ID=1201，时长=1.72秒，累计时长=3.28秒
2025-08-07 17:40:59,969 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1193.mp4 (确认存在: True)
2025-08-07 17:40:59,969 - INFO - 添加场景ID=1193，时长=1.04秒，累计时长=4.32秒
2025-08-07 17:40:59,969 - INFO - 准备合并 3 个场景文件，总时长约 4.32秒
2025-08-07 17:40:59,969 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1188.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1201.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1193.mp4'

2025-08-07 17:40:59,969 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpdx7nylvq\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpdx7nylvq\temp_combined.mp4
2025-08-07 17:41:00,123 - INFO - 合并后的视频时长: 4.39秒，目标音频时长: 3.77秒
2025-08-07 17:41:00,123 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpdx7nylvq\temp_combined.mp4 -ss 0 -to 3.772 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\32_3.mp4
2025-08-07 17:41:00,414 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:00,414 - INFO - 目标音频时长: 3.77秒
2025-08-07 17:41:00,414 - INFO - 实际视频时长: 3.82秒
2025-08-07 17:41:00,414 - INFO - 时长差异: 0.05秒 (1.35%)
2025-08-07 17:41:00,414 - INFO - ==========================================
2025-08-07 17:41:00,414 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:00,414 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\32_3.mp4
2025-08-07 17:41:00,415 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpdx7nylvq
2025-08-07 17:41:00,458 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:00,458 - INFO -   - 音频时长: 3.77秒
2025-08-07 17:41:00,458 - INFO -   - 视频时长: 3.82秒
2025-08-07 17:41:00,458 - INFO -   - 时长差异: 0.05秒 (1.35%)
2025-08-07 17:41:00,458 - INFO - 
字幕 #32 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:00,458 - INFO - 生成的视频文件:
2025-08-07 17:41:00,458 - INFO -   1. F:/github/aicut_auto/newcut_ai\32_1.mp4
2025-08-07 17:41:00,458 - INFO -   2. F:/github/aicut_auto/newcut_ai\32_2.mp4
2025-08-07 17:41:00,458 - INFO -   3. F:/github/aicut_auto/newcut_ai\32_3.mp4
2025-08-07 17:41:00,458 - INFO - ========== 字幕 #32 处理结束 ==========

