2025-08-07 17:41:53,368 - INFO - ========== 字幕 #71 处理开始 ==========
2025-08-07 17:41:53,368 - INFO - 字幕内容: 尘埃落定，了结了所有因果，女孩终于可以功成身退，返回山上继续修行。
2025-08-07 17:41:53,368 - INFO - 字幕序号: [3094, 3097]
2025-08-07 17:41:53,368 - INFO - 音频文件详情:
2025-08-07 17:41:53,368 - INFO -   - 路径: output\71.wav
2025-08-07 17:41:53,368 - INFO -   - 时长: 4.74秒
2025-08-07 17:41:53,369 - INFO -   - 验证音频时长: 4.74秒
2025-08-07 17:41:53,369 - INFO - 字幕时间戳信息:
2025-08-07 17:41:53,369 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:53,369 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:53,369 - INFO -   - 根据生成的音频时长(4.74秒)已调整字幕时间戳
2025-08-07 17:41:53,369 - INFO - ========== 新模式：为字幕 #71 生成4套场景方案 ==========
2025-08-07 17:41:53,369 - INFO - 字幕序号列表: [3094, 3097]
2025-08-07 17:41:53,369 - INFO - 
--- 生成方案 #1：基于字幕序号 #3094 ---
2025-08-07 17:41:53,369 - INFO - 开始为单个字幕序号 #3094 匹配场景，目标时长: 4.74秒
2025-08-07 17:41:53,369 - INFO - 开始查找字幕序号 [3094] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:53,370 - INFO - 找到related_overlap场景: scene_id=3437, 字幕#3094
2025-08-07 17:41:53,370 - INFO - 找到related_between场景: scene_id=3435, 字幕#3094
2025-08-07 17:41:53,371 - INFO - 找到related_between场景: scene_id=3436, 字幕#3094
2025-08-07 17:41:53,371 - INFO - 字幕 #3094 找到 1 个overlap场景, 2 个between场景
2025-08-07 17:41:53,371 - INFO - 字幕序号 #3094 找到 1 个可用overlap场景, 2 个可用between场景
2025-08-07 17:41:53,371 - INFO - 选择第一个overlap场景作为起点: scene_id=3437
2025-08-07 17:41:53,371 - INFO - 添加起点场景: scene_id=3437, 时长=3.12秒, 累计时长=3.12秒
2025-08-07 17:41:53,371 - INFO - 起点场景时长不足，需要延伸填充 1.62秒
2025-08-07 17:41:53,371 - INFO - 起点场景在原始列表中的索引: 3436
2025-08-07 17:41:53,371 - INFO - 延伸添加场景: scene_id=3438 (裁剪至 1.62秒)
2025-08-07 17:41:53,371 - INFO - 累计时长: 4.74秒
2025-08-07 17:41:53,371 - INFO - 字幕序号 #3094 场景匹配完成，共选择 2 个场景，总时长: 4.74秒
2025-08-07 17:41:53,371 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-08-07 17:41:53,371 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-08-07 17:41:53,371 - INFO - 
--- 生成方案 #2：基于字幕序号 #3097 ---
2025-08-07 17:41:53,371 - INFO - 开始为单个字幕序号 #3097 匹配场景，目标时长: 4.74秒
2025-08-07 17:41:53,371 - INFO - 开始查找字幕序号 [3097] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:53,372 - INFO - 找到related_overlap场景: scene_id=3439, 字幕#3097
2025-08-07 17:41:53,372 - INFO - 字幕 #3097 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:53,372 - INFO - 字幕序号 #3097 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:53,372 - INFO - 选择第一个overlap场景作为起点: scene_id=3439
2025-08-07 17:41:53,372 - INFO - 添加起点场景: scene_id=3439, 时长=2.96秒, 累计时长=2.96秒
2025-08-07 17:41:53,372 - INFO - 起点场景时长不足，需要延伸填充 1.78秒
2025-08-07 17:41:53,373 - INFO - 起点场景在原始列表中的索引: 3438
2025-08-07 17:41:53,373 - INFO - 延伸添加场景: scene_id=3440 (完整时长 1.16秒)
2025-08-07 17:41:53,373 - INFO - 累计时长: 4.12秒
2025-08-07 17:41:53,373 - INFO - 延伸添加场景: scene_id=3441 (裁剪至 0.62秒)
2025-08-07 17:41:53,373 - INFO - 累计时长: 4.74秒
2025-08-07 17:41:53,373 - INFO - 字幕序号 #3097 场景匹配完成，共选择 3 个场景，总时长: 4.74秒
2025-08-07 17:41:53,373 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-08-07 17:41:53,373 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:53,373 - INFO - ========== 当前模式：为字幕 #71 生成 1 套场景方案 ==========
2025-08-07 17:41:53,373 - INFO - 开始查找字幕序号 [3094, 3097] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:53,373 - INFO - 找到related_overlap场景: scene_id=3437, 字幕#3094
2025-08-07 17:41:53,373 - INFO - 找到related_overlap场景: scene_id=3439, 字幕#3097
2025-08-07 17:41:53,373 - INFO - 找到related_between场景: scene_id=3435, 字幕#3094
2025-08-07 17:41:53,373 - INFO - 找到related_between场景: scene_id=3436, 字幕#3094
2025-08-07 17:41:53,375 - INFO - 字幕 #3094 找到 1 个overlap场景, 2 个between场景
2025-08-07 17:41:53,375 - INFO - 字幕 #3097 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:53,375 - INFO - 共收集 2 个未使用的overlap场景和 2 个未使用的between场景
2025-08-07 17:41:53,375 - INFO - 开始生成方案 #1
2025-08-07 17:41:53,375 - INFO - 方案 #1: 为字幕#3094选择初始化overlap场景id=3437
2025-08-07 17:41:53,375 - INFO - 方案 #1: 为字幕#3097选择初始化overlap场景id=3439
2025-08-07 17:41:53,375 - INFO - 方案 #1: 初始选择后，当前总时长=6.08秒
2025-08-07 17:41:53,375 - INFO - 方案 #1: 额外between选择后，当前总时长=6.08秒
2025-08-07 17:41:53,375 - INFO - 方案 #1: 场景总时长(6.08秒)大于音频时长(4.74秒)，需要裁剪
2025-08-07 17:41:53,375 - INFO - 调整前总时长: 6.08秒, 目标时长: 4.74秒
2025-08-07 17:41:53,375 - INFO - 需要裁剪 1.34秒
2025-08-07 17:41:53,375 - INFO - 裁剪最长场景ID=3437：从3.12秒裁剪至1.78秒
2025-08-07 17:41:53,375 - INFO - 调整后总时长: 4.74秒，与目标时长差异: 0.00秒
2025-08-07 17:41:53,375 - INFO - 方案 #1 调整/填充后最终总时长: 4.74秒
2025-08-07 17:41:53,375 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:53,375 - INFO - ========== 当前模式：字幕 #71 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:53,375 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:53,375 - INFO - ========== 新模式：字幕 #71 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:53,375 - INFO - 
----- 处理字幕 #71 的方案 #1 -----
2025-08-07 17:41:53,375 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\71_1.mp4
2025-08-07 17:41:53,375 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpowx9jd2d
2025-08-07 17:41:53,376 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3437.mp4 (确认存在: True)
2025-08-07 17:41:53,376 - INFO - 添加场景ID=3437，时长=3.12秒，累计时长=3.12秒
2025-08-07 17:41:53,376 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3438.mp4 (确认存在: True)
2025-08-07 17:41:53,376 - INFO - 添加场景ID=3438，时长=2.60秒，累计时长=5.72秒
2025-08-07 17:41:53,376 - INFO - 准备合并 2 个场景文件，总时长约 5.72秒
2025-08-07 17:41:53,376 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3437.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3438.mp4'

2025-08-07 17:41:53,376 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpowx9jd2d\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpowx9jd2d\temp_combined.mp4
2025-08-07 17:41:53,529 - INFO - 合并后的视频时长: 5.77秒，目标音频时长: 4.74秒
2025-08-07 17:41:53,529 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpowx9jd2d\temp_combined.mp4 -ss 0 -to 4.741 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\71_1.mp4
2025-08-07 17:41:53,816 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:53,817 - INFO - 目标音频时长: 4.74秒
2025-08-07 17:41:53,817 - INFO - 实际视频时长: 4.78秒
2025-08-07 17:41:53,817 - INFO - 时长差异: 0.04秒 (0.89%)
2025-08-07 17:41:53,817 - INFO - ==========================================
2025-08-07 17:41:53,817 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:53,817 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\71_1.mp4
2025-08-07 17:41:53,817 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpowx9jd2d
2025-08-07 17:41:53,863 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:53,863 - INFO -   - 音频时长: 4.74秒
2025-08-07 17:41:53,863 - INFO -   - 视频时长: 4.78秒
2025-08-07 17:41:53,863 - INFO -   - 时长差异: 0.04秒 (0.89%)
2025-08-07 17:41:53,863 - INFO - 
----- 处理字幕 #71 的方案 #2 -----
2025-08-07 17:41:53,863 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\71_2.mp4
2025-08-07 17:41:53,863 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbszznb_6
2025-08-07 17:41:53,863 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3439.mp4 (确认存在: True)
2025-08-07 17:41:53,863 - INFO - 添加场景ID=3439，时长=2.96秒，累计时长=2.96秒
2025-08-07 17:41:53,865 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3440.mp4 (确认存在: True)
2025-08-07 17:41:53,865 - INFO - 添加场景ID=3440，时长=1.16秒，累计时长=4.12秒
2025-08-07 17:41:53,865 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3441.mp4 (确认存在: True)
2025-08-07 17:41:53,865 - INFO - 添加场景ID=3441，时长=1.08秒，累计时长=5.20秒
2025-08-07 17:41:53,865 - INFO - 准备合并 3 个场景文件，总时长约 5.20秒
2025-08-07 17:41:53,865 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3439.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3440.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3441.mp4'

2025-08-07 17:41:53,865 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpbszznb_6\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpbszznb_6\temp_combined.mp4
2025-08-07 17:41:54,000 - INFO - 合并后的视频时长: 5.27秒，目标音频时长: 4.74秒
2025-08-07 17:41:54,000 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpbszznb_6\temp_combined.mp4 -ss 0 -to 4.741 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\71_2.mp4
2025-08-07 17:41:54,280 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:54,281 - INFO - 目标音频时长: 4.74秒
2025-08-07 17:41:54,281 - INFO - 实际视频时长: 4.78秒
2025-08-07 17:41:54,281 - INFO - 时长差异: 0.04秒 (0.89%)
2025-08-07 17:41:54,281 - INFO - ==========================================
2025-08-07 17:41:54,281 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:54,281 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\71_2.mp4
2025-08-07 17:41:54,281 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbszznb_6
2025-08-07 17:41:54,327 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:54,327 - INFO -   - 音频时长: 4.74秒
2025-08-07 17:41:54,327 - INFO -   - 视频时长: 4.78秒
2025-08-07 17:41:54,327 - INFO -   - 时长差异: 0.04秒 (0.89%)
2025-08-07 17:41:54,327 - INFO - 
----- 处理字幕 #71 的方案 #3 -----
2025-08-07 17:41:54,327 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\71_3.mp4
2025-08-07 17:41:54,328 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplyfyezvy
2025-08-07 17:41:54,328 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3437.mp4 (确认存在: True)
2025-08-07 17:41:54,328 - INFO - 添加场景ID=3437，时长=3.12秒，累计时长=3.12秒
2025-08-07 17:41:54,328 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3439.mp4 (确认存在: True)
2025-08-07 17:41:54,328 - INFO - 添加场景ID=3439，时长=2.96秒，累计时长=6.08秒
2025-08-07 17:41:54,329 - INFO - 准备合并 2 个场景文件，总时长约 6.08秒
2025-08-07 17:41:54,329 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3437.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3439.mp4'

2025-08-07 17:41:54,329 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmplyfyezvy\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmplyfyezvy\temp_combined.mp4
2025-08-07 17:41:54,470 - INFO - 合并后的视频时长: 6.13秒，目标音频时长: 4.74秒
2025-08-07 17:41:54,470 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmplyfyezvy\temp_combined.mp4 -ss 0 -to 4.741 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\71_3.mp4
2025-08-07 17:41:54,750 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:54,750 - INFO - 目标音频时长: 4.74秒
2025-08-07 17:41:54,750 - INFO - 实际视频时长: 4.78秒
2025-08-07 17:41:54,750 - INFO - 时长差异: 0.04秒 (0.89%)
2025-08-07 17:41:54,750 - INFO - ==========================================
2025-08-07 17:41:54,750 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:54,750 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\71_3.mp4
2025-08-07 17:41:54,751 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplyfyezvy
2025-08-07 17:41:54,796 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:54,796 - INFO -   - 音频时长: 4.74秒
2025-08-07 17:41:54,796 - INFO -   - 视频时长: 4.78秒
2025-08-07 17:41:54,796 - INFO -   - 时长差异: 0.04秒 (0.89%)
2025-08-07 17:41:54,797 - INFO - 
字幕 #71 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:54,797 - INFO - 生成的视频文件:
2025-08-07 17:41:54,797 - INFO -   1. F:/github/aicut_auto/newcut_ai\71_1.mp4
2025-08-07 17:41:54,797 - INFO -   2. F:/github/aicut_auto/newcut_ai\71_2.mp4
2025-08-07 17:41:54,797 - INFO -   3. F:/github/aicut_auto/newcut_ai\71_3.mp4
2025-08-07 17:41:54,797 - INFO - ========== 字幕 #71 处理结束 ==========

