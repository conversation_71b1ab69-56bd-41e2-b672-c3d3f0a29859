2025-08-07 17:40:34,243 - INFO - ========== 字幕 #15 处理开始 ==========
2025-08-07 17:40:34,243 - INFO - 字幕内容: 爷爷激动地确认，她果然就是传说中的佑佑姑姑，女孩也大方承认，自己下山另有要事。
2025-08-07 17:40:34,243 - INFO - 字幕序号: [297, 304]
2025-08-07 17:40:34,243 - INFO - 音频文件详情:
2025-08-07 17:40:34,244 - INFO -   - 路径: output\15.wav
2025-08-07 17:40:34,244 - INFO -   - 时长: 5.14秒
2025-08-07 17:40:34,244 - INFO -   - 验证音频时长: 5.14秒
2025-08-07 17:40:34,244 - INFO - 字幕时间戳信息:
2025-08-07 17:40:34,244 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:34,244 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:34,244 - INFO -   - 根据生成的音频时长(5.14秒)已调整字幕时间戳
2025-08-07 17:40:34,244 - INFO - ========== 新模式：为字幕 #15 生成4套场景方案 ==========
2025-08-07 17:40:34,244 - INFO - 字幕序号列表: [297, 304]
2025-08-07 17:40:34,244 - INFO - 
--- 生成方案 #1：基于字幕序号 #297 ---
2025-08-07 17:40:34,244 - INFO - 开始为单个字幕序号 #297 匹配场景，目标时长: 5.14秒
2025-08-07 17:40:34,244 - INFO - 开始查找字幕序号 [297] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:34,244 - INFO - 找到related_overlap场景: scene_id=445, 字幕#297
2025-08-07 17:40:34,245 - INFO - 找到related_between场景: scene_id=438, 字幕#297
2025-08-07 17:40:34,245 - INFO - 找到related_between场景: scene_id=439, 字幕#297
2025-08-07 17:40:34,245 - INFO - 找到related_between场景: scene_id=440, 字幕#297
2025-08-07 17:40:34,245 - INFO - 找到related_between场景: scene_id=441, 字幕#297
2025-08-07 17:40:34,245 - INFO - 找到related_between场景: scene_id=442, 字幕#297
2025-08-07 17:40:34,245 - INFO - 找到related_between场景: scene_id=443, 字幕#297
2025-08-07 17:40:34,245 - INFO - 找到related_between场景: scene_id=444, 字幕#297
2025-08-07 17:40:34,245 - INFO - 字幕 #297 找到 1 个overlap场景, 7 个between场景
2025-08-07 17:40:34,245 - INFO - 字幕序号 #297 找到 1 个可用overlap场景, 7 个可用between场景
2025-08-07 17:40:34,245 - INFO - 选择第一个overlap场景作为起点: scene_id=445
2025-08-07 17:40:34,245 - INFO - 添加起点场景: scene_id=445, 时长=1.36秒, 累计时长=1.36秒
2025-08-07 17:40:34,245 - INFO - 起点场景时长不足，需要延伸填充 3.78秒
2025-08-07 17:40:34,245 - INFO - 起点场景在原始列表中的索引: 444
2025-08-07 17:40:34,245 - INFO - 延伸添加场景: scene_id=446 (完整时长 1.32秒)
2025-08-07 17:40:34,245 - INFO - 累计时长: 2.68秒
2025-08-07 17:40:34,245 - INFO - 延伸添加场景: scene_id=447 (完整时长 1.12秒)
2025-08-07 17:40:34,245 - INFO - 累计时长: 3.80秒
2025-08-07 17:40:34,245 - INFO - 延伸添加场景: scene_id=448 (裁剪至 1.34秒)
2025-08-07 17:40:34,245 - INFO - 累计时长: 5.14秒
2025-08-07 17:40:34,245 - INFO - 字幕序号 #297 场景匹配完成，共选择 4 个场景，总时长: 5.14秒
2025-08-07 17:40:34,245 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-08-07 17:40:34,245 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-08-07 17:40:34,245 - INFO - 
--- 生成方案 #2：基于字幕序号 #304 ---
2025-08-07 17:40:34,245 - INFO - 开始为单个字幕序号 #304 匹配场景，目标时长: 5.14秒
2025-08-07 17:40:34,245 - INFO - 开始查找字幕序号 [304] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:34,245 - INFO - 找到related_overlap场景: scene_id=459, 字幕#304
2025-08-07 17:40:34,246 - INFO - 找到related_between场景: scene_id=458, 字幕#304
2025-08-07 17:40:34,246 - INFO - 字幕 #304 找到 1 个overlap场景, 1 个between场景
2025-08-07 17:40:34,246 - INFO - 字幕序号 #304 找到 1 个可用overlap场景, 1 个可用between场景
2025-08-07 17:40:34,246 - INFO - 选择第一个overlap场景作为起点: scene_id=459
2025-08-07 17:40:34,246 - INFO - 添加起点场景: scene_id=459, 时长=2.72秒, 累计时长=2.72秒
2025-08-07 17:40:34,246 - INFO - 起点场景时长不足，需要延伸填充 2.42秒
2025-08-07 17:40:34,246 - INFO - 起点场景在原始列表中的索引: 458
2025-08-07 17:40:34,246 - INFO - 延伸添加场景: scene_id=460 (完整时长 2.08秒)
2025-08-07 17:40:34,246 - INFO - 累计时长: 4.80秒
2025-08-07 17:40:34,246 - INFO - 延伸添加场景: scene_id=461 (裁剪至 0.34秒)
2025-08-07 17:40:34,247 - INFO - 累计时长: 5.14秒
2025-08-07 17:40:34,247 - INFO - 字幕序号 #304 场景匹配完成，共选择 3 个场景，总时长: 5.14秒
2025-08-07 17:40:34,247 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-08-07 17:40:34,247 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:34,247 - INFO - ========== 当前模式：为字幕 #15 生成 1 套场景方案 ==========
2025-08-07 17:40:34,247 - INFO - 开始查找字幕序号 [297, 304] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:34,247 - INFO - 找到related_overlap场景: scene_id=445, 字幕#297
2025-08-07 17:40:34,247 - INFO - 找到related_overlap场景: scene_id=459, 字幕#304
2025-08-07 17:40:34,247 - INFO - 找到related_between场景: scene_id=438, 字幕#297
2025-08-07 17:40:34,247 - INFO - 找到related_between场景: scene_id=439, 字幕#297
2025-08-07 17:40:34,247 - INFO - 找到related_between场景: scene_id=440, 字幕#297
2025-08-07 17:40:34,247 - INFO - 找到related_between场景: scene_id=441, 字幕#297
2025-08-07 17:40:34,247 - INFO - 找到related_between场景: scene_id=442, 字幕#297
2025-08-07 17:40:34,247 - INFO - 找到related_between场景: scene_id=443, 字幕#297
2025-08-07 17:40:34,247 - INFO - 找到related_between场景: scene_id=444, 字幕#297
2025-08-07 17:40:34,247 - INFO - 找到related_between场景: scene_id=458, 字幕#304
2025-08-07 17:40:34,248 - INFO - 字幕 #297 找到 1 个overlap场景, 7 个between场景
2025-08-07 17:40:34,248 - INFO - 字幕 #304 找到 1 个overlap场景, 1 个between场景
2025-08-07 17:40:34,248 - INFO - 共收集 2 个未使用的overlap场景和 8 个未使用的between场景
2025-08-07 17:40:34,248 - INFO - 开始生成方案 #1
2025-08-07 17:40:34,248 - INFO - 方案 #1: 为字幕#297选择初始化overlap场景id=445
2025-08-07 17:40:34,248 - INFO - 方案 #1: 为字幕#304选择初始化overlap场景id=459
2025-08-07 17:40:34,248 - INFO - 方案 #1: 初始选择后，当前总时长=4.08秒
2025-08-07 17:40:34,248 - INFO - 方案 #1: 额外between选择后，当前总时长=4.08秒
2025-08-07 17:40:34,248 - INFO - 方案 #1: 额外添加between场景id=443, 当前总时长=5.20秒
2025-08-07 17:40:34,248 - INFO - 方案 #1: 场景总时长(5.20秒)大于音频时长(5.14秒)，需要裁剪
2025-08-07 17:40:34,248 - INFO - 调整前总时长: 5.20秒, 目标时长: 5.14秒
2025-08-07 17:40:34,248 - INFO - 需要裁剪 0.06秒
2025-08-07 17:40:34,248 - INFO - 裁剪最长场景ID=459：从2.72秒裁剪至2.66秒
2025-08-07 17:40:34,248 - INFO - 调整后总时长: 5.14秒，与目标时长差异: 0.00秒
2025-08-07 17:40:34,248 - INFO - 方案 #1 调整/填充后最终总时长: 5.14秒
2025-08-07 17:40:34,248 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:34,248 - INFO - ========== 当前模式：字幕 #15 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:34,248 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:34,248 - INFO - ========== 新模式：字幕 #15 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:34,248 - INFO - 
----- 处理字幕 #15 的方案 #1 -----
2025-08-07 17:40:34,248 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\15_1.mp4
2025-08-07 17:40:34,248 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpwflan7rk
2025-08-07 17:40:34,249 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\445.mp4 (确认存在: True)
2025-08-07 17:40:34,249 - INFO - 添加场景ID=445，时长=1.36秒，累计时长=1.36秒
2025-08-07 17:40:34,249 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\446.mp4 (确认存在: True)
2025-08-07 17:40:34,249 - INFO - 添加场景ID=446，时长=1.32秒，累计时长=2.68秒
2025-08-07 17:40:34,249 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\447.mp4 (确认存在: True)
2025-08-07 17:40:34,249 - INFO - 添加场景ID=447，时长=1.12秒，累计时长=3.80秒
2025-08-07 17:40:34,249 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\448.mp4 (确认存在: True)
2025-08-07 17:40:34,249 - INFO - 添加场景ID=448，时长=1.44秒，累计时长=5.24秒
2025-08-07 17:40:34,249 - INFO - 准备合并 4 个场景文件，总时长约 5.24秒
2025-08-07 17:40:34,249 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/445.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/446.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/447.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/448.mp4'

2025-08-07 17:40:34,249 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpwflan7rk\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpwflan7rk\temp_combined.mp4
2025-08-07 17:40:34,420 - INFO - 合并后的视频时长: 5.33秒，目标音频时长: 5.14秒
2025-08-07 17:40:34,420 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpwflan7rk\temp_combined.mp4 -ss 0 -to 5.137 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\15_1.mp4
2025-08-07 17:40:34,721 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:34,721 - INFO - 目标音频时长: 5.14秒
2025-08-07 17:40:34,721 - INFO - 实际视频时长: 5.18秒
2025-08-07 17:40:34,721 - INFO - 时长差异: 0.05秒 (0.90%)
2025-08-07 17:40:34,721 - INFO - ==========================================
2025-08-07 17:40:34,721 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:34,721 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\15_1.mp4
2025-08-07 17:40:34,722 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpwflan7rk
2025-08-07 17:40:34,766 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:34,766 - INFO -   - 音频时长: 5.14秒
2025-08-07 17:40:34,766 - INFO -   - 视频时长: 5.18秒
2025-08-07 17:40:34,766 - INFO -   - 时长差异: 0.05秒 (0.90%)
2025-08-07 17:40:34,766 - INFO - 
----- 处理字幕 #15 的方案 #2 -----
2025-08-07 17:40:34,766 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\15_2.mp4
2025-08-07 17:40:34,766 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5_5exh1b
2025-08-07 17:40:34,767 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\459.mp4 (确认存在: True)
2025-08-07 17:40:34,767 - INFO - 添加场景ID=459，时长=2.72秒，累计时长=2.72秒
2025-08-07 17:40:34,767 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\460.mp4 (确认存在: True)
2025-08-07 17:40:34,767 - INFO - 添加场景ID=460，时长=2.08秒，累计时长=4.80秒
2025-08-07 17:40:34,767 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\461.mp4 (确认存在: True)
2025-08-07 17:40:34,767 - INFO - 添加场景ID=461，时长=1.52秒，累计时长=6.32秒
2025-08-07 17:40:34,767 - INFO - 准备合并 3 个场景文件，总时长约 6.32秒
2025-08-07 17:40:34,767 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/459.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/460.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/461.mp4'

2025-08-07 17:40:34,767 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp5_5exh1b\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp5_5exh1b\temp_combined.mp4
2025-08-07 17:40:34,907 - INFO - 合并后的视频时长: 6.39秒，目标音频时长: 5.14秒
2025-08-07 17:40:34,907 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp5_5exh1b\temp_combined.mp4 -ss 0 -to 5.137 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\15_2.mp4
2025-08-07 17:40:35,209 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:35,209 - INFO - 目标音频时长: 5.14秒
2025-08-07 17:40:35,209 - INFO - 实际视频时长: 5.18秒
2025-08-07 17:40:35,209 - INFO - 时长差异: 0.05秒 (0.90%)
2025-08-07 17:40:35,209 - INFO - ==========================================
2025-08-07 17:40:35,209 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:35,209 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\15_2.mp4
2025-08-07 17:40:35,210 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5_5exh1b
2025-08-07 17:40:35,252 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:35,252 - INFO -   - 音频时长: 5.14秒
2025-08-07 17:40:35,252 - INFO -   - 视频时长: 5.18秒
2025-08-07 17:40:35,252 - INFO -   - 时长差异: 0.05秒 (0.90%)
2025-08-07 17:40:35,252 - INFO - 
----- 处理字幕 #15 的方案 #3 -----
2025-08-07 17:40:35,253 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\15_3.mp4
2025-08-07 17:40:35,253 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpl_skj7ju
2025-08-07 17:40:35,253 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\445.mp4 (确认存在: True)
2025-08-07 17:40:35,253 - INFO - 添加场景ID=445，时长=1.36秒，累计时长=1.36秒
2025-08-07 17:40:35,253 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\459.mp4 (确认存在: True)
2025-08-07 17:40:35,253 - INFO - 添加场景ID=459，时长=2.72秒，累计时长=4.08秒
2025-08-07 17:40:35,254 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\443.mp4 (确认存在: True)
2025-08-07 17:40:35,254 - INFO - 添加场景ID=443，时长=1.12秒，累计时长=5.20秒
2025-08-07 17:40:35,254 - INFO - 准备合并 3 个场景文件，总时长约 5.20秒
2025-08-07 17:40:35,254 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/445.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/459.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/443.mp4'

2025-08-07 17:40:35,254 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpl_skj7ju\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpl_skj7ju\temp_combined.mp4
2025-08-07 17:40:35,387 - INFO - 合并后的视频时长: 5.27秒，目标音频时长: 5.14秒
2025-08-07 17:40:35,387 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpl_skj7ju\temp_combined.mp4 -ss 0 -to 5.137 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\15_3.mp4
2025-08-07 17:40:35,671 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:35,671 - INFO - 目标音频时长: 5.14秒
2025-08-07 17:40:35,671 - INFO - 实际视频时长: 5.18秒
2025-08-07 17:40:35,671 - INFO - 时长差异: 0.05秒 (0.90%)
2025-08-07 17:40:35,671 - INFO - ==========================================
2025-08-07 17:40:35,671 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:35,671 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\15_3.mp4
2025-08-07 17:40:35,671 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpl_skj7ju
2025-08-07 17:40:35,725 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:35,725 - INFO -   - 音频时长: 5.14秒
2025-08-07 17:40:35,725 - INFO -   - 视频时长: 5.18秒
2025-08-07 17:40:35,725 - INFO -   - 时长差异: 0.05秒 (0.90%)
2025-08-07 17:40:35,725 - INFO - 
字幕 #15 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:35,725 - INFO - 生成的视频文件:
2025-08-07 17:40:35,725 - INFO -   1. F:/github/aicut_auto/newcut_ai\15_1.mp4
2025-08-07 17:40:35,725 - INFO -   2. F:/github/aicut_auto/newcut_ai\15_2.mp4
2025-08-07 17:40:35,725 - INFO -   3. F:/github/aicut_auto/newcut_ai\15_3.mp4
2025-08-07 17:40:35,725 - INFO - ========== 字幕 #15 处理结束 ==========

