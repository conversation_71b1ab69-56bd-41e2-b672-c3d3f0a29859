2025-08-07 17:40:37,393 - INFO - ========== 字幕 #17 处理开始 ==========
2025-08-07 17:40:37,393 - INFO - 字幕内容: 寿宴当天，男人带着女孩出席，立刻引来亲戚们的嘲讽，竟敢称呼女孩为“小屁孩”，浑然不知自己是在对老祖宗不敬。
2025-08-07 17:40:37,393 - INFO - 字幕序号: [451, 464]
2025-08-07 17:40:37,393 - INFO - 音频文件详情:
2025-08-07 17:40:37,393 - INFO -   - 路径: output\17.wav
2025-08-07 17:40:37,393 - INFO -   - 时长: 5.38秒
2025-08-07 17:40:37,394 - INFO -   - 验证音频时长: 5.38秒
2025-08-07 17:40:37,403 - INFO - 字幕时间戳信息:
2025-08-07 17:40:37,403 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:37,403 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:37,403 - INFO -   - 根据生成的音频时长(5.38秒)已调整字幕时间戳
2025-08-07 17:40:37,404 - INFO - ========== 新模式：为字幕 #17 生成4套场景方案 ==========
2025-08-07 17:40:37,404 - INFO - 字幕序号列表: [451, 464]
2025-08-07 17:40:37,404 - INFO - 
--- 生成方案 #1：基于字幕序号 #451 ---
2025-08-07 17:40:37,404 - INFO - 开始为单个字幕序号 #451 匹配场景，目标时长: 5.38秒
2025-08-07 17:40:37,404 - INFO - 开始查找字幕序号 [451] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:37,404 - INFO - 找到related_overlap场景: scene_id=616, 字幕#451
2025-08-07 17:40:37,405 - INFO - 找到related_between场景: scene_id=617, 字幕#451
2025-08-07 17:40:37,405 - INFO - 找到related_between场景: scene_id=618, 字幕#451
2025-08-07 17:40:37,405 - INFO - 找到related_between场景: scene_id=619, 字幕#451
2025-08-07 17:40:37,405 - INFO - 找到related_between场景: scene_id=620, 字幕#451
2025-08-07 17:40:37,405 - INFO - 字幕 #451 找到 1 个overlap场景, 4 个between场景
2025-08-07 17:40:37,405 - INFO - 字幕序号 #451 找到 1 个可用overlap场景, 4 个可用between场景
2025-08-07 17:40:37,405 - INFO - 选择第一个overlap场景作为起点: scene_id=616
2025-08-07 17:40:37,405 - INFO - 添加起点场景: scene_id=616, 时长=2.92秒, 累计时长=2.92秒
2025-08-07 17:40:37,405 - INFO - 起点场景时长不足，需要延伸填充 2.47秒
2025-08-07 17:40:37,405 - INFO - 起点场景在原始列表中的索引: 615
2025-08-07 17:40:37,405 - INFO - 延伸添加场景: scene_id=617 (完整时长 1.60秒)
2025-08-07 17:40:37,405 - INFO - 累计时长: 4.52秒
2025-08-07 17:40:37,405 - INFO - 延伸添加场景: scene_id=618 (裁剪至 0.87秒)
2025-08-07 17:40:37,405 - INFO - 累计时长: 5.38秒
2025-08-07 17:40:37,405 - INFO - 字幕序号 #451 场景匹配完成，共选择 3 个场景，总时长: 5.38秒
2025-08-07 17:40:37,405 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-08-07 17:40:37,405 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-08-07 17:40:37,405 - INFO - 
--- 生成方案 #2：基于字幕序号 #464 ---
2025-08-07 17:40:37,405 - INFO - 开始为单个字幕序号 #464 匹配场景，目标时长: 5.38秒
2025-08-07 17:40:37,405 - INFO - 开始查找字幕序号 [464] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:37,405 - INFO - 找到related_overlap场景: scene_id=630, 字幕#464
2025-08-07 17:40:37,406 - INFO - 字幕 #464 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:37,406 - INFO - 字幕序号 #464 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:37,406 - INFO - 选择第一个overlap场景作为起点: scene_id=630
2025-08-07 17:40:37,406 - INFO - 添加起点场景: scene_id=630, 时长=2.36秒, 累计时长=2.36秒
2025-08-07 17:40:37,406 - INFO - 起点场景时长不足，需要延伸填充 3.03秒
2025-08-07 17:40:37,406 - INFO - 起点场景在原始列表中的索引: 629
2025-08-07 17:40:37,406 - INFO - 延伸添加场景: scene_id=631 (完整时长 2.88秒)
2025-08-07 17:40:37,406 - INFO - 累计时长: 5.24秒
2025-08-07 17:40:37,406 - INFO - 延伸添加场景: scene_id=632 (裁剪至 0.15秒)
2025-08-07 17:40:37,406 - INFO - 累计时长: 5.38秒
2025-08-07 17:40:37,406 - INFO - 字幕序号 #464 场景匹配完成，共选择 3 个场景，总时长: 5.38秒
2025-08-07 17:40:37,406 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-08-07 17:40:37,406 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:37,406 - INFO - ========== 当前模式：为字幕 #17 生成 1 套场景方案 ==========
2025-08-07 17:40:37,406 - INFO - 开始查找字幕序号 [451, 464] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:37,406 - INFO - 找到related_overlap场景: scene_id=616, 字幕#451
2025-08-07 17:40:37,406 - INFO - 找到related_overlap场景: scene_id=630, 字幕#464
2025-08-07 17:40:37,408 - INFO - 找到related_between场景: scene_id=617, 字幕#451
2025-08-07 17:40:37,408 - INFO - 找到related_between场景: scene_id=618, 字幕#451
2025-08-07 17:40:37,408 - INFO - 找到related_between场景: scene_id=619, 字幕#451
2025-08-07 17:40:37,408 - INFO - 找到related_between场景: scene_id=620, 字幕#451
2025-08-07 17:40:37,408 - INFO - 字幕 #451 找到 1 个overlap场景, 4 个between场景
2025-08-07 17:40:37,408 - INFO - 字幕 #464 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:37,408 - INFO - 共收集 2 个未使用的overlap场景和 4 个未使用的between场景
2025-08-07 17:40:37,408 - INFO - 开始生成方案 #1
2025-08-07 17:40:37,408 - INFO - 方案 #1: 为字幕#451选择初始化overlap场景id=616
2025-08-07 17:40:37,408 - INFO - 方案 #1: 为字幕#464选择初始化overlap场景id=630
2025-08-07 17:40:37,408 - INFO - 方案 #1: 初始选择后，当前总时长=5.28秒
2025-08-07 17:40:37,408 - INFO - 方案 #1: 额外between选择后，当前总时长=5.28秒
2025-08-07 17:40:37,408 - INFO - 方案 #1: 额外添加between场景id=618, 当前总时长=7.68秒
2025-08-07 17:40:37,408 - INFO - 方案 #1: 场景总时长(7.68秒)大于音频时长(5.38秒)，需要裁剪
2025-08-07 17:40:37,408 - INFO - 调整前总时长: 7.68秒, 目标时长: 5.38秒
2025-08-07 17:40:37,408 - INFO - 需要裁剪 2.29秒
2025-08-07 17:40:37,408 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-08-07 17:40:37,408 - INFO - 裁剪场景ID=616：从2.92秒裁剪至1.00秒
2025-08-07 17:40:37,408 - INFO - 裁剪场景ID=618：从2.40秒裁剪至2.03秒
2025-08-07 17:40:37,408 - INFO - 调整后总时长: 5.38秒，与目标时长差异: 0.00秒
2025-08-07 17:40:37,408 - INFO - 方案 #1 调整/填充后最终总时长: 5.38秒
2025-08-07 17:40:37,408 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:37,408 - INFO - ========== 当前模式：字幕 #17 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:37,408 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:37,408 - INFO - ========== 新模式：字幕 #17 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:37,408 - INFO - 
----- 处理字幕 #17 的方案 #1 -----
2025-08-07 17:40:37,408 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\17_1.mp4
2025-08-07 17:40:37,409 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpnmeq7xsg
2025-08-07 17:40:37,409 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\616.mp4 (确认存在: True)
2025-08-07 17:40:37,409 - INFO - 添加场景ID=616，时长=2.92秒，累计时长=2.92秒
2025-08-07 17:40:37,409 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\617.mp4 (确认存在: True)
2025-08-07 17:40:37,409 - INFO - 添加场景ID=617，时长=1.60秒，累计时长=4.52秒
2025-08-07 17:40:37,410 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\618.mp4 (确认存在: True)
2025-08-07 17:40:37,410 - INFO - 添加场景ID=618，时长=2.40秒，累计时长=6.92秒
2025-08-07 17:40:37,410 - INFO - 准备合并 3 个场景文件，总时长约 6.92秒
2025-08-07 17:40:37,410 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/616.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/617.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/618.mp4'

2025-08-07 17:40:37,410 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpnmeq7xsg\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpnmeq7xsg\temp_combined.mp4
2025-08-07 17:40:37,553 - INFO - 合并后的视频时长: 6.99秒，目标音频时长: 5.38秒
2025-08-07 17:40:37,553 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpnmeq7xsg\temp_combined.mp4 -ss 0 -to 5.384 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\17_1.mp4
2025-08-07 17:40:37,873 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:37,874 - INFO - 目标音频时长: 5.38秒
2025-08-07 17:40:37,874 - INFO - 实际视频时长: 5.42秒
2025-08-07 17:40:37,874 - INFO - 时长差异: 0.04秒 (0.72%)
2025-08-07 17:40:37,874 - INFO - ==========================================
2025-08-07 17:40:37,874 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:37,874 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\17_1.mp4
2025-08-07 17:40:37,874 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpnmeq7xsg
2025-08-07 17:40:37,916 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:37,916 - INFO -   - 音频时长: 5.38秒
2025-08-07 17:40:37,916 - INFO -   - 视频时长: 5.42秒
2025-08-07 17:40:37,916 - INFO -   - 时长差异: 0.04秒 (0.72%)
2025-08-07 17:40:37,916 - INFO - 
----- 处理字幕 #17 的方案 #2 -----
2025-08-07 17:40:37,916 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\17_2.mp4
2025-08-07 17:40:37,917 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxc7bmz8v
2025-08-07 17:40:37,917 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\630.mp4 (确认存在: True)
2025-08-07 17:40:37,917 - INFO - 添加场景ID=630，时长=2.36秒，累计时长=2.36秒
2025-08-07 17:40:37,917 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\631.mp4 (确认存在: True)
2025-08-07 17:40:37,917 - INFO - 添加场景ID=631，时长=2.88秒，累计时长=5.24秒
2025-08-07 17:40:37,917 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\632.mp4 (确认存在: True)
2025-08-07 17:40:37,917 - INFO - 添加场景ID=632，时长=1.84秒，累计时长=7.08秒
2025-08-07 17:40:37,918 - INFO - 准备合并 3 个场景文件，总时长约 7.08秒
2025-08-07 17:40:37,918 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/630.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/631.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/632.mp4'

2025-08-07 17:40:37,918 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpxc7bmz8v\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpxc7bmz8v\temp_combined.mp4
2025-08-07 17:40:38,058 - INFO - 合并后的视频时长: 7.15秒，目标音频时长: 5.38秒
2025-08-07 17:40:38,058 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpxc7bmz8v\temp_combined.mp4 -ss 0 -to 5.384 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\17_2.mp4
2025-08-07 17:40:38,349 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:38,349 - INFO - 目标音频时长: 5.38秒
2025-08-07 17:40:38,349 - INFO - 实际视频时长: 5.42秒
2025-08-07 17:40:38,349 - INFO - 时长差异: 0.04秒 (0.72%)
2025-08-07 17:40:38,349 - INFO - ==========================================
2025-08-07 17:40:38,349 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:38,349 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\17_2.mp4
2025-08-07 17:40:38,350 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxc7bmz8v
2025-08-07 17:40:38,394 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:38,394 - INFO -   - 音频时长: 5.38秒
2025-08-07 17:40:38,394 - INFO -   - 视频时长: 5.42秒
2025-08-07 17:40:38,394 - INFO -   - 时长差异: 0.04秒 (0.72%)
2025-08-07 17:40:38,394 - INFO - 
----- 处理字幕 #17 的方案 #3 -----
2025-08-07 17:40:38,396 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\17_3.mp4
2025-08-07 17:40:38,396 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpz6f5lhfx
2025-08-07 17:40:38,396 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\616.mp4 (确认存在: True)
2025-08-07 17:40:38,396 - INFO - 添加场景ID=616，时长=2.92秒，累计时长=2.92秒
2025-08-07 17:40:38,397 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\630.mp4 (确认存在: True)
2025-08-07 17:40:38,397 - INFO - 添加场景ID=630，时长=2.36秒，累计时长=5.28秒
2025-08-07 17:40:38,397 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\618.mp4 (确认存在: True)
2025-08-07 17:40:38,397 - INFO - 添加场景ID=618，时长=2.40秒，累计时长=7.68秒
2025-08-07 17:40:38,397 - INFO - 准备合并 3 个场景文件，总时长约 7.68秒
2025-08-07 17:40:38,397 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/616.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/630.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/618.mp4'

2025-08-07 17:40:38,397 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpz6f5lhfx\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpz6f5lhfx\temp_combined.mp4
2025-08-07 17:40:38,527 - INFO - 合并后的视频时长: 7.75秒，目标音频时长: 5.38秒
2025-08-07 17:40:38,527 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpz6f5lhfx\temp_combined.mp4 -ss 0 -to 5.384 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\17_3.mp4
2025-08-07 17:40:38,831 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:38,831 - INFO - 目标音频时长: 5.38秒
2025-08-07 17:40:38,831 - INFO - 实际视频时长: 5.42秒
2025-08-07 17:40:38,831 - INFO - 时长差异: 0.04秒 (0.72%)
2025-08-07 17:40:38,831 - INFO - ==========================================
2025-08-07 17:40:38,831 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:38,831 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\17_3.mp4
2025-08-07 17:40:38,832 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpz6f5lhfx
2025-08-07 17:40:38,875 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:38,875 - INFO -   - 音频时长: 5.38秒
2025-08-07 17:40:38,875 - INFO -   - 视频时长: 5.42秒
2025-08-07 17:40:38,875 - INFO -   - 时长差异: 0.04秒 (0.72%)
2025-08-07 17:40:38,877 - INFO - 
字幕 #17 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:38,877 - INFO - 生成的视频文件:
2025-08-07 17:40:38,877 - INFO -   1. F:/github/aicut_auto/newcut_ai\17_1.mp4
2025-08-07 17:40:38,877 - INFO -   2. F:/github/aicut_auto/newcut_ai\17_2.mp4
2025-08-07 17:40:38,877 - INFO -   3. F:/github/aicut_auto/newcut_ai\17_3.mp4
2025-08-07 17:40:38,877 - INFO - ========== 字幕 #17 处理结束 ==========

