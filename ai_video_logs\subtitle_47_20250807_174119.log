2025-08-07 17:41:19,714 - INFO - ========== 字幕 #47 处理开始 ==========
2025-08-07 17:41:19,714 - INFO - 字幕内容: 他声称商会会长身患重病，自己有能包治百病的“祖传秘药”，以此为筹码，企图抢夺合作项目。
2025-08-07 17:41:19,714 - INFO - 字幕序号: [2082, 2091]
2025-08-07 17:41:19,714 - INFO - 音频文件详情:
2025-08-07 17:41:19,714 - INFO -   - 路径: output\47.wav
2025-08-07 17:41:19,714 - INFO -   - 时长: 6.01秒
2025-08-07 17:41:19,714 - INFO -   - 验证音频时长: 6.01秒
2025-08-07 17:41:19,714 - INFO - 字幕时间戳信息:
2025-08-07 17:41:19,714 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:19,714 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:19,714 - INFO -   - 根据生成的音频时长(6.01秒)已调整字幕时间戳
2025-08-07 17:41:19,714 - INFO - ========== 新模式：为字幕 #47 生成4套场景方案 ==========
2025-08-07 17:41:19,714 - INFO - 字幕序号列表: [2082, 2091]
2025-08-07 17:41:19,715 - INFO - 
--- 生成方案 #1：基于字幕序号 #2082 ---
2025-08-07 17:41:19,715 - INFO - 开始为单个字幕序号 #2082 匹配场景，目标时长: 6.01秒
2025-08-07 17:41:19,715 - INFO - 开始查找字幕序号 [2082] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:19,715 - INFO - 找到related_overlap场景: scene_id=2351, 字幕#2082
2025-08-07 17:41:19,716 - INFO - 字幕 #2082 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:19,716 - INFO - 字幕序号 #2082 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:19,716 - INFO - 选择第一个overlap场景作为起点: scene_id=2351
2025-08-07 17:41:19,716 - INFO - 添加起点场景: scene_id=2351, 时长=2.08秒, 累计时长=2.08秒
2025-08-07 17:41:19,716 - INFO - 起点场景时长不足，需要延伸填充 3.93秒
2025-08-07 17:41:19,716 - INFO - 起点场景在原始列表中的索引: 2350
2025-08-07 17:41:19,716 - INFO - 延伸添加场景: scene_id=2352 (完整时长 3.20秒)
2025-08-07 17:41:19,716 - INFO - 累计时长: 5.28秒
2025-08-07 17:41:19,716 - INFO - 延伸添加场景: scene_id=2353 (裁剪至 0.73秒)
2025-08-07 17:41:19,716 - INFO - 累计时长: 6.01秒
2025-08-07 17:41:19,716 - INFO - 字幕序号 #2082 场景匹配完成，共选择 3 个场景，总时长: 6.01秒
2025-08-07 17:41:19,716 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-08-07 17:41:19,716 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-08-07 17:41:19,716 - INFO - 
--- 生成方案 #2：基于字幕序号 #2091 ---
2025-08-07 17:41:19,716 - INFO - 开始为单个字幕序号 #2091 匹配场景，目标时长: 6.01秒
2025-08-07 17:41:19,716 - INFO - 开始查找字幕序号 [2091] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:19,717 - INFO - 找到related_overlap场景: scene_id=2360, 字幕#2091
2025-08-07 17:41:19,717 - INFO - 字幕 #2091 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:19,717 - INFO - 字幕序号 #2091 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:19,717 - INFO - 选择第一个overlap场景作为起点: scene_id=2360
2025-08-07 17:41:19,717 - INFO - 添加起点场景: scene_id=2360, 时长=1.88秒, 累计时长=1.88秒
2025-08-07 17:41:19,717 - INFO - 起点场景时长不足，需要延伸填充 4.13秒
2025-08-07 17:41:19,717 - INFO - 起点场景在原始列表中的索引: 2359
2025-08-07 17:41:19,717 - INFO - 延伸添加场景: scene_id=2361 (完整时长 1.32秒)
2025-08-07 17:41:19,717 - INFO - 累计时长: 3.20秒
2025-08-07 17:41:19,717 - INFO - 延伸添加场景: scene_id=2362 (完整时长 1.24秒)
2025-08-07 17:41:19,717 - INFO - 累计时长: 4.44秒
2025-08-07 17:41:19,717 - INFO - 延伸添加场景: scene_id=2363 (裁剪至 1.57秒)
2025-08-07 17:41:19,717 - INFO - 累计时长: 6.01秒
2025-08-07 17:41:19,717 - INFO - 字幕序号 #2091 场景匹配完成，共选择 4 个场景，总时长: 6.01秒
2025-08-07 17:41:19,717 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-08-07 17:41:19,718 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:19,718 - INFO - ========== 当前模式：为字幕 #47 生成 1 套场景方案 ==========
2025-08-07 17:41:19,718 - INFO - 开始查找字幕序号 [2082, 2091] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:19,718 - INFO - 找到related_overlap场景: scene_id=2351, 字幕#2082
2025-08-07 17:41:19,718 - INFO - 找到related_overlap场景: scene_id=2360, 字幕#2091
2025-08-07 17:41:19,719 - INFO - 字幕 #2082 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:19,719 - INFO - 字幕 #2091 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:19,719 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-08-07 17:41:19,719 - INFO - 开始生成方案 #1
2025-08-07 17:41:19,719 - INFO - 方案 #1: 为字幕#2082选择初始化overlap场景id=2351
2025-08-07 17:41:19,719 - INFO - 方案 #1: 为字幕#2091选择初始化overlap场景id=2360
2025-08-07 17:41:19,719 - INFO - 方案 #1: 初始选择后，当前总时长=3.96秒
2025-08-07 17:41:19,719 - INFO - 方案 #1: 额外between选择后，当前总时长=3.96秒
2025-08-07 17:41:19,719 - INFO - 方案 #1: 场景总时长(3.96秒)小于音频时长(6.01秒)，需要延伸填充
2025-08-07 17:41:19,719 - INFO - 方案 #1: 最后一个场景ID: 2360
2025-08-07 17:41:19,719 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 2359
2025-08-07 17:41:19,719 - INFO - 方案 #1: 需要填充时长: 2.05秒
2025-08-07 17:41:19,719 - INFO - 方案 #1: 追加场景 scene_id=2361 (完整时长 1.32秒)
2025-08-07 17:41:19,719 - INFO - 方案 #1: 追加场景 scene_id=2362 (裁剪至 0.73秒)
2025-08-07 17:41:19,719 - INFO - 方案 #1: 成功填充至目标时长
2025-08-07 17:41:19,719 - INFO - 方案 #1 调整/填充后最终总时长: 6.01秒
2025-08-07 17:41:19,719 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:19,719 - INFO - ========== 当前模式：字幕 #47 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:19,719 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:19,719 - INFO - ========== 新模式：字幕 #47 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:19,719 - INFO - 
----- 处理字幕 #47 的方案 #1 -----
2025-08-07 17:41:19,719 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\47_1.mp4
2025-08-07 17:41:19,719 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmphnvn0n_g
2025-08-07 17:41:19,720 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2351.mp4 (确认存在: True)
2025-08-07 17:41:19,720 - INFO - 添加场景ID=2351，时长=2.08秒，累计时长=2.08秒
2025-08-07 17:41:19,720 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2352.mp4 (确认存在: True)
2025-08-07 17:41:19,720 - INFO - 添加场景ID=2352，时长=3.20秒，累计时长=5.28秒
2025-08-07 17:41:19,720 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2353.mp4 (确认存在: True)
2025-08-07 17:41:19,720 - INFO - 添加场景ID=2353，时长=3.08秒，累计时长=8.36秒
2025-08-07 17:41:19,720 - INFO - 准备合并 3 个场景文件，总时长约 8.36秒
2025-08-07 17:41:19,720 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2351.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2352.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2353.mp4'

2025-08-07 17:41:19,720 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmphnvn0n_g\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmphnvn0n_g\temp_combined.mp4
2025-08-07 17:41:19,845 - INFO - 合并后的视频时长: 8.43秒，目标音频时长: 6.01秒
2025-08-07 17:41:19,845 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmphnvn0n_g\temp_combined.mp4 -ss 0 -to 6.01 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\47_1.mp4
2025-08-07 17:41:20,141 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:20,141 - INFO - 目标音频时长: 6.01秒
2025-08-07 17:41:20,142 - INFO - 实际视频时长: 6.06秒
2025-08-07 17:41:20,142 - INFO - 时长差异: 0.05秒 (0.88%)
2025-08-07 17:41:20,142 - INFO - ==========================================
2025-08-07 17:41:20,142 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:20,142 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\47_1.mp4
2025-08-07 17:41:20,143 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmphnvn0n_g
2025-08-07 17:41:20,187 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:20,187 - INFO -   - 音频时长: 6.01秒
2025-08-07 17:41:20,187 - INFO -   - 视频时长: 6.06秒
2025-08-07 17:41:20,187 - INFO -   - 时长差异: 0.05秒 (0.88%)
2025-08-07 17:41:20,187 - INFO - 
----- 处理字幕 #47 的方案 #2 -----
2025-08-07 17:41:20,187 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\47_2.mp4
2025-08-07 17:41:20,187 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpfp2p4fn4
2025-08-07 17:41:20,188 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2360.mp4 (确认存在: True)
2025-08-07 17:41:20,188 - INFO - 添加场景ID=2360，时长=1.88秒，累计时长=1.88秒
2025-08-07 17:41:20,188 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2361.mp4 (确认存在: True)
2025-08-07 17:41:20,188 - INFO - 添加场景ID=2361，时长=1.32秒，累计时长=3.20秒
2025-08-07 17:41:20,188 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2362.mp4 (确认存在: True)
2025-08-07 17:41:20,188 - INFO - 添加场景ID=2362，时长=1.24秒，累计时长=4.44秒
2025-08-07 17:41:20,188 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2363.mp4 (确认存在: True)
2025-08-07 17:41:20,188 - INFO - 添加场景ID=2363，时长=1.64秒，累计时长=6.08秒
2025-08-07 17:41:20,188 - INFO - 准备合并 4 个场景文件，总时长约 6.08秒
2025-08-07 17:41:20,188 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2360.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2361.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2362.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2363.mp4'

2025-08-07 17:41:20,188 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpfp2p4fn4\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpfp2p4fn4\temp_combined.mp4
2025-08-07 17:41:20,333 - INFO - 合并后的视频时长: 6.17秒，目标音频时长: 6.01秒
2025-08-07 17:41:20,333 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpfp2p4fn4\temp_combined.mp4 -ss 0 -to 6.01 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\47_2.mp4
2025-08-07 17:41:20,649 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:20,649 - INFO - 目标音频时长: 6.01秒
2025-08-07 17:41:20,649 - INFO - 实际视频时长: 6.06秒
2025-08-07 17:41:20,649 - INFO - 时长差异: 0.05秒 (0.88%)
2025-08-07 17:41:20,649 - INFO - ==========================================
2025-08-07 17:41:20,649 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:20,649 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\47_2.mp4
2025-08-07 17:41:20,649 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpfp2p4fn4
2025-08-07 17:41:20,693 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:20,693 - INFO -   - 音频时长: 6.01秒
2025-08-07 17:41:20,693 - INFO -   - 视频时长: 6.06秒
2025-08-07 17:41:20,693 - INFO -   - 时长差异: 0.05秒 (0.88%)
2025-08-07 17:41:20,693 - INFO - 
----- 处理字幕 #47 的方案 #3 -----
2025-08-07 17:41:20,693 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\47_3.mp4
2025-08-07 17:41:20,694 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp024bslj3
2025-08-07 17:41:20,694 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2351.mp4 (确认存在: True)
2025-08-07 17:41:20,694 - INFO - 添加场景ID=2351，时长=2.08秒，累计时长=2.08秒
2025-08-07 17:41:20,694 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2360.mp4 (确认存在: True)
2025-08-07 17:41:20,694 - INFO - 添加场景ID=2360，时长=1.88秒，累计时长=3.96秒
2025-08-07 17:41:20,695 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2361.mp4 (确认存在: True)
2025-08-07 17:41:20,695 - INFO - 添加场景ID=2361，时长=1.32秒，累计时长=5.28秒
2025-08-07 17:41:20,695 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2362.mp4 (确认存在: True)
2025-08-07 17:41:20,695 - INFO - 添加场景ID=2362，时长=1.24秒，累计时长=6.52秒
2025-08-07 17:41:20,695 - INFO - 准备合并 4 个场景文件，总时长约 6.52秒
2025-08-07 17:41:20,695 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2351.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2360.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2361.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2362.mp4'

2025-08-07 17:41:20,695 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp024bslj3\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp024bslj3\temp_combined.mp4
2025-08-07 17:41:20,823 - INFO - 合并后的视频时长: 6.61秒，目标音频时长: 6.01秒
2025-08-07 17:41:20,823 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp024bslj3\temp_combined.mp4 -ss 0 -to 6.01 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\47_3.mp4
2025-08-07 17:41:21,133 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:21,133 - INFO - 目标音频时长: 6.01秒
2025-08-07 17:41:21,133 - INFO - 实际视频时长: 6.06秒
2025-08-07 17:41:21,133 - INFO - 时长差异: 0.05秒 (0.88%)
2025-08-07 17:41:21,133 - INFO - ==========================================
2025-08-07 17:41:21,133 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:21,133 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\47_3.mp4
2025-08-07 17:41:21,134 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp024bslj3
2025-08-07 17:41:21,178 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:21,178 - INFO -   - 音频时长: 6.01秒
2025-08-07 17:41:21,178 - INFO -   - 视频时长: 6.06秒
2025-08-07 17:41:21,178 - INFO -   - 时长差异: 0.05秒 (0.88%)
2025-08-07 17:41:21,178 - INFO - 
字幕 #47 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:21,178 - INFO - 生成的视频文件:
2025-08-07 17:41:21,178 - INFO -   1. F:/github/aicut_auto/newcut_ai\47_1.mp4
2025-08-07 17:41:21,178 - INFO -   2. F:/github/aicut_auto/newcut_ai\47_2.mp4
2025-08-07 17:41:21,178 - INFO -   3. F:/github/aicut_auto/newcut_ai\47_3.mp4
2025-08-07 17:41:21,178 - INFO - ========== 字幕 #47 处理结束 ==========

