2025-08-07 17:41:50,367 - INFO - ========== 字幕 #69 处理开始 ==========
2025-08-07 17:41:50,367 - INFO - 字幕内容: 眼看阴谋败露，他竟在身上绑满了炸药，要与所有人同归于尽。
2025-08-07 17:41:50,367 - INFO - 字幕序号: [3086, 3090]
2025-08-07 17:41:50,367 - INFO - 音频文件详情:
2025-08-07 17:41:50,367 - INFO -   - 路径: output\69.wav
2025-08-07 17:41:50,367 - INFO -   - 时长: 3.97秒
2025-08-07 17:41:50,368 - INFO -   - 验证音频时长: 3.97秒
2025-08-07 17:41:50,368 - INFO - 字幕时间戳信息:
2025-08-07 17:41:50,368 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:50,368 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:50,368 - INFO -   - 根据生成的音频时长(3.97秒)已调整字幕时间戳
2025-08-07 17:41:50,368 - INFO - ========== 新模式：为字幕 #69 生成4套场景方案 ==========
2025-08-07 17:41:50,368 - INFO - 字幕序号列表: [3086, 3090]
2025-08-07 17:41:50,368 - INFO - 
--- 生成方案 #1：基于字幕序号 #3086 ---
2025-08-07 17:41:50,368 - INFO - 开始为单个字幕序号 #3086 匹配场景，目标时长: 3.97秒
2025-08-07 17:41:50,368 - INFO - 开始查找字幕序号 [3086] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:50,368 - INFO - 找到related_overlap场景: scene_id=3426, 字幕#3086
2025-08-07 17:41:50,368 - INFO - 找到related_overlap场景: scene_id=3428, 字幕#3086
2025-08-07 17:41:50,370 - INFO - 字幕 #3086 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:50,370 - INFO - 字幕序号 #3086 找到 2 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:50,370 - INFO - 选择第一个overlap场景作为起点: scene_id=3426
2025-08-07 17:41:50,370 - INFO - 添加起点场景: scene_id=3426, 时长=0.88秒, 累计时长=0.88秒
2025-08-07 17:41:50,370 - INFO - 起点场景时长不足，需要延伸填充 3.09秒
2025-08-07 17:41:50,370 - INFO - 起点场景在原始列表中的索引: 3425
2025-08-07 17:41:50,370 - INFO - 延伸添加场景: scene_id=3427 (完整时长 0.56秒)
2025-08-07 17:41:50,370 - INFO - 累计时长: 1.44秒
2025-08-07 17:41:50,370 - INFO - 延伸添加场景: scene_id=3428 (完整时长 0.64秒)
2025-08-07 17:41:50,370 - INFO - 累计时长: 2.08秒
2025-08-07 17:41:50,370 - INFO - 延伸添加场景: scene_id=3429 (完整时长 0.72秒)
2025-08-07 17:41:50,370 - INFO - 累计时长: 2.80秒
2025-08-07 17:41:50,370 - INFO - 延伸添加场景: scene_id=3430 (裁剪至 1.17秒)
2025-08-07 17:41:50,370 - INFO - 累计时长: 3.97秒
2025-08-07 17:41:50,370 - INFO - 字幕序号 #3086 场景匹配完成，共选择 5 个场景，总时长: 3.97秒
2025-08-07 17:41:50,370 - INFO - 方案 #1 生成成功，包含 5 个场景
2025-08-07 17:41:50,370 - INFO - 新模式：第1套方案的 5 个场景已加入全局已使用集合
2025-08-07 17:41:50,370 - INFO - 
--- 生成方案 #2：基于字幕序号 #3090 ---
2025-08-07 17:41:50,370 - INFO - 开始为单个字幕序号 #3090 匹配场景，目标时长: 3.97秒
2025-08-07 17:41:50,370 - INFO - 开始查找字幕序号 [3090] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:50,371 - INFO - 找到related_overlap场景: scene_id=3432, 字幕#3090
2025-08-07 17:41:50,371 - INFO - 字幕 #3090 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:50,371 - INFO - 字幕序号 #3090 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:50,371 - INFO - 选择第一个overlap场景作为起点: scene_id=3432
2025-08-07 17:41:50,371 - INFO - 添加起点场景: scene_id=3432, 时长=3.68秒, 累计时长=3.68秒
2025-08-07 17:41:50,371 - INFO - 起点场景时长不足，需要延伸填充 0.29秒
2025-08-07 17:41:50,371 - INFO - 起点场景在原始列表中的索引: 3431
2025-08-07 17:41:50,371 - INFO - 延伸添加场景: scene_id=3433 (裁剪至 0.29秒)
2025-08-07 17:41:50,371 - INFO - 累计时长: 3.97秒
2025-08-07 17:41:50,371 - INFO - 字幕序号 #3090 场景匹配完成，共选择 2 个场景，总时长: 3.97秒
2025-08-07 17:41:50,371 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-08-07 17:41:50,371 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:50,371 - INFO - ========== 当前模式：为字幕 #69 生成 1 套场景方案 ==========
2025-08-07 17:41:50,371 - INFO - 开始查找字幕序号 [3086, 3090] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:50,372 - INFO - 找到related_overlap场景: scene_id=3426, 字幕#3086
2025-08-07 17:41:50,372 - INFO - 找到related_overlap场景: scene_id=3428, 字幕#3086
2025-08-07 17:41:50,372 - INFO - 找到related_overlap场景: scene_id=3432, 字幕#3090
2025-08-07 17:41:50,372 - INFO - 字幕 #3086 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:50,372 - INFO - 字幕 #3090 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:50,372 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-08-07 17:41:50,373 - INFO - 开始生成方案 #1
2025-08-07 17:41:50,373 - INFO - 方案 #1: 为字幕#3086选择初始化overlap场景id=3428
2025-08-07 17:41:50,373 - INFO - 方案 #1: 为字幕#3090选择初始化overlap场景id=3432
2025-08-07 17:41:50,373 - INFO - 方案 #1: 初始选择后，当前总时长=4.32秒
2025-08-07 17:41:50,373 - INFO - 方案 #1: 额外between选择后，当前总时长=4.32秒
2025-08-07 17:41:50,373 - INFO - 方案 #1: 场景总时长(4.32秒)大于音频时长(3.97秒)，需要裁剪
2025-08-07 17:41:50,373 - INFO - 调整前总时长: 4.32秒, 目标时长: 3.97秒
2025-08-07 17:41:50,373 - INFO - 需要裁剪 0.35秒
2025-08-07 17:41:50,373 - INFO - 裁剪最长场景ID=3432：从3.68秒裁剪至3.33秒
2025-08-07 17:41:50,373 - INFO - 调整后总时长: 3.97秒，与目标时长差异: 0.00秒
2025-08-07 17:41:50,373 - INFO - 方案 #1 调整/填充后最终总时长: 3.97秒
2025-08-07 17:41:50,373 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:50,373 - INFO - ========== 当前模式：字幕 #69 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:50,373 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:50,373 - INFO - ========== 新模式：字幕 #69 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:50,373 - INFO - 
----- 处理字幕 #69 的方案 #1 -----
2025-08-07 17:41:50,373 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\69_1.mp4
2025-08-07 17:41:50,373 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpurhzixzd
2025-08-07 17:41:50,373 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3426.mp4 (确认存在: True)
2025-08-07 17:41:50,373 - INFO - 添加场景ID=3426，时长=0.88秒，累计时长=0.88秒
2025-08-07 17:41:50,373 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3427.mp4 (确认存在: True)
2025-08-07 17:41:50,373 - INFO - 添加场景ID=3427，时长=0.56秒，累计时长=1.44秒
2025-08-07 17:41:50,373 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3428.mp4 (确认存在: True)
2025-08-07 17:41:50,373 - INFO - 添加场景ID=3428，时长=0.64秒，累计时长=2.08秒
2025-08-07 17:41:50,373 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3429.mp4 (确认存在: True)
2025-08-07 17:41:50,373 - INFO - 添加场景ID=3429，时长=0.72秒，累计时长=2.80秒
2025-08-07 17:41:50,375 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3430.mp4 (确认存在: True)
2025-08-07 17:41:50,375 - INFO - 添加场景ID=3430，时长=1.40秒，累计时长=4.20秒
2025-08-07 17:41:50,375 - INFO - 准备合并 5 个场景文件，总时长约 4.20秒
2025-08-07 17:41:50,375 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3426.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3427.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3428.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3429.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3430.mp4'

2025-08-07 17:41:50,375 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpurhzixzd\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpurhzixzd\temp_combined.mp4
2025-08-07 17:41:50,538 - INFO - 合并后的视频时长: 4.32秒，目标音频时长: 3.97秒
2025-08-07 17:41:50,538 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpurhzixzd\temp_combined.mp4 -ss 0 -to 3.968 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\69_1.mp4
2025-08-07 17:41:50,842 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:50,842 - INFO - 目标音频时长: 3.97秒
2025-08-07 17:41:50,842 - INFO - 实际视频时长: 4.02秒
2025-08-07 17:41:50,842 - INFO - 时长差异: 0.05秒 (1.39%)
2025-08-07 17:41:50,842 - INFO - ==========================================
2025-08-07 17:41:50,842 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:50,842 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\69_1.mp4
2025-08-07 17:41:50,842 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpurhzixzd
2025-08-07 17:41:50,889 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:50,889 - INFO -   - 音频时长: 3.97秒
2025-08-07 17:41:50,889 - INFO -   - 视频时长: 4.02秒
2025-08-07 17:41:50,889 - INFO -   - 时长差异: 0.05秒 (1.39%)
2025-08-07 17:41:50,889 - INFO - 
----- 处理字幕 #69 的方案 #2 -----
2025-08-07 17:41:50,889 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\69_2.mp4
2025-08-07 17:41:50,889 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplwzchw12
2025-08-07 17:41:50,890 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3432.mp4 (确认存在: True)
2025-08-07 17:41:50,890 - INFO - 添加场景ID=3432，时长=3.68秒，累计时长=3.68秒
2025-08-07 17:41:50,890 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3433.mp4 (确认存在: True)
2025-08-07 17:41:50,890 - INFO - 添加场景ID=3433，时长=0.96秒，累计时长=4.64秒
2025-08-07 17:41:50,890 - INFO - 准备合并 2 个场景文件，总时长约 4.64秒
2025-08-07 17:41:50,890 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3432.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3433.mp4'

2025-08-07 17:41:50,890 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmplwzchw12\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmplwzchw12\temp_combined.mp4
2025-08-07 17:41:51,010 - INFO - 合并后的视频时长: 4.69秒，目标音频时长: 3.97秒
2025-08-07 17:41:51,010 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmplwzchw12\temp_combined.mp4 -ss 0 -to 3.968 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\69_2.mp4
2025-08-07 17:41:51,295 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:51,295 - INFO - 目标音频时长: 3.97秒
2025-08-07 17:41:51,295 - INFO - 实际视频时长: 4.02秒
2025-08-07 17:41:51,295 - INFO - 时长差异: 0.05秒 (1.39%)
2025-08-07 17:41:51,295 - INFO - ==========================================
2025-08-07 17:41:51,295 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:51,295 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\69_2.mp4
2025-08-07 17:41:51,296 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplwzchw12
2025-08-07 17:41:51,342 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:51,342 - INFO -   - 音频时长: 3.97秒
2025-08-07 17:41:51,342 - INFO -   - 视频时长: 4.02秒
2025-08-07 17:41:51,342 - INFO -   - 时长差异: 0.05秒 (1.39%)
2025-08-07 17:41:51,342 - INFO - 
----- 处理字幕 #69 的方案 #3 -----
2025-08-07 17:41:51,342 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\69_3.mp4
2025-08-07 17:41:51,342 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpnny0jua1
2025-08-07 17:41:51,343 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3428.mp4 (确认存在: True)
2025-08-07 17:41:51,343 - INFO - 添加场景ID=3428，时长=0.64秒，累计时长=0.64秒
2025-08-07 17:41:51,343 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3432.mp4 (确认存在: True)
2025-08-07 17:41:51,343 - INFO - 添加场景ID=3432，时长=3.68秒，累计时长=4.32秒
2025-08-07 17:41:51,343 - INFO - 准备合并 2 个场景文件，总时长约 4.32秒
2025-08-07 17:41:51,343 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3428.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3432.mp4'

2025-08-07 17:41:51,343 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpnny0jua1\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpnny0jua1\temp_combined.mp4
2025-08-07 17:41:51,455 - INFO - 合并后的视频时长: 4.37秒，目标音频时长: 3.97秒
2025-08-07 17:41:51,455 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpnny0jua1\temp_combined.mp4 -ss 0 -to 3.968 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\69_3.mp4
2025-08-07 17:41:51,740 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:51,740 - INFO - 目标音频时长: 3.97秒
2025-08-07 17:41:51,740 - INFO - 实际视频时长: 4.02秒
2025-08-07 17:41:51,740 - INFO - 时长差异: 0.05秒 (1.39%)
2025-08-07 17:41:51,740 - INFO - ==========================================
2025-08-07 17:41:51,740 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:51,740 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\69_3.mp4
2025-08-07 17:41:51,740 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpnny0jua1
2025-08-07 17:41:51,783 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:51,783 - INFO -   - 音频时长: 3.97秒
2025-08-07 17:41:51,783 - INFO -   - 视频时长: 4.02秒
2025-08-07 17:41:51,783 - INFO -   - 时长差异: 0.05秒 (1.39%)
2025-08-07 17:41:51,783 - INFO - 
字幕 #69 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:51,783 - INFO - 生成的视频文件:
2025-08-07 17:41:51,783 - INFO -   1. F:/github/aicut_auto/newcut_ai\69_1.mp4
2025-08-07 17:41:51,783 - INFO -   2. F:/github/aicut_auto/newcut_ai\69_2.mp4
2025-08-07 17:41:51,783 - INFO -   3. F:/github/aicut_auto/newcut_ai\69_3.mp4
2025-08-07 17:41:51,783 - INFO - ========== 字幕 #69 处理结束 ==========

