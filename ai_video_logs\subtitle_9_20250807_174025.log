2025-08-07 17:40:25,438 - INFO - ========== 字幕 #9 处理开始 ==========
2025-08-07 17:40:25,438 - INFO - 字幕内容: 爷爷一眼认出女孩身上的玉佩，神情激动，他知道，眼前这个小女孩，正是自己那位失散多年的老祖宗。
2025-08-07 17:40:25,438 - INFO - 字幕序号: [103, 111]
2025-08-07 17:40:25,438 - INFO - 音频文件详情:
2025-08-07 17:40:25,439 - INFO -   - 路径: output\9.wav
2025-08-07 17:40:25,439 - INFO -   - 时长: 4.06秒
2025-08-07 17:40:25,439 - INFO -   - 验证音频时长: 4.06秒
2025-08-07 17:40:25,439 - INFO - 字幕时间戳信息:
2025-08-07 17:40:25,449 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:25,449 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:25,449 - INFO -   - 根据生成的音频时长(4.06秒)已调整字幕时间戳
2025-08-07 17:40:25,449 - INFO - ========== 新模式：为字幕 #9 生成4套场景方案 ==========
2025-08-07 17:40:25,449 - INFO - 字幕序号列表: [103, 111]
2025-08-07 17:40:25,449 - INFO - 
--- 生成方案 #1：基于字幕序号 #103 ---
2025-08-07 17:40:25,449 - INFO - 开始为单个字幕序号 #103 匹配场景，目标时长: 4.06秒
2025-08-07 17:40:25,449 - INFO - 开始查找字幕序号 [103] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:25,449 - INFO - 找到related_overlap场景: scene_id=165, 字幕#103
2025-08-07 17:40:25,449 - INFO - 找到related_overlap场景: scene_id=166, 字幕#103
2025-08-07 17:40:25,450 - INFO - 找到related_between场景: scene_id=163, 字幕#103
2025-08-07 17:40:25,450 - INFO - 找到related_between场景: scene_id=164, 字幕#103
2025-08-07 17:40:25,451 - INFO - 字幕 #103 找到 2 个overlap场景, 2 个between场景
2025-08-07 17:40:25,451 - INFO - 字幕序号 #103 找到 2 个可用overlap场景, 2 个可用between场景
2025-08-07 17:40:25,451 - INFO - 选择第一个overlap场景作为起点: scene_id=165
2025-08-07 17:40:25,451 - INFO - 添加起点场景: scene_id=165, 时长=2.56秒, 累计时长=2.56秒
2025-08-07 17:40:25,451 - INFO - 起点场景时长不足，需要延伸填充 1.50秒
2025-08-07 17:40:25,451 - INFO - 起点场景在原始列表中的索引: 164
2025-08-07 17:40:25,451 - INFO - 延伸添加场景: scene_id=166 (完整时长 1.44秒)
2025-08-07 17:40:25,451 - INFO - 累计时长: 4.00秒
2025-08-07 17:40:25,451 - INFO - 延伸添加场景: scene_id=167 (裁剪至 0.06秒)
2025-08-07 17:40:25,451 - INFO - 累计时长: 4.06秒
2025-08-07 17:40:25,451 - INFO - 字幕序号 #103 场景匹配完成，共选择 3 个场景，总时长: 4.06秒
2025-08-07 17:40:25,451 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-08-07 17:40:25,451 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-08-07 17:40:25,451 - INFO - 
--- 生成方案 #2：基于字幕序号 #111 ---
2025-08-07 17:40:25,451 - INFO - 开始为单个字幕序号 #111 匹配场景，目标时长: 4.06秒
2025-08-07 17:40:25,451 - INFO - 开始查找字幕序号 [111] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:25,451 - INFO - 找到related_overlap场景: scene_id=169, 字幕#111
2025-08-07 17:40:25,452 - INFO - 字幕 #111 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:25,452 - INFO - 字幕序号 #111 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:25,452 - INFO - 选择第一个overlap场景作为起点: scene_id=169
2025-08-07 17:40:25,452 - INFO - 添加起点场景: scene_id=169, 时长=3.40秒, 累计时长=3.40秒
2025-08-07 17:40:25,452 - INFO - 起点场景时长不足，需要延伸填充 0.66秒
2025-08-07 17:40:25,452 - INFO - 起点场景在原始列表中的索引: 168
2025-08-07 17:40:25,452 - INFO - 延伸添加场景: scene_id=170 (裁剪至 0.66秒)
2025-08-07 17:40:25,452 - INFO - 累计时长: 4.06秒
2025-08-07 17:40:25,452 - INFO - 字幕序号 #111 场景匹配完成，共选择 2 个场景，总时长: 4.06秒
2025-08-07 17:40:25,452 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-08-07 17:40:25,453 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:25,453 - INFO - ========== 当前模式：为字幕 #9 生成 1 套场景方案 ==========
2025-08-07 17:40:25,453 - INFO - 开始查找字幕序号 [103, 111] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:25,453 - INFO - 找到related_overlap场景: scene_id=165, 字幕#103
2025-08-07 17:40:25,453 - INFO - 找到related_overlap场景: scene_id=166, 字幕#103
2025-08-07 17:40:25,453 - INFO - 找到related_overlap场景: scene_id=169, 字幕#111
2025-08-07 17:40:25,453 - INFO - 找到related_between场景: scene_id=163, 字幕#103
2025-08-07 17:40:25,453 - INFO - 找到related_between场景: scene_id=164, 字幕#103
2025-08-07 17:40:25,453 - INFO - 字幕 #103 找到 2 个overlap场景, 2 个between场景
2025-08-07 17:40:25,453 - INFO - 字幕 #111 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:25,453 - INFO - 共收集 3 个未使用的overlap场景和 2 个未使用的between场景
2025-08-07 17:40:25,453 - INFO - 开始生成方案 #1
2025-08-07 17:40:25,453 - INFO - 方案 #1: 为字幕#103选择初始化overlap场景id=165
2025-08-07 17:40:25,453 - INFO - 方案 #1: 为字幕#111选择初始化overlap场景id=169
2025-08-07 17:40:25,453 - INFO - 方案 #1: 初始选择后，当前总时长=5.96秒
2025-08-07 17:40:25,453 - INFO - 方案 #1: 额外between选择后，当前总时长=5.96秒
2025-08-07 17:40:25,453 - INFO - 方案 #1: 场景总时长(5.96秒)大于音频时长(4.06秒)，需要裁剪
2025-08-07 17:40:25,453 - INFO - 调整前总时长: 5.96秒, 目标时长: 4.06秒
2025-08-07 17:40:25,453 - INFO - 需要裁剪 1.90秒
2025-08-07 17:40:25,453 - INFO - 裁剪最长场景ID=169：从3.40秒裁剪至1.50秒
2025-08-07 17:40:25,453 - INFO - 调整后总时长: 4.06秒，与目标时长差异: 0.00秒
2025-08-07 17:40:25,453 - INFO - 方案 #1 调整/填充后最终总时长: 4.06秒
2025-08-07 17:40:25,453 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:25,453 - INFO - ========== 当前模式：字幕 #9 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:25,453 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:25,453 - INFO - ========== 新模式：字幕 #9 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:25,454 - INFO - 
----- 处理字幕 #9 的方案 #1 -----
2025-08-07 17:40:25,454 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\9_1.mp4
2025-08-07 17:40:25,454 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpengcn6rt
2025-08-07 17:40:25,454 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\165.mp4 (确认存在: True)
2025-08-07 17:40:25,454 - INFO - 添加场景ID=165，时长=2.56秒，累计时长=2.56秒
2025-08-07 17:40:25,454 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\166.mp4 (确认存在: True)
2025-08-07 17:40:25,454 - INFO - 添加场景ID=166，时长=1.44秒，累计时长=4.00秒
2025-08-07 17:40:25,455 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\167.mp4 (确认存在: True)
2025-08-07 17:40:25,455 - INFO - 添加场景ID=167，时长=2.00秒，累计时长=6.00秒
2025-08-07 17:40:25,455 - INFO - 准备合并 3 个场景文件，总时长约 6.00秒
2025-08-07 17:40:25,455 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/165.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/166.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/167.mp4'

2025-08-07 17:40:25,455 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpengcn6rt\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpengcn6rt\temp_combined.mp4
2025-08-07 17:40:25,573 - INFO - 合并后的视频时长: 6.07秒，目标音频时长: 4.06秒
2025-08-07 17:40:25,573 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpengcn6rt\temp_combined.mp4 -ss 0 -to 4.06 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\9_1.mp4
2025-08-07 17:40:25,848 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:25,849 - INFO - 目标音频时长: 4.06秒
2025-08-07 17:40:25,849 - INFO - 实际视频时长: 4.10秒
2025-08-07 17:40:25,849 - INFO - 时长差异: 0.04秒 (1.06%)
2025-08-07 17:40:25,849 - INFO - ==========================================
2025-08-07 17:40:25,849 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:25,849 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\9_1.mp4
2025-08-07 17:40:25,849 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpengcn6rt
2025-08-07 17:40:25,891 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:25,891 - INFO -   - 音频时长: 4.06秒
2025-08-07 17:40:25,891 - INFO -   - 视频时长: 4.10秒
2025-08-07 17:40:25,891 - INFO -   - 时长差异: 0.04秒 (1.06%)
2025-08-07 17:40:25,891 - INFO - 
----- 处理字幕 #9 的方案 #2 -----
2025-08-07 17:40:25,891 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\9_2.mp4
2025-08-07 17:40:25,892 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp48ol72l5
2025-08-07 17:40:25,892 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\169.mp4 (确认存在: True)
2025-08-07 17:40:25,892 - INFO - 添加场景ID=169，时长=3.40秒，累计时长=3.40秒
2025-08-07 17:40:25,892 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\170.mp4 (确认存在: True)
2025-08-07 17:40:25,892 - INFO - 添加场景ID=170，时长=2.72秒，累计时长=6.12秒
2025-08-07 17:40:25,892 - INFO - 场景总时长(6.12秒)已达到音频时长(4.06秒)的1.5倍，停止添加场景
2025-08-07 17:40:25,892 - INFO - 准备合并 2 个场景文件，总时长约 6.12秒
2025-08-07 17:40:25,892 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/169.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/170.mp4'

2025-08-07 17:40:25,893 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp48ol72l5\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp48ol72l5\temp_combined.mp4
2025-08-07 17:40:26,024 - INFO - 合并后的视频时长: 6.17秒，目标音频时长: 4.06秒
2025-08-07 17:40:26,024 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp48ol72l5\temp_combined.mp4 -ss 0 -to 4.06 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\9_2.mp4
2025-08-07 17:40:26,302 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:26,302 - INFO - 目标音频时长: 4.06秒
2025-08-07 17:40:26,302 - INFO - 实际视频时长: 4.10秒
2025-08-07 17:40:26,302 - INFO - 时长差异: 0.04秒 (1.06%)
2025-08-07 17:40:26,302 - INFO - ==========================================
2025-08-07 17:40:26,302 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:26,302 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\9_2.mp4
2025-08-07 17:40:26,303 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp48ol72l5
2025-08-07 17:40:26,349 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:26,349 - INFO -   - 音频时长: 4.06秒
2025-08-07 17:40:26,349 - INFO -   - 视频时长: 4.10秒
2025-08-07 17:40:26,349 - INFO -   - 时长差异: 0.04秒 (1.06%)
2025-08-07 17:40:26,349 - INFO - 
----- 处理字幕 #9 的方案 #3 -----
2025-08-07 17:40:26,349 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\9_3.mp4
2025-08-07 17:40:26,349 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbmt5c6y5
2025-08-07 17:40:26,350 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\165.mp4 (确认存在: True)
2025-08-07 17:40:26,350 - INFO - 添加场景ID=165，时长=2.56秒，累计时长=2.56秒
2025-08-07 17:40:26,350 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\169.mp4 (确认存在: True)
2025-08-07 17:40:26,350 - INFO - 添加场景ID=169，时长=3.40秒，累计时长=5.96秒
2025-08-07 17:40:26,350 - INFO - 准备合并 2 个场景文件，总时长约 5.96秒
2025-08-07 17:40:26,351 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/165.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/169.mp4'

2025-08-07 17:40:26,351 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpbmt5c6y5\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpbmt5c6y5\temp_combined.mp4
2025-08-07 17:40:26,454 - INFO - 合并后的视频时长: 6.01秒，目标音频时长: 4.06秒
2025-08-07 17:40:26,454 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpbmt5c6y5\temp_combined.mp4 -ss 0 -to 4.06 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\9_3.mp4
2025-08-07 17:40:26,723 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:26,723 - INFO - 目标音频时长: 4.06秒
2025-08-07 17:40:26,723 - INFO - 实际视频时长: 4.10秒
2025-08-07 17:40:26,723 - INFO - 时长差异: 0.04秒 (1.06%)
2025-08-07 17:40:26,723 - INFO - ==========================================
2025-08-07 17:40:26,723 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:26,723 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\9_3.mp4
2025-08-07 17:40:26,724 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbmt5c6y5
2025-08-07 17:40:26,767 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:26,767 - INFO -   - 音频时长: 4.06秒
2025-08-07 17:40:26,767 - INFO -   - 视频时长: 4.10秒
2025-08-07 17:40:26,767 - INFO -   - 时长差异: 0.04秒 (1.06%)
2025-08-07 17:40:26,767 - INFO - 
字幕 #9 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:26,767 - INFO - 生成的视频文件:
2025-08-07 17:40:26,767 - INFO -   1. F:/github/aicut_auto/newcut_ai\9_1.mp4
2025-08-07 17:40:26,767 - INFO -   2. F:/github/aicut_auto/newcut_ai\9_2.mp4
2025-08-07 17:40:26,767 - INFO -   3. F:/github/aicut_auto/newcut_ai\9_3.mp4
2025-08-07 17:40:26,767 - INFO - ========== 字幕 #9 处理结束 ==========

