2025-08-07 17:40:28,203 - INFO - ========== 字幕 #11 处理开始 ==========
2025-08-07 17:40:28,203 - INFO - 字幕内容: 门外，二弟野心毕露，他绝不甘心让一个残废抢走楚家继承权，誓要让大哥身败名裂。
2025-08-07 17:40:28,203 - INFO - 字幕序号: [216, 220]
2025-08-07 17:40:28,203 - INFO - 音频文件详情:
2025-08-07 17:40:28,203 - INFO -   - 路径: output\11.wav
2025-08-07 17:40:28,203 - INFO -   - 时长: 5.11秒
2025-08-07 17:40:28,203 - INFO -   - 验证音频时长: 5.11秒
2025-08-07 17:40:28,203 - INFO - 字幕时间戳信息:
2025-08-07 17:40:28,203 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:28,203 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:28,203 - INFO -   - 根据生成的音频时长(5.11秒)已调整字幕时间戳
2025-08-07 17:40:28,203 - INFO - ========== 新模式：为字幕 #11 生成4套场景方案 ==========
2025-08-07 17:40:28,203 - INFO - 字幕序号列表: [216, 220]
2025-08-07 17:40:28,203 - INFO - 
--- 生成方案 #1：基于字幕序号 #216 ---
2025-08-07 17:40:28,203 - INFO - 开始为单个字幕序号 #216 匹配场景，目标时长: 5.11秒
2025-08-07 17:40:28,203 - INFO - 开始查找字幕序号 [216] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:28,204 - INFO - 找到related_overlap场景: scene_id=323, 字幕#216
2025-08-07 17:40:28,204 - INFO - 找到related_between场景: scene_id=316, 字幕#216
2025-08-07 17:40:28,204 - INFO - 找到related_between场景: scene_id=317, 字幕#216
2025-08-07 17:40:28,204 - INFO - 找到related_between场景: scene_id=318, 字幕#216
2025-08-07 17:40:28,204 - INFO - 找到related_between场景: scene_id=319, 字幕#216
2025-08-07 17:40:28,204 - INFO - 找到related_between场景: scene_id=320, 字幕#216
2025-08-07 17:40:28,204 - INFO - 找到related_between场景: scene_id=321, 字幕#216
2025-08-07 17:40:28,204 - INFO - 找到related_between场景: scene_id=322, 字幕#216
2025-08-07 17:40:28,205 - INFO - 字幕 #216 找到 1 个overlap场景, 7 个between场景
2025-08-07 17:40:28,205 - INFO - 字幕序号 #216 找到 1 个可用overlap场景, 7 个可用between场景
2025-08-07 17:40:28,205 - INFO - 选择第一个overlap场景作为起点: scene_id=323
2025-08-07 17:40:28,205 - INFO - 添加起点场景: scene_id=323, 时长=1.68秒, 累计时长=1.68秒
2025-08-07 17:40:28,205 - INFO - 起点场景时长不足，需要延伸填充 3.43秒
2025-08-07 17:40:28,205 - INFO - 起点场景在原始列表中的索引: 322
2025-08-07 17:40:28,205 - INFO - 延伸添加场景: scene_id=324 (完整时长 3.12秒)
2025-08-07 17:40:28,205 - INFO - 累计时长: 4.80秒
2025-08-07 17:40:28,205 - INFO - 延伸添加场景: scene_id=325 (裁剪至 0.31秒)
2025-08-07 17:40:28,205 - INFO - 累计时长: 5.11秒
2025-08-07 17:40:28,205 - INFO - 字幕序号 #216 场景匹配完成，共选择 3 个场景，总时长: 5.11秒
2025-08-07 17:40:28,205 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-08-07 17:40:28,205 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-08-07 17:40:28,205 - INFO - 
--- 生成方案 #2：基于字幕序号 #220 ---
2025-08-07 17:40:28,205 - INFO - 开始为单个字幕序号 #220 匹配场景，目标时长: 5.11秒
2025-08-07 17:40:28,205 - INFO - 开始查找字幕序号 [220] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:28,205 - INFO - 找到related_overlap场景: scene_id=326, 字幕#220
2025-08-07 17:40:28,206 - INFO - 找到related_between场景: scene_id=327, 字幕#220
2025-08-07 17:40:28,206 - INFO - 找到related_between场景: scene_id=328, 字幕#220
2025-08-07 17:40:28,206 - INFO - 找到related_between场景: scene_id=329, 字幕#220
2025-08-07 17:40:28,206 - INFO - 找到related_between场景: scene_id=330, 字幕#220
2025-08-07 17:40:28,206 - INFO - 找到related_between场景: scene_id=331, 字幕#220
2025-08-07 17:40:28,206 - INFO - 找到related_between场景: scene_id=332, 字幕#220
2025-08-07 17:40:28,206 - INFO - 找到related_between场景: scene_id=333, 字幕#220
2025-08-07 17:40:28,206 - INFO - 找到related_between场景: scene_id=334, 字幕#220
2025-08-07 17:40:28,206 - INFO - 找到related_between场景: scene_id=335, 字幕#220
2025-08-07 17:40:28,206 - INFO - 找到related_between场景: scene_id=336, 字幕#220
2025-08-07 17:40:28,207 - INFO - 字幕 #220 找到 1 个overlap场景, 10 个between场景
2025-08-07 17:40:28,207 - INFO - 字幕序号 #220 找到 1 个可用overlap场景, 10 个可用between场景
2025-08-07 17:40:28,207 - INFO - 选择第一个overlap场景作为起点: scene_id=326
2025-08-07 17:40:28,207 - INFO - 添加起点场景: scene_id=326, 时长=1.04秒, 累计时长=1.04秒
2025-08-07 17:40:28,207 - INFO - 起点场景时长不足，需要延伸填充 4.07秒
2025-08-07 17:40:28,207 - INFO - 起点场景在原始列表中的索引: 325
2025-08-07 17:40:28,207 - INFO - 延伸添加场景: scene_id=327 (裁剪至 4.07秒)
2025-08-07 17:40:28,207 - INFO - 累计时长: 5.11秒
2025-08-07 17:40:28,207 - INFO - 字幕序号 #220 场景匹配完成，共选择 2 个场景，总时长: 5.11秒
2025-08-07 17:40:28,207 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-08-07 17:40:28,207 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:28,207 - INFO - ========== 当前模式：为字幕 #11 生成 1 套场景方案 ==========
2025-08-07 17:40:28,207 - INFO - 开始查找字幕序号 [216, 220] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:28,207 - INFO - 找到related_overlap场景: scene_id=323, 字幕#216
2025-08-07 17:40:28,207 - INFO - 找到related_overlap场景: scene_id=326, 字幕#220
2025-08-07 17:40:28,208 - INFO - 找到related_between场景: scene_id=316, 字幕#216
2025-08-07 17:40:28,208 - INFO - 找到related_between场景: scene_id=317, 字幕#216
2025-08-07 17:40:28,208 - INFO - 找到related_between场景: scene_id=318, 字幕#216
2025-08-07 17:40:28,208 - INFO - 找到related_between场景: scene_id=319, 字幕#216
2025-08-07 17:40:28,208 - INFO - 找到related_between场景: scene_id=320, 字幕#216
2025-08-07 17:40:28,208 - INFO - 找到related_between场景: scene_id=321, 字幕#216
2025-08-07 17:40:28,208 - INFO - 找到related_between场景: scene_id=322, 字幕#216
2025-08-07 17:40:28,208 - INFO - 找到related_between场景: scene_id=327, 字幕#220
2025-08-07 17:40:28,208 - INFO - 找到related_between场景: scene_id=328, 字幕#220
2025-08-07 17:40:28,208 - INFO - 找到related_between场景: scene_id=329, 字幕#220
2025-08-07 17:40:28,208 - INFO - 找到related_between场景: scene_id=330, 字幕#220
2025-08-07 17:40:28,208 - INFO - 找到related_between场景: scene_id=331, 字幕#220
2025-08-07 17:40:28,208 - INFO - 找到related_between场景: scene_id=332, 字幕#220
2025-08-07 17:40:28,208 - INFO - 找到related_between场景: scene_id=333, 字幕#220
2025-08-07 17:40:28,208 - INFO - 找到related_between场景: scene_id=334, 字幕#220
2025-08-07 17:40:28,208 - INFO - 找到related_between场景: scene_id=335, 字幕#220
2025-08-07 17:40:28,208 - INFO - 找到related_between场景: scene_id=336, 字幕#220
2025-08-07 17:40:28,208 - INFO - 字幕 #216 找到 1 个overlap场景, 7 个between场景
2025-08-07 17:40:28,208 - INFO - 字幕 #220 找到 1 个overlap场景, 10 个between场景
2025-08-07 17:40:28,208 - INFO - 共收集 2 个未使用的overlap场景和 17 个未使用的between场景
2025-08-07 17:40:28,208 - INFO - 开始生成方案 #1
2025-08-07 17:40:28,208 - INFO - 方案 #1: 为字幕#216选择初始化overlap场景id=323
2025-08-07 17:40:28,208 - INFO - 方案 #1: 为字幕#220选择初始化overlap场景id=326
2025-08-07 17:40:28,208 - INFO - 方案 #1: 初始选择后，当前总时长=2.72秒
2025-08-07 17:40:28,208 - INFO - 方案 #1: 额外between选择后，当前总时长=2.72秒
2025-08-07 17:40:28,208 - INFO - 方案 #1: 额外添加between场景id=316, 当前总时长=5.68秒
2025-08-07 17:40:28,208 - INFO - 方案 #1: 场景总时长(5.68秒)大于音频时长(5.11秒)，需要裁剪
2025-08-07 17:40:28,208 - INFO - 调整前总时长: 5.68秒, 目标时长: 5.11秒
2025-08-07 17:40:28,208 - INFO - 需要裁剪 0.57秒
2025-08-07 17:40:28,208 - INFO - 裁剪最长场景ID=316：从2.96秒裁剪至2.39秒
2025-08-07 17:40:28,209 - INFO - 调整后总时长: 5.11秒，与目标时长差异: 0.00秒
2025-08-07 17:40:28,209 - INFO - 方案 #1 调整/填充后最终总时长: 5.11秒
2025-08-07 17:40:28,209 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:28,209 - INFO - ========== 当前模式：字幕 #11 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:28,209 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:28,209 - INFO - ========== 新模式：字幕 #11 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:28,209 - INFO - 
----- 处理字幕 #11 的方案 #1 -----
2025-08-07 17:40:28,209 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\11_1.mp4
2025-08-07 17:40:28,209 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpash8kxjl
2025-08-07 17:40:28,209 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\323.mp4 (确认存在: True)
2025-08-07 17:40:28,209 - INFO - 添加场景ID=323，时长=1.68秒，累计时长=1.68秒
2025-08-07 17:40:28,210 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\324.mp4 (确认存在: True)
2025-08-07 17:40:28,210 - INFO - 添加场景ID=324，时长=3.12秒，累计时长=4.80秒
2025-08-07 17:40:28,210 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\325.mp4 (确认存在: True)
2025-08-07 17:40:28,210 - INFO - 添加场景ID=325，时长=0.76秒，累计时长=5.56秒
2025-08-07 17:40:28,210 - INFO - 准备合并 3 个场景文件，总时长约 5.56秒
2025-08-07 17:40:28,210 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/323.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/324.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/325.mp4'

2025-08-07 17:40:28,210 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpash8kxjl\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpash8kxjl\temp_combined.mp4
2025-08-07 17:40:28,335 - INFO - 合并后的视频时长: 5.63秒，目标音频时长: 5.11秒
2025-08-07 17:40:28,335 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpash8kxjl\temp_combined.mp4 -ss 0 -to 5.112 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\11_1.mp4
2025-08-07 17:40:28,628 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:28,628 - INFO - 目标音频时长: 5.11秒
2025-08-07 17:40:28,628 - INFO - 实际视频时长: 5.14秒
2025-08-07 17:40:28,628 - INFO - 时长差异: 0.03秒 (0.61%)
2025-08-07 17:40:28,628 - INFO - ==========================================
2025-08-07 17:40:28,628 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:28,628 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\11_1.mp4
2025-08-07 17:40:28,629 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpash8kxjl
2025-08-07 17:40:28,672 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:28,672 - INFO -   - 音频时长: 5.11秒
2025-08-07 17:40:28,672 - INFO -   - 视频时长: 5.14秒
2025-08-07 17:40:28,672 - INFO -   - 时长差异: 0.03秒 (0.61%)
2025-08-07 17:40:28,672 - INFO - 
----- 处理字幕 #11 的方案 #2 -----
2025-08-07 17:40:28,672 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\11_2.mp4
2025-08-07 17:40:28,672 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8ts9nn49
2025-08-07 17:40:28,673 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\326.mp4 (确认存在: True)
2025-08-07 17:40:28,673 - INFO - 添加场景ID=326，时长=1.04秒，累计时长=1.04秒
2025-08-07 17:40:28,673 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\327.mp4 (确认存在: True)
2025-08-07 17:40:28,673 - INFO - 添加场景ID=327，时长=4.80秒，累计时长=5.84秒
2025-08-07 17:40:28,673 - INFO - 准备合并 2 个场景文件，总时长约 5.84秒
2025-08-07 17:40:28,673 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/326.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/327.mp4'

2025-08-07 17:40:28,673 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp8ts9nn49\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp8ts9nn49\temp_combined.mp4
2025-08-07 17:40:28,798 - INFO - 合并后的视频时长: 5.89秒，目标音频时长: 5.11秒
2025-08-07 17:40:28,798 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp8ts9nn49\temp_combined.mp4 -ss 0 -to 5.112 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\11_2.mp4
2025-08-07 17:40:29,103 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:29,103 - INFO - 目标音频时长: 5.11秒
2025-08-07 17:40:29,103 - INFO - 实际视频时长: 5.14秒
2025-08-07 17:40:29,103 - INFO - 时长差异: 0.03秒 (0.61%)
2025-08-07 17:40:29,103 - INFO - ==========================================
2025-08-07 17:40:29,103 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:29,103 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\11_2.mp4
2025-08-07 17:40:29,103 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8ts9nn49
2025-08-07 17:40:29,146 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:29,146 - INFO -   - 音频时长: 5.11秒
2025-08-07 17:40:29,146 - INFO -   - 视频时长: 5.14秒
2025-08-07 17:40:29,146 - INFO -   - 时长差异: 0.03秒 (0.61%)
2025-08-07 17:40:29,146 - INFO - 
----- 处理字幕 #11 的方案 #3 -----
2025-08-07 17:40:29,146 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\11_3.mp4
2025-08-07 17:40:29,147 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpktdb070m
2025-08-07 17:40:29,147 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\323.mp4 (确认存在: True)
2025-08-07 17:40:29,147 - INFO - 添加场景ID=323，时长=1.68秒，累计时长=1.68秒
2025-08-07 17:40:29,147 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\326.mp4 (确认存在: True)
2025-08-07 17:40:29,147 - INFO - 添加场景ID=326，时长=1.04秒，累计时长=2.72秒
2025-08-07 17:40:29,147 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\316.mp4 (确认存在: True)
2025-08-07 17:40:29,147 - INFO - 添加场景ID=316，时长=2.96秒，累计时长=5.68秒
2025-08-07 17:40:29,147 - INFO - 准备合并 3 个场景文件，总时长约 5.68秒
2025-08-07 17:40:29,147 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/323.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/326.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/316.mp4'

2025-08-07 17:40:29,148 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpktdb070m\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpktdb070m\temp_combined.mp4
2025-08-07 17:40:29,276 - INFO - 合并后的视频时长: 5.75秒，目标音频时长: 5.11秒
2025-08-07 17:40:29,276 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpktdb070m\temp_combined.mp4 -ss 0 -to 5.112 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\11_3.mp4
2025-08-07 17:40:29,586 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:29,586 - INFO - 目标音频时长: 5.11秒
2025-08-07 17:40:29,587 - INFO - 实际视频时长: 5.14秒
2025-08-07 17:40:29,587 - INFO - 时长差异: 0.03秒 (0.61%)
2025-08-07 17:40:29,587 - INFO - ==========================================
2025-08-07 17:40:29,587 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:29,587 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\11_3.mp4
2025-08-07 17:40:29,587 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpktdb070m
2025-08-07 17:40:29,630 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:29,630 - INFO -   - 音频时长: 5.11秒
2025-08-07 17:40:29,630 - INFO -   - 视频时长: 5.14秒
2025-08-07 17:40:29,630 - INFO -   - 时长差异: 0.03秒 (0.61%)
2025-08-07 17:40:29,630 - INFO - 
字幕 #11 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:29,630 - INFO - 生成的视频文件:
2025-08-07 17:40:29,630 - INFO -   1. F:/github/aicut_auto/newcut_ai\11_1.mp4
2025-08-07 17:40:29,630 - INFO -   2. F:/github/aicut_auto/newcut_ai\11_2.mp4
2025-08-07 17:40:29,630 - INFO -   3. F:/github/aicut_auto/newcut_ai\11_3.mp4
2025-08-07 17:40:29,630 - INFO - ========== 字幕 #11 处理结束 ==========

