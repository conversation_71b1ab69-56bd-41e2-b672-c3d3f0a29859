2025-08-07 17:41:44,183 - INFO - ========== 字幕 #65 处理开始 ==========
2025-08-07 17:41:44,183 - INFO - 字幕内容: 女孩瞬移而至，终于见到了那个阴德亏损的楚家后人，竟是爷爷失散多年的亲弟弟。
2025-08-07 17:41:44,183 - INFO - 字幕序号: [3001, 3008]
2025-08-07 17:41:44,183 - INFO - 音频文件详情:
2025-08-07 17:41:44,183 - INFO -   - 路径: output\65.wav
2025-08-07 17:41:44,183 - INFO -   - 时长: 5.17秒
2025-08-07 17:41:44,183 - INFO -   - 验证音频时长: 5.17秒
2025-08-07 17:41:44,183 - INFO - 字幕时间戳信息:
2025-08-07 17:41:44,183 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:44,183 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:44,183 - INFO -   - 根据生成的音频时长(5.17秒)已调整字幕时间戳
2025-08-07 17:41:44,183 - INFO - ========== 新模式：为字幕 #65 生成4套场景方案 ==========
2025-08-07 17:41:44,183 - INFO - 字幕序号列表: [3001, 3008]
2025-08-07 17:41:44,183 - INFO - 
--- 生成方案 #1：基于字幕序号 #3001 ---
2025-08-07 17:41:44,183 - INFO - 开始为单个字幕序号 #3001 匹配场景，目标时长: 5.17秒
2025-08-07 17:41:44,183 - INFO - 开始查找字幕序号 [3001] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:44,185 - INFO - 找到related_overlap场景: scene_id=3283, 字幕#3001
2025-08-07 17:41:44,185 - INFO - 找到related_overlap场景: scene_id=3284, 字幕#3001
2025-08-07 17:41:44,186 - INFO - 字幕 #3001 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:44,186 - INFO - 字幕序号 #3001 找到 2 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:44,186 - INFO - 选择第一个overlap场景作为起点: scene_id=3283
2025-08-07 17:41:44,186 - INFO - 添加起点场景: scene_id=3283, 时长=1.76秒, 累计时长=1.76秒
2025-08-07 17:41:44,186 - INFO - 起点场景时长不足，需要延伸填充 3.41秒
2025-08-07 17:41:44,186 - INFO - 起点场景在原始列表中的索引: 3282
2025-08-07 17:41:44,186 - INFO - 延伸添加场景: scene_id=3284 (完整时长 1.68秒)
2025-08-07 17:41:44,186 - INFO - 累计时长: 3.44秒
2025-08-07 17:41:44,186 - INFO - 延伸添加场景: scene_id=3285 (完整时长 0.92秒)
2025-08-07 17:41:44,186 - INFO - 累计时长: 4.36秒
2025-08-07 17:41:44,186 - INFO - 延伸添加场景: scene_id=3286 (裁剪至 0.81秒)
2025-08-07 17:41:44,186 - INFO - 累计时长: 5.17秒
2025-08-07 17:41:44,186 - INFO - 字幕序号 #3001 场景匹配完成，共选择 4 个场景，总时长: 5.17秒
2025-08-07 17:41:44,186 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-08-07 17:41:44,186 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-08-07 17:41:44,186 - INFO - 
--- 生成方案 #2：基于字幕序号 #3008 ---
2025-08-07 17:41:44,186 - INFO - 开始为单个字幕序号 #3008 匹配场景，目标时长: 5.17秒
2025-08-07 17:41:44,186 - INFO - 开始查找字幕序号 [3008] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:44,187 - INFO - 找到related_overlap场景: scene_id=3298, 字幕#3008
2025-08-07 17:41:44,187 - INFO - 字幕 #3008 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:44,187 - INFO - 字幕序号 #3008 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:44,187 - INFO - 选择第一个overlap场景作为起点: scene_id=3298
2025-08-07 17:41:44,187 - INFO - 添加起点场景: scene_id=3298, 时长=0.76秒, 累计时长=0.76秒
2025-08-07 17:41:44,187 - INFO - 起点场景时长不足，需要延伸填充 4.41秒
2025-08-07 17:41:44,187 - INFO - 起点场景在原始列表中的索引: 3297
2025-08-07 17:41:44,188 - INFO - 延伸添加场景: scene_id=3299 (完整时长 1.08秒)
2025-08-07 17:41:44,188 - INFO - 累计时长: 1.84秒
2025-08-07 17:41:44,188 - INFO - 延伸添加场景: scene_id=3300 (完整时长 1.52秒)
2025-08-07 17:41:44,188 - INFO - 累计时长: 3.36秒
2025-08-07 17:41:44,188 - INFO - 延伸添加场景: scene_id=3301 (完整时长 0.96秒)
2025-08-07 17:41:44,188 - INFO - 累计时长: 4.32秒
2025-08-07 17:41:44,188 - INFO - 延伸添加场景: scene_id=3302 (裁剪至 0.85秒)
2025-08-07 17:41:44,188 - INFO - 累计时长: 5.17秒
2025-08-07 17:41:44,188 - INFO - 字幕序号 #3008 场景匹配完成，共选择 5 个场景，总时长: 5.17秒
2025-08-07 17:41:44,188 - INFO - 方案 #2 生成成功，包含 5 个场景
2025-08-07 17:41:44,188 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:44,188 - INFO - ========== 当前模式：为字幕 #65 生成 1 套场景方案 ==========
2025-08-07 17:41:44,188 - INFO - 开始查找字幕序号 [3001, 3008] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:44,188 - INFO - 找到related_overlap场景: scene_id=3283, 字幕#3001
2025-08-07 17:41:44,188 - INFO - 找到related_overlap场景: scene_id=3284, 字幕#3001
2025-08-07 17:41:44,188 - INFO - 找到related_overlap场景: scene_id=3298, 字幕#3008
2025-08-07 17:41:44,189 - INFO - 字幕 #3001 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:44,189 - INFO - 字幕 #3008 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:44,189 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-08-07 17:41:44,189 - INFO - 开始生成方案 #1
2025-08-07 17:41:44,189 - INFO - 方案 #1: 为字幕#3001选择初始化overlap场景id=3284
2025-08-07 17:41:44,189 - INFO - 方案 #1: 为字幕#3008选择初始化overlap场景id=3298
2025-08-07 17:41:44,189 - INFO - 方案 #1: 初始选择后，当前总时长=2.44秒
2025-08-07 17:41:44,189 - INFO - 方案 #1: 额外添加overlap场景id=3283, 当前总时长=4.20秒
2025-08-07 17:41:44,189 - INFO - 方案 #1: 额外between选择后，当前总时长=4.20秒
2025-08-07 17:41:44,189 - INFO - 方案 #1: 场景总时长(4.20秒)小于音频时长(5.17秒)，需要延伸填充
2025-08-07 17:41:44,189 - INFO - 方案 #1: 最后一个场景ID: 3283
2025-08-07 17:41:44,189 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 3282
2025-08-07 17:41:44,189 - INFO - 方案 #1: 需要填充时长: 0.97秒
2025-08-07 17:41:44,189 - INFO - 方案 #1: 跳过已使用的场景: scene_id=3284
2025-08-07 17:41:44,189 - INFO - 方案 #1: 追加场景 scene_id=3285 (完整时长 0.92秒)
2025-08-07 17:41:44,189 - INFO - 方案 #1: 追加场景 scene_id=3286 (裁剪至 0.05秒)
2025-08-07 17:41:44,189 - INFO - 方案 #1: 成功填充至目标时长
2025-08-07 17:41:44,189 - INFO - 方案 #1 调整/填充后最终总时长: 5.17秒
2025-08-07 17:41:44,189 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:44,189 - INFO - ========== 当前模式：字幕 #65 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:44,189 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:44,189 - INFO - ========== 新模式：字幕 #65 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:44,189 - INFO - 
----- 处理字幕 #65 的方案 #1 -----
2025-08-07 17:41:44,189 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\65_1.mp4
2025-08-07 17:41:44,190 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqpy5mjke
2025-08-07 17:41:44,190 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3283.mp4 (确认存在: True)
2025-08-07 17:41:44,190 - INFO - 添加场景ID=3283，时长=1.76秒，累计时长=1.76秒
2025-08-07 17:41:44,190 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3284.mp4 (确认存在: True)
2025-08-07 17:41:44,190 - INFO - 添加场景ID=3284，时长=1.68秒，累计时长=3.44秒
2025-08-07 17:41:44,190 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3285.mp4 (确认存在: True)
2025-08-07 17:41:44,190 - INFO - 添加场景ID=3285，时长=0.92秒，累计时长=4.36秒
2025-08-07 17:41:44,190 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3286.mp4 (确认存在: True)
2025-08-07 17:41:44,191 - INFO - 添加场景ID=3286，时长=0.88秒，累计时长=5.24秒
2025-08-07 17:41:44,191 - INFO - 准备合并 4 个场景文件，总时长约 5.24秒
2025-08-07 17:41:44,191 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3283.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3284.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3285.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3286.mp4'

2025-08-07 17:41:44,191 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpqpy5mjke\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpqpy5mjke\temp_combined.mp4
2025-08-07 17:41:44,365 - INFO - 合并后的视频时长: 5.33秒，目标音频时长: 5.17秒
2025-08-07 17:41:44,365 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpqpy5mjke\temp_combined.mp4 -ss 0 -to 5.17 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\65_1.mp4
2025-08-07 17:41:44,692 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:44,692 - INFO - 目标音频时长: 5.17秒
2025-08-07 17:41:44,692 - INFO - 实际视频时长: 5.22秒
2025-08-07 17:41:44,692 - INFO - 时长差异: 0.05秒 (1.03%)
2025-08-07 17:41:44,692 - INFO - ==========================================
2025-08-07 17:41:44,692 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:44,692 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\65_1.mp4
2025-08-07 17:41:44,693 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqpy5mjke
2025-08-07 17:41:44,738 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:44,738 - INFO -   - 音频时长: 5.17秒
2025-08-07 17:41:44,738 - INFO -   - 视频时长: 5.22秒
2025-08-07 17:41:44,738 - INFO -   - 时长差异: 0.05秒 (1.03%)
2025-08-07 17:41:44,738 - INFO - 
----- 处理字幕 #65 的方案 #2 -----
2025-08-07 17:41:44,738 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\65_2.mp4
2025-08-07 17:41:44,739 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp7isypqfn
2025-08-07 17:41:44,739 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3298.mp4 (确认存在: True)
2025-08-07 17:41:44,739 - INFO - 添加场景ID=3298，时长=0.76秒，累计时长=0.76秒
2025-08-07 17:41:44,739 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3299.mp4 (确认存在: True)
2025-08-07 17:41:44,739 - INFO - 添加场景ID=3299，时长=1.08秒，累计时长=1.84秒
2025-08-07 17:41:44,739 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3300.mp4 (确认存在: True)
2025-08-07 17:41:44,739 - INFO - 添加场景ID=3300，时长=1.52秒，累计时长=3.36秒
2025-08-07 17:41:44,739 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3301.mp4 (确认存在: True)
2025-08-07 17:41:44,739 - INFO - 添加场景ID=3301，时长=0.96秒，累计时长=4.32秒
2025-08-07 17:41:44,739 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3302.mp4 (确认存在: True)
2025-08-07 17:41:44,739 - INFO - 添加场景ID=3302，时长=1.20秒，累计时长=5.52秒
2025-08-07 17:41:44,739 - INFO - 准备合并 5 个场景文件，总时长约 5.52秒
2025-08-07 17:41:44,740 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3298.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3299.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3300.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3301.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3302.mp4'

2025-08-07 17:41:44,740 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp7isypqfn\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp7isypqfn\temp_combined.mp4
2025-08-07 17:41:44,910 - INFO - 合并后的视频时长: 5.64秒，目标音频时长: 5.17秒
2025-08-07 17:41:44,910 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp7isypqfn\temp_combined.mp4 -ss 0 -to 5.17 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\65_2.mp4
2025-08-07 17:41:45,212 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:45,212 - INFO - 目标音频时长: 5.17秒
2025-08-07 17:41:45,212 - INFO - 实际视频时长: 5.22秒
2025-08-07 17:41:45,212 - INFO - 时长差异: 0.05秒 (1.03%)
2025-08-07 17:41:45,212 - INFO - ==========================================
2025-08-07 17:41:45,212 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:45,212 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\65_2.mp4
2025-08-07 17:41:45,212 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp7isypqfn
2025-08-07 17:41:45,257 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:45,257 - INFO -   - 音频时长: 5.17秒
2025-08-07 17:41:45,257 - INFO -   - 视频时长: 5.22秒
2025-08-07 17:41:45,257 - INFO -   - 时长差异: 0.05秒 (1.03%)
2025-08-07 17:41:45,257 - INFO - 
----- 处理字幕 #65 的方案 #3 -----
2025-08-07 17:41:45,257 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\65_3.mp4
2025-08-07 17:41:45,257 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpybf1qgsf
2025-08-07 17:41:45,258 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3284.mp4 (确认存在: True)
2025-08-07 17:41:45,258 - INFO - 添加场景ID=3284，时长=1.68秒，累计时长=1.68秒
2025-08-07 17:41:45,258 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3298.mp4 (确认存在: True)
2025-08-07 17:41:45,258 - INFO - 添加场景ID=3298，时长=0.76秒，累计时长=2.44秒
2025-08-07 17:41:45,258 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3283.mp4 (确认存在: True)
2025-08-07 17:41:45,259 - INFO - 添加场景ID=3283，时长=1.76秒，累计时长=4.20秒
2025-08-07 17:41:45,259 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3285.mp4 (确认存在: True)
2025-08-07 17:41:45,259 - INFO - 添加场景ID=3285，时长=0.92秒，累计时长=5.12秒
2025-08-07 17:41:45,259 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3286.mp4 (确认存在: True)
2025-08-07 17:41:45,259 - INFO - 添加场景ID=3286，时长=0.88秒，累计时长=6.00秒
2025-08-07 17:41:45,259 - INFO - 准备合并 5 个场景文件，总时长约 6.00秒
2025-08-07 17:41:45,259 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3284.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3298.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3283.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3285.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3286.mp4'

2025-08-07 17:41:45,259 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpybf1qgsf\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpybf1qgsf\temp_combined.mp4
2025-08-07 17:41:45,426 - INFO - 合并后的视频时长: 6.12秒，目标音频时长: 5.17秒
2025-08-07 17:41:45,426 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpybf1qgsf\temp_combined.mp4 -ss 0 -to 5.17 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\65_3.mp4
2025-08-07 17:41:45,749 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:45,749 - INFO - 目标音频时长: 5.17秒
2025-08-07 17:41:45,749 - INFO - 实际视频时长: 5.22秒
2025-08-07 17:41:45,749 - INFO - 时长差异: 0.05秒 (1.03%)
2025-08-07 17:41:45,749 - INFO - ==========================================
2025-08-07 17:41:45,749 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:45,749 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\65_3.mp4
2025-08-07 17:41:45,751 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpybf1qgsf
2025-08-07 17:41:45,796 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:45,796 - INFO -   - 音频时长: 5.17秒
2025-08-07 17:41:45,796 - INFO -   - 视频时长: 5.22秒
2025-08-07 17:41:45,796 - INFO -   - 时长差异: 0.05秒 (1.03%)
2025-08-07 17:41:45,796 - INFO - 
字幕 #65 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:45,796 - INFO - 生成的视频文件:
2025-08-07 17:41:45,796 - INFO -   1. F:/github/aicut_auto/newcut_ai\65_1.mp4
2025-08-07 17:41:45,796 - INFO -   2. F:/github/aicut_auto/newcut_ai\65_2.mp4
2025-08-07 17:41:45,796 - INFO -   3. F:/github/aicut_auto/newcut_ai\65_3.mp4
2025-08-07 17:41:45,796 - INFO - ========== 字幕 #65 处理结束 ==========

