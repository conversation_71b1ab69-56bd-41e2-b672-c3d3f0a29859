2025-08-07 17:40:52,393 - INFO - ========== 字幕 #27 处理开始 ==========
2025-08-07 17:40:52,393 - INFO - 字幕内容: 面对女孩的质疑，二弟死不承认，女孩便让爷爷当场给股东打电话确认。
2025-08-07 17:40:52,393 - INFO - 字幕序号: [770, 772]
2025-08-07 17:40:52,393 - INFO - 音频文件详情:
2025-08-07 17:40:52,393 - INFO -   - 路径: output\27.wav
2025-08-07 17:40:52,393 - INFO -   - 时长: 4.24秒
2025-08-07 17:40:52,393 - INFO -   - 验证音频时长: 4.24秒
2025-08-07 17:40:52,394 - INFO - 字幕时间戳信息:
2025-08-07 17:40:52,403 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:52,403 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:52,403 - INFO -   - 根据生成的音频时长(4.24秒)已调整字幕时间戳
2025-08-07 17:40:52,403 - INFO - ========== 新模式：为字幕 #27 生成4套场景方案 ==========
2025-08-07 17:40:52,403 - INFO - 字幕序号列表: [770, 772]
2025-08-07 17:40:52,403 - INFO - 
--- 生成方案 #1：基于字幕序号 #770 ---
2025-08-07 17:40:52,403 - INFO - 开始为单个字幕序号 #770 匹配场景，目标时长: 4.24秒
2025-08-07 17:40:52,403 - INFO - 开始查找字幕序号 [770] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:52,403 - INFO - 找到related_overlap场景: scene_id=938, 字幕#770
2025-08-07 17:40:52,404 - INFO - 字幕 #770 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:52,404 - INFO - 字幕序号 #770 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:52,404 - INFO - 选择第一个overlap场景作为起点: scene_id=938
2025-08-07 17:40:52,404 - INFO - 添加起点场景: scene_id=938, 时长=1.40秒, 累计时长=1.40秒
2025-08-07 17:40:52,404 - INFO - 起点场景时长不足，需要延伸填充 2.84秒
2025-08-07 17:40:52,404 - INFO - 起点场景在原始列表中的索引: 937
2025-08-07 17:40:52,404 - INFO - 延伸添加场景: scene_id=939 (完整时长 1.68秒)
2025-08-07 17:40:52,404 - INFO - 累计时长: 3.08秒
2025-08-07 17:40:52,404 - INFO - 延伸添加场景: scene_id=940 (完整时长 1.12秒)
2025-08-07 17:40:52,404 - INFO - 累计时长: 4.20秒
2025-08-07 17:40:52,404 - INFO - 延伸添加场景: scene_id=941 (裁剪至 0.04秒)
2025-08-07 17:40:52,404 - INFO - 累计时长: 4.24秒
2025-08-07 17:40:52,404 - INFO - 字幕序号 #770 场景匹配完成，共选择 4 个场景，总时长: 4.24秒
2025-08-07 17:40:52,404 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-08-07 17:40:52,404 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-08-07 17:40:52,404 - INFO - 
--- 生成方案 #2：基于字幕序号 #772 ---
2025-08-07 17:40:52,404 - INFO - 开始为单个字幕序号 #772 匹配场景，目标时长: 4.24秒
2025-08-07 17:40:52,404 - INFO - 开始查找字幕序号 [772] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:52,406 - INFO - 找到related_overlap场景: scene_id=939, 字幕#772
2025-08-07 17:40:52,406 - INFO - 找到related_overlap场景: scene_id=940, 字幕#772
2025-08-07 17:40:52,406 - INFO - 字幕 #772 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:40:52,406 - INFO - 字幕序号 #772 找到 0 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:52,406 - ERROR - 字幕序号 #772 没有找到任何可用的匹配场景
2025-08-07 17:40:52,406 - WARNING - 方案 #2 生成失败，未找到合适的场景
2025-08-07 17:40:52,406 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-08-07 17:40:52,406 - INFO - ========== 当前模式：为字幕 #27 生成 1 套场景方案 ==========
2025-08-07 17:40:52,406 - INFO - 开始查找字幕序号 [770, 772] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:52,407 - INFO - 找到related_overlap场景: scene_id=938, 字幕#770
2025-08-07 17:40:52,407 - INFO - 找到related_overlap场景: scene_id=939, 字幕#772
2025-08-07 17:40:52,407 - INFO - 找到related_overlap场景: scene_id=940, 字幕#772
2025-08-07 17:40:52,407 - INFO - 字幕 #770 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:52,407 - INFO - 字幕 #772 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:40:52,407 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-08-07 17:40:52,408 - INFO - 开始生成方案 #1
2025-08-07 17:40:52,408 - INFO - 方案 #1: 为字幕#770选择初始化overlap场景id=938
2025-08-07 17:40:52,408 - INFO - 方案 #1: 为字幕#772选择初始化overlap场景id=939
2025-08-07 17:40:52,408 - INFO - 方案 #1: 初始选择后，当前总时长=3.08秒
2025-08-07 17:40:52,408 - INFO - 方案 #1: 额外添加overlap场景id=940, 当前总时长=4.20秒
2025-08-07 17:40:52,408 - INFO - 方案 #1: 额外between选择后，当前总时长=4.20秒
2025-08-07 17:40:52,408 - INFO - 方案 #1: 场景总时长(4.20秒)小于音频时长(4.24秒)，需要延伸填充
2025-08-07 17:40:52,408 - INFO - 方案 #1: 最后一个场景ID: 940
2025-08-07 17:40:52,408 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 939
2025-08-07 17:40:52,408 - INFO - 方案 #1: 需要填充时长: 0.04秒
2025-08-07 17:40:52,408 - INFO - 方案 #1: 追加场景 scene_id=941 (裁剪至 0.04秒)
2025-08-07 17:40:52,408 - INFO - 方案 #1: 成功填充至目标时长
2025-08-07 17:40:52,408 - INFO - 方案 #1 调整/填充后最终总时长: 4.24秒
2025-08-07 17:40:52,408 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:52,408 - INFO - ========== 当前模式：字幕 #27 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:52,408 - INFO - 方案 #2 (传统模式) 生成成功
2025-08-07 17:40:52,408 - INFO - ========== 新模式：字幕 #27 共生成 2 套有效场景方案 ==========
2025-08-07 17:40:52,408 - INFO - 
----- 处理字幕 #27 的方案 #1 -----
2025-08-07 17:40:52,408 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\27_1.mp4
2025-08-07 17:40:52,408 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp2sg47gbn
2025-08-07 17:40:52,409 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\938.mp4 (确认存在: True)
2025-08-07 17:40:52,409 - INFO - 添加场景ID=938，时长=1.40秒，累计时长=1.40秒
2025-08-07 17:40:52,409 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\939.mp4 (确认存在: True)
2025-08-07 17:40:52,409 - INFO - 添加场景ID=939，时长=1.68秒，累计时长=3.08秒
2025-08-07 17:40:52,409 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\940.mp4 (确认存在: True)
2025-08-07 17:40:52,409 - INFO - 添加场景ID=940，时长=1.12秒，累计时长=4.20秒
2025-08-07 17:40:52,409 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\941.mp4 (确认存在: True)
2025-08-07 17:40:52,409 - INFO - 添加场景ID=941，时长=1.36秒，累计时长=5.56秒
2025-08-07 17:40:52,410 - INFO - 准备合并 4 个场景文件，总时长约 5.56秒
2025-08-07 17:40:52,410 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/938.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/939.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/940.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/941.mp4'

2025-08-07 17:40:52,410 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp2sg47gbn\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp2sg47gbn\temp_combined.mp4
2025-08-07 17:40:52,589 - INFO - 合并后的视频时长: 5.65秒，目标音频时长: 4.24秒
2025-08-07 17:40:52,589 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp2sg47gbn\temp_combined.mp4 -ss 0 -to 4.238 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\27_1.mp4
2025-08-07 17:40:52,879 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:52,879 - INFO - 目标音频时长: 4.24秒
2025-08-07 17:40:52,880 - INFO - 实际视频时长: 4.26秒
2025-08-07 17:40:52,880 - INFO - 时长差异: 0.02秒 (0.59%)
2025-08-07 17:40:52,880 - INFO - ==========================================
2025-08-07 17:40:52,880 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:52,880 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\27_1.mp4
2025-08-07 17:40:52,880 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp2sg47gbn
2025-08-07 17:40:52,923 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:52,923 - INFO -   - 音频时长: 4.24秒
2025-08-07 17:40:52,923 - INFO -   - 视频时长: 4.26秒
2025-08-07 17:40:52,923 - INFO -   - 时长差异: 0.02秒 (0.59%)
2025-08-07 17:40:52,923 - INFO - 
----- 处理字幕 #27 的方案 #2 -----
2025-08-07 17:40:52,923 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\27_2.mp4
2025-08-07 17:40:52,924 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp9fidnrkx
2025-08-07 17:40:52,924 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\938.mp4 (确认存在: True)
2025-08-07 17:40:52,924 - INFO - 添加场景ID=938，时长=1.40秒，累计时长=1.40秒
2025-08-07 17:40:52,925 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\939.mp4 (确认存在: True)
2025-08-07 17:40:52,925 - INFO - 添加场景ID=939，时长=1.68秒，累计时长=3.08秒
2025-08-07 17:40:52,925 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\940.mp4 (确认存在: True)
2025-08-07 17:40:52,925 - INFO - 添加场景ID=940，时长=1.12秒，累计时长=4.20秒
2025-08-07 17:40:52,925 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\941.mp4 (确认存在: True)
2025-08-07 17:40:52,925 - INFO - 添加场景ID=941，时长=1.36秒，累计时长=5.56秒
2025-08-07 17:40:52,925 - INFO - 准备合并 4 个场景文件，总时长约 5.56秒
2025-08-07 17:40:52,925 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/938.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/939.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/940.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/941.mp4'

2025-08-07 17:40:52,925 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp9fidnrkx\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp9fidnrkx\temp_combined.mp4
2025-08-07 17:40:53,083 - INFO - 合并后的视频时长: 5.65秒，目标音频时长: 4.24秒
2025-08-07 17:40:53,083 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp9fidnrkx\temp_combined.mp4 -ss 0 -to 4.238 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\27_2.mp4
2025-08-07 17:40:53,382 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:53,382 - INFO - 目标音频时长: 4.24秒
2025-08-07 17:40:53,382 - INFO - 实际视频时长: 4.26秒
2025-08-07 17:40:53,382 - INFO - 时长差异: 0.02秒 (0.59%)
2025-08-07 17:40:53,382 - INFO - ==========================================
2025-08-07 17:40:53,382 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:53,382 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\27_2.mp4
2025-08-07 17:40:53,383 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp9fidnrkx
2025-08-07 17:40:53,427 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:53,427 - INFO -   - 音频时长: 4.24秒
2025-08-07 17:40:53,428 - INFO -   - 视频时长: 4.26秒
2025-08-07 17:40:53,428 - INFO -   - 时长差异: 0.02秒 (0.59%)
2025-08-07 17:40:53,428 - INFO - 
字幕 #27 处理完成，成功生成 2/2 套方案
2025-08-07 17:40:53,428 - INFO - 生成的视频文件:
2025-08-07 17:40:53,428 - INFO -   1. F:/github/aicut_auto/newcut_ai\27_1.mp4
2025-08-07 17:40:53,428 - INFO -   2. F:/github/aicut_auto/newcut_ai\27_2.mp4
2025-08-07 17:40:53,428 - INFO - ========== 字幕 #27 处理结束 ==========

