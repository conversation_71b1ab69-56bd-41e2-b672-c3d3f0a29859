2025-08-07 17:41:40,382 - INFO - ========== 字幕 #62 处理开始 ==========
2025-08-07 17:41:40,382 - INFO - 字幕内容: 幕后黑手派出了幽冥第一杀手贪狼，势要将所有人灭口。
2025-08-07 17:41:40,382 - INFO - 字幕序号: [2934, 2938]
2025-08-07 17:41:40,382 - INFO - 音频文件详情:
2025-08-07 17:41:40,382 - INFO -   - 路径: output\62.wav
2025-08-07 17:41:40,382 - INFO -   - 时长: 3.79秒
2025-08-07 17:41:40,382 - INFO -   - 验证音频时长: 3.79秒
2025-08-07 17:41:40,382 - INFO - 字幕时间戳信息:
2025-08-07 17:41:40,382 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:40,382 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:40,382 - INFO -   - 根据生成的音频时长(3.79秒)已调整字幕时间戳
2025-08-07 17:41:40,382 - INFO - ========== 新模式：为字幕 #62 生成4套场景方案 ==========
2025-08-07 17:41:40,382 - INFO - 字幕序号列表: [2934, 2938]
2025-08-07 17:41:40,382 - INFO - 
--- 生成方案 #1：基于字幕序号 #2934 ---
2025-08-07 17:41:40,382 - INFO - 开始为单个字幕序号 #2934 匹配场景，目标时长: 3.79秒
2025-08-07 17:41:40,382 - INFO - 开始查找字幕序号 [2934] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:40,383 - INFO - 找到related_overlap场景: scene_id=3180, 字幕#2934
2025-08-07 17:41:40,383 - INFO - 找到related_overlap场景: scene_id=3182, 字幕#2934
2025-08-07 17:41:40,384 - INFO - 找到related_between场景: scene_id=3179, 字幕#2934
2025-08-07 17:41:40,384 - INFO - 字幕 #2934 找到 2 个overlap场景, 1 个between场景
2025-08-07 17:41:40,384 - INFO - 字幕序号 #2934 找到 2 个可用overlap场景, 1 个可用between场景
2025-08-07 17:41:40,384 - INFO - 选择第一个overlap场景作为起点: scene_id=3180
2025-08-07 17:41:40,384 - INFO - 添加起点场景: scene_id=3180, 时长=1.40秒, 累计时长=1.40秒
2025-08-07 17:41:40,384 - INFO - 起点场景时长不足，需要延伸填充 2.39秒
2025-08-07 17:41:40,384 - INFO - 起点场景在原始列表中的索引: 3179
2025-08-07 17:41:40,384 - INFO - 延伸添加场景: scene_id=3181 (完整时长 0.96秒)
2025-08-07 17:41:40,384 - INFO - 累计时长: 2.36秒
2025-08-07 17:41:40,384 - INFO - 延伸添加场景: scene_id=3182 (完整时长 0.80秒)
2025-08-07 17:41:40,384 - INFO - 累计时长: 3.16秒
2025-08-07 17:41:40,384 - INFO - 延伸添加场景: scene_id=3183 (裁剪至 0.63秒)
2025-08-07 17:41:40,384 - INFO - 累计时长: 3.79秒
2025-08-07 17:41:40,384 - INFO - 字幕序号 #2934 场景匹配完成，共选择 4 个场景，总时长: 3.79秒
2025-08-07 17:41:40,384 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-08-07 17:41:40,384 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-08-07 17:41:40,384 - INFO - 
--- 生成方案 #2：基于字幕序号 #2938 ---
2025-08-07 17:41:40,384 - INFO - 开始为单个字幕序号 #2938 匹配场景，目标时长: 3.79秒
2025-08-07 17:41:40,384 - INFO - 开始查找字幕序号 [2938] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:40,385 - INFO - 找到related_overlap场景: scene_id=3184, 字幕#2938
2025-08-07 17:41:40,385 - INFO - 找到related_overlap场景: scene_id=3186, 字幕#2938
2025-08-07 17:41:40,385 - INFO - 字幕 #2938 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:40,385 - INFO - 字幕序号 #2938 找到 2 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:40,385 - INFO - 选择第一个overlap场景作为起点: scene_id=3184
2025-08-07 17:41:40,385 - INFO - 添加起点场景: scene_id=3184, 时长=1.00秒, 累计时长=1.00秒
2025-08-07 17:41:40,385 - INFO - 起点场景时长不足，需要延伸填充 2.79秒
2025-08-07 17:41:40,385 - INFO - 起点场景在原始列表中的索引: 3183
2025-08-07 17:41:40,385 - INFO - 延伸添加场景: scene_id=3185 (完整时长 0.80秒)
2025-08-07 17:41:40,385 - INFO - 累计时长: 1.80秒
2025-08-07 17:41:40,385 - INFO - 延伸添加场景: scene_id=3186 (裁剪至 1.99秒)
2025-08-07 17:41:40,385 - INFO - 累计时长: 3.79秒
2025-08-07 17:41:40,385 - INFO - 字幕序号 #2938 场景匹配完成，共选择 3 个场景，总时长: 3.79秒
2025-08-07 17:41:40,385 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-08-07 17:41:40,385 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:40,385 - INFO - ========== 当前模式：为字幕 #62 生成 1 套场景方案 ==========
2025-08-07 17:41:40,385 - INFO - 开始查找字幕序号 [2934, 2938] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:40,386 - INFO - 找到related_overlap场景: scene_id=3180, 字幕#2934
2025-08-07 17:41:40,386 - INFO - 找到related_overlap场景: scene_id=3182, 字幕#2934
2025-08-07 17:41:40,386 - INFO - 找到related_overlap场景: scene_id=3184, 字幕#2938
2025-08-07 17:41:40,386 - INFO - 找到related_overlap场景: scene_id=3186, 字幕#2938
2025-08-07 17:41:40,386 - INFO - 找到related_between场景: scene_id=3179, 字幕#2934
2025-08-07 17:41:40,386 - INFO - 字幕 #2934 找到 2 个overlap场景, 1 个between场景
2025-08-07 17:41:40,386 - INFO - 字幕 #2938 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:40,386 - INFO - 共收集 4 个未使用的overlap场景和 1 个未使用的between场景
2025-08-07 17:41:40,386 - INFO - 开始生成方案 #1
2025-08-07 17:41:40,386 - INFO - 方案 #1: 为字幕#2934选择初始化overlap场景id=3182
2025-08-07 17:41:40,386 - INFO - 方案 #1: 为字幕#2938选择初始化overlap场景id=3184
2025-08-07 17:41:40,386 - INFO - 方案 #1: 初始选择后，当前总时长=1.80秒
2025-08-07 17:41:40,387 - INFO - 方案 #1: 额外添加overlap场景id=3180, 当前总时长=3.20秒
2025-08-07 17:41:40,387 - INFO - 方案 #1: 额外添加overlap场景id=3186, 当前总时长=5.52秒
2025-08-07 17:41:40,387 - INFO - 方案 #1: 额外between选择后，当前总时长=5.52秒
2025-08-07 17:41:40,387 - INFO - 方案 #1: 场景总时长(5.52秒)大于音频时长(3.79秒)，需要裁剪
2025-08-07 17:41:40,387 - INFO - 调整前总时长: 5.52秒, 目标时长: 3.79秒
2025-08-07 17:41:40,387 - INFO - 需要裁剪 1.73秒
2025-08-07 17:41:40,387 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-08-07 17:41:40,387 - INFO - 裁剪场景ID=3186：从2.32秒裁剪至1.00秒
2025-08-07 17:41:40,387 - INFO - 裁剪场景ID=3180：从1.40秒裁剪至1.00秒
2025-08-07 17:41:40,387 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 0.01秒
2025-08-07 17:41:40,387 - INFO - 移除场景ID=3182，时长=0.80秒
2025-08-07 17:41:40,387 - INFO - 调整后总时长: 3.00秒，与目标时长差异: 0.79秒
2025-08-07 17:41:40,387 - INFO - 方案 #1 调整/填充后最终总时长: 3.00秒
2025-08-07 17:41:40,387 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:40,387 - INFO - ========== 当前模式：字幕 #62 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:40,387 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:40,387 - INFO - ========== 新模式：字幕 #62 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:40,387 - INFO - 
----- 处理字幕 #62 的方案 #1 -----
2025-08-07 17:41:40,387 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\62_1.mp4
2025-08-07 17:41:40,387 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpz09ox2h4
2025-08-07 17:41:40,388 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3180.mp4 (确认存在: True)
2025-08-07 17:41:40,388 - INFO - 添加场景ID=3180，时长=1.40秒，累计时长=1.40秒
2025-08-07 17:41:40,388 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3181.mp4 (确认存在: True)
2025-08-07 17:41:40,388 - INFO - 添加场景ID=3181，时长=0.96秒，累计时长=2.36秒
2025-08-07 17:41:40,388 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3182.mp4 (确认存在: True)
2025-08-07 17:41:40,388 - INFO - 添加场景ID=3182，时长=0.80秒，累计时长=3.16秒
2025-08-07 17:41:40,388 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3183.mp4 (确认存在: True)
2025-08-07 17:41:40,388 - INFO - 添加场景ID=3183，时长=2.04秒，累计时长=5.20秒
2025-08-07 17:41:40,388 - INFO - 准备合并 4 个场景文件，总时长约 5.20秒
2025-08-07 17:41:40,388 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3180.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3181.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3182.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3183.mp4'

2025-08-07 17:41:40,388 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpz09ox2h4\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpz09ox2h4\temp_combined.mp4
2025-08-07 17:41:40,549 - INFO - 合并后的视频时长: 5.29秒，目标音频时长: 3.79秒
2025-08-07 17:41:40,549 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpz09ox2h4\temp_combined.mp4 -ss 0 -to 3.789 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\62_1.mp4
2025-08-07 17:41:40,832 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:40,832 - INFO - 目标音频时长: 3.79秒
2025-08-07 17:41:40,832 - INFO - 实际视频时长: 3.82秒
2025-08-07 17:41:40,832 - INFO - 时长差异: 0.03秒 (0.90%)
2025-08-07 17:41:40,832 - INFO - ==========================================
2025-08-07 17:41:40,832 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:40,832 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\62_1.mp4
2025-08-07 17:41:40,832 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpz09ox2h4
2025-08-07 17:41:40,876 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:40,876 - INFO -   - 音频时长: 3.79秒
2025-08-07 17:41:40,876 - INFO -   - 视频时长: 3.82秒
2025-08-07 17:41:40,876 - INFO -   - 时长差异: 0.03秒 (0.90%)
2025-08-07 17:41:40,876 - INFO - 
----- 处理字幕 #62 的方案 #2 -----
2025-08-07 17:41:40,876 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\62_2.mp4
2025-08-07 17:41:40,877 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkq_2ebwz
2025-08-07 17:41:40,877 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3184.mp4 (确认存在: True)
2025-08-07 17:41:40,877 - INFO - 添加场景ID=3184，时长=1.00秒，累计时长=1.00秒
2025-08-07 17:41:40,877 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3185.mp4 (确认存在: True)
2025-08-07 17:41:40,877 - INFO - 添加场景ID=3185，时长=0.80秒，累计时长=1.80秒
2025-08-07 17:41:40,878 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3186.mp4 (确认存在: True)
2025-08-07 17:41:40,878 - INFO - 添加场景ID=3186，时长=2.32秒，累计时长=4.12秒
2025-08-07 17:41:40,878 - INFO - 准备合并 3 个场景文件，总时长约 4.12秒
2025-08-07 17:41:40,878 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3184.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3185.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3186.mp4'

2025-08-07 17:41:40,878 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpkq_2ebwz\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpkq_2ebwz\temp_combined.mp4
2025-08-07 17:41:41,028 - INFO - 合并后的视频时长: 4.19秒，目标音频时长: 3.79秒
2025-08-07 17:41:41,028 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpkq_2ebwz\temp_combined.mp4 -ss 0 -to 3.789 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\62_2.mp4
2025-08-07 17:41:41,303 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:41,303 - INFO - 目标音频时长: 3.79秒
2025-08-07 17:41:41,303 - INFO - 实际视频时长: 3.82秒
2025-08-07 17:41:41,303 - INFO - 时长差异: 0.03秒 (0.90%)
2025-08-07 17:41:41,303 - INFO - ==========================================
2025-08-07 17:41:41,303 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:41,303 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\62_2.mp4
2025-08-07 17:41:41,305 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkq_2ebwz
2025-08-07 17:41:41,350 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:41,350 - INFO -   - 音频时长: 3.79秒
2025-08-07 17:41:41,350 - INFO -   - 视频时长: 3.82秒
2025-08-07 17:41:41,350 - INFO -   - 时长差异: 0.03秒 (0.90%)
2025-08-07 17:41:41,351 - INFO - 
----- 处理字幕 #62 的方案 #3 -----
2025-08-07 17:41:41,351 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\62_3.mp4
2025-08-07 17:41:41,351 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp3xw_fzcy
2025-08-07 17:41:41,351 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3184.mp4 (确认存在: True)
2025-08-07 17:41:41,351 - INFO - 添加场景ID=3184，时长=1.00秒，累计时长=1.00秒
2025-08-07 17:41:41,351 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3180.mp4 (确认存在: True)
2025-08-07 17:41:41,352 - INFO - 添加场景ID=3180，时长=1.40秒，累计时长=2.40秒
2025-08-07 17:41:41,352 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3186.mp4 (确认存在: True)
2025-08-07 17:41:41,352 - INFO - 添加场景ID=3186，时长=2.32秒，累计时长=4.72秒
2025-08-07 17:41:41,352 - INFO - 准备合并 3 个场景文件，总时长约 4.72秒
2025-08-07 17:41:41,352 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3184.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3180.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3186.mp4'

2025-08-07 17:41:41,352 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp3xw_fzcy\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp3xw_fzcy\temp_combined.mp4
2025-08-07 17:41:41,488 - INFO - 合并后的视频时长: 4.79秒，目标音频时长: 3.79秒
2025-08-07 17:41:41,488 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp3xw_fzcy\temp_combined.mp4 -ss 0 -to 3.789 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\62_3.mp4
2025-08-07 17:41:41,759 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:41,759 - INFO - 目标音频时长: 3.79秒
2025-08-07 17:41:41,759 - INFO - 实际视频时长: 3.82秒
2025-08-07 17:41:41,759 - INFO - 时长差异: 0.03秒 (0.90%)
2025-08-07 17:41:41,759 - INFO - ==========================================
2025-08-07 17:41:41,759 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:41,759 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\62_3.mp4
2025-08-07 17:41:41,760 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp3xw_fzcy
2025-08-07 17:41:41,802 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:41,802 - INFO -   - 音频时长: 3.79秒
2025-08-07 17:41:41,802 - INFO -   - 视频时长: 3.82秒
2025-08-07 17:41:41,802 - INFO -   - 时长差异: 0.03秒 (0.90%)
2025-08-07 17:41:41,803 - INFO - 
字幕 #62 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:41,803 - INFO - 生成的视频文件:
2025-08-07 17:41:41,803 - INFO -   1. F:/github/aicut_auto/newcut_ai\62_1.mp4
2025-08-07 17:41:41,803 - INFO -   2. F:/github/aicut_auto/newcut_ai\62_2.mp4
2025-08-07 17:41:41,803 - INFO -   3. F:/github/aicut_auto/newcut_ai\62_3.mp4
2025-08-07 17:41:41,803 - INFO - ========== 字幕 #62 处理结束 ==========

