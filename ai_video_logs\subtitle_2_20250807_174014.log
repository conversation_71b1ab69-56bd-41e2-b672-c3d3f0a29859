2025-08-07 17:40:14,671 - INFO - ========== 字幕 #2 处理开始 ==========
2025-08-07 17:40:14,671 - INFO - 字幕内容: 为揪出幕后黑手，了结因果，她必须下山，临行前，师傅给了她一个能无限续杯灵芝水的奶瓶，作为补充灵力的法宝。
2025-08-07 17:40:14,671 - INFO - 字幕序号: [10, 24]
2025-08-07 17:40:14,671 - INFO - 音频文件详情:
2025-08-07 17:40:14,671 - INFO -   - 路径: output\2.wav
2025-08-07 17:40:14,671 - INFO -   - 时长: 6.40秒
2025-08-07 17:40:14,672 - INFO -   - 验证音频时长: 6.40秒
2025-08-07 17:40:14,672 - INFO - 字幕时间戳信息:
2025-08-07 17:40:14,672 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:14,672 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:14,672 - INFO -   - 根据生成的音频时长(6.40秒)已调整字幕时间戳
2025-08-07 17:40:14,672 - INFO - ========== 新模式：为字幕 #2 生成4套场景方案 ==========
2025-08-07 17:40:14,672 - INFO - 字幕序号列表: [10, 24]
2025-08-07 17:40:14,672 - INFO - 
--- 生成方案 #1：基于字幕序号 #10 ---
2025-08-07 17:40:14,672 - INFO - 开始为单个字幕序号 #10 匹配场景，目标时长: 6.40秒
2025-08-07 17:40:14,672 - INFO - 开始查找字幕序号 [10] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:14,672 - INFO - 找到related_overlap场景: scene_id=32, 字幕#10
2025-08-07 17:40:14,673 - INFO - 字幕 #10 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:14,673 - INFO - 字幕序号 #10 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:14,673 - INFO - 选择第一个overlap场景作为起点: scene_id=32
2025-08-07 17:40:14,673 - INFO - 添加起点场景: scene_id=32, 时长=2.04秒, 累计时长=2.04秒
2025-08-07 17:40:14,673 - INFO - 起点场景时长不足，需要延伸填充 4.36秒
2025-08-07 17:40:14,673 - INFO - 起点场景在原始列表中的索引: 31
2025-08-07 17:40:14,673 - INFO - 延伸添加场景: scene_id=33 (完整时长 3.48秒)
2025-08-07 17:40:14,673 - INFO - 累计时长: 5.52秒
2025-08-07 17:40:14,673 - INFO - 延伸添加场景: scene_id=34 (裁剪至 0.88秒)
2025-08-07 17:40:14,673 - INFO - 累计时长: 6.40秒
2025-08-07 17:40:14,673 - INFO - 字幕序号 #10 场景匹配完成，共选择 3 个场景，总时长: 6.40秒
2025-08-07 17:40:14,673 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-08-07 17:40:14,673 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-08-07 17:40:14,673 - INFO - 
--- 生成方案 #2：基于字幕序号 #24 ---
2025-08-07 17:40:14,673 - INFO - 开始为单个字幕序号 #24 匹配场景，目标时长: 6.40秒
2025-08-07 17:40:14,673 - INFO - 开始查找字幕序号 [24] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:14,673 - INFO - 找到related_overlap场景: scene_id=50, 字幕#24
2025-08-07 17:40:14,674 - INFO - 字幕 #24 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:14,674 - INFO - 字幕序号 #24 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:14,674 - INFO - 选择第一个overlap场景作为起点: scene_id=50
2025-08-07 17:40:14,674 - INFO - 添加起点场景: scene_id=50, 时长=3.68秒, 累计时长=3.68秒
2025-08-07 17:40:14,674 - INFO - 起点场景时长不足，需要延伸填充 2.72秒
2025-08-07 17:40:14,674 - INFO - 起点场景在原始列表中的索引: 49
2025-08-07 17:40:14,674 - INFO - 延伸添加场景: scene_id=51 (完整时长 1.40秒)
2025-08-07 17:40:14,674 - INFO - 累计时长: 5.08秒
2025-08-07 17:40:14,674 - INFO - 延伸添加场景: scene_id=52 (裁剪至 1.32秒)
2025-08-07 17:40:14,674 - INFO - 累计时长: 6.40秒
2025-08-07 17:40:14,674 - INFO - 字幕序号 #24 场景匹配完成，共选择 3 个场景，总时长: 6.40秒
2025-08-07 17:40:14,674 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-08-07 17:40:14,674 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:14,674 - INFO - ========== 当前模式：为字幕 #2 生成 1 套场景方案 ==========
2025-08-07 17:40:14,674 - INFO - 开始查找字幕序号 [10, 24] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:14,674 - INFO - 找到related_overlap场景: scene_id=32, 字幕#10
2025-08-07 17:40:14,674 - INFO - 找到related_overlap场景: scene_id=50, 字幕#24
2025-08-07 17:40:14,675 - INFO - 字幕 #10 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:14,675 - INFO - 字幕 #24 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:14,675 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-08-07 17:40:14,676 - INFO - 开始生成方案 #1
2025-08-07 17:40:14,676 - INFO - 方案 #1: 为字幕#10选择初始化overlap场景id=32
2025-08-07 17:40:14,676 - INFO - 方案 #1: 为字幕#24选择初始化overlap场景id=50
2025-08-07 17:40:14,676 - INFO - 方案 #1: 初始选择后，当前总时长=5.72秒
2025-08-07 17:40:14,676 - INFO - 方案 #1: 额外between选择后，当前总时长=5.72秒
2025-08-07 17:40:14,676 - INFO - 方案 #1: 场景总时长(5.72秒)小于音频时长(6.40秒)，需要延伸填充
2025-08-07 17:40:14,676 - INFO - 方案 #1: 最后一个场景ID: 50
2025-08-07 17:40:14,676 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 49
2025-08-07 17:40:14,676 - INFO - 方案 #1: 需要填充时长: 0.68秒
2025-08-07 17:40:14,676 - INFO - 方案 #1: 追加场景 scene_id=51 (裁剪至 0.68秒)
2025-08-07 17:40:14,676 - INFO - 方案 #1: 成功填充至目标时长
2025-08-07 17:40:14,676 - INFO - 方案 #1 调整/填充后最终总时长: 6.40秒
2025-08-07 17:40:14,676 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:14,676 - INFO - ========== 当前模式：字幕 #2 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:14,676 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:14,676 - INFO - ========== 新模式：字幕 #2 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:14,676 - INFO - 
----- 处理字幕 #2 的方案 #1 -----
2025-08-07 17:40:14,676 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\2_1.mp4
2025-08-07 17:40:14,685 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp9ce2x7_6
2025-08-07 17:40:14,685 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\32.mp4 (确认存在: True)
2025-08-07 17:40:14,685 - INFO - 添加场景ID=32，时长=2.04秒，累计时长=2.04秒
2025-08-07 17:40:14,685 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\33.mp4 (确认存在: True)
2025-08-07 17:40:14,685 - INFO - 添加场景ID=33，时长=3.48秒，累计时长=5.52秒
2025-08-07 17:40:14,685 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\34.mp4 (确认存在: True)
2025-08-07 17:40:14,685 - INFO - 添加场景ID=34，时长=2.76秒，累计时长=8.28秒
2025-08-07 17:40:14,685 - INFO - 准备合并 3 个场景文件，总时长约 8.28秒
2025-08-07 17:40:14,685 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/32.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/33.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/34.mp4'

2025-08-07 17:40:14,685 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp9ce2x7_6\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp9ce2x7_6\temp_combined.mp4
2025-08-07 17:40:14,832 - INFO - 合并后的视频时长: 8.35秒，目标音频时长: 6.40秒
2025-08-07 17:40:14,832 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp9ce2x7_6\temp_combined.mp4 -ss 0 -to 6.396 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\2_1.mp4
2025-08-07 17:40:15,168 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:15,168 - INFO - 目标音频时长: 6.40秒
2025-08-07 17:40:15,168 - INFO - 实际视频时长: 6.42秒
2025-08-07 17:40:15,168 - INFO - 时长差异: 0.03秒 (0.42%)
2025-08-07 17:40:15,168 - INFO - ==========================================
2025-08-07 17:40:15,168 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:15,168 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\2_1.mp4
2025-08-07 17:40:15,169 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp9ce2x7_6
2025-08-07 17:40:15,213 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:15,213 - INFO -   - 音频时长: 6.40秒
2025-08-07 17:40:15,213 - INFO -   - 视频时长: 6.42秒
2025-08-07 17:40:15,213 - INFO -   - 时长差异: 0.03秒 (0.42%)
2025-08-07 17:40:15,213 - INFO - 
----- 处理字幕 #2 的方案 #2 -----
2025-08-07 17:40:15,213 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\2_2.mp4
2025-08-07 17:40:15,213 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmps_6hlqfg
2025-08-07 17:40:15,214 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\50.mp4 (确认存在: True)
2025-08-07 17:40:15,214 - INFO - 添加场景ID=50，时长=3.68秒，累计时长=3.68秒
2025-08-07 17:40:15,214 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\51.mp4 (确认存在: True)
2025-08-07 17:40:15,214 - INFO - 添加场景ID=51，时长=1.40秒，累计时长=5.08秒
2025-08-07 17:40:15,214 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\52.mp4 (确认存在: True)
2025-08-07 17:40:15,214 - INFO - 添加场景ID=52，时长=1.76秒，累计时长=6.84秒
2025-08-07 17:40:15,214 - INFO - 准备合并 3 个场景文件，总时长约 6.84秒
2025-08-07 17:40:15,214 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/50.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/51.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/52.mp4'

2025-08-07 17:40:15,214 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmps_6hlqfg\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmps_6hlqfg\temp_combined.mp4
2025-08-07 17:40:15,358 - INFO - 合并后的视频时长: 6.91秒，目标音频时长: 6.40秒
2025-08-07 17:40:15,358 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmps_6hlqfg\temp_combined.mp4 -ss 0 -to 6.396 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\2_2.mp4
2025-08-07 17:40:15,685 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:15,687 - INFO - 目标音频时长: 6.40秒
2025-08-07 17:40:15,687 - INFO - 实际视频时长: 6.42秒
2025-08-07 17:40:15,687 - INFO - 时长差异: 0.03秒 (0.42%)
2025-08-07 17:40:15,687 - INFO - ==========================================
2025-08-07 17:40:15,687 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:15,687 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\2_2.mp4
2025-08-07 17:40:15,687 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmps_6hlqfg
2025-08-07 17:40:15,732 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:15,732 - INFO -   - 音频时长: 6.40秒
2025-08-07 17:40:15,732 - INFO -   - 视频时长: 6.42秒
2025-08-07 17:40:15,732 - INFO -   - 时长差异: 0.03秒 (0.42%)
2025-08-07 17:40:15,732 - INFO - 
----- 处理字幕 #2 的方案 #3 -----
2025-08-07 17:40:15,732 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\2_3.mp4
2025-08-07 17:40:15,732 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpyq2bdcpo
2025-08-07 17:40:15,733 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\32.mp4 (确认存在: True)
2025-08-07 17:40:15,733 - INFO - 添加场景ID=32，时长=2.04秒，累计时长=2.04秒
2025-08-07 17:40:15,733 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\50.mp4 (确认存在: True)
2025-08-07 17:40:15,733 - INFO - 添加场景ID=50，时长=3.68秒，累计时长=5.72秒
2025-08-07 17:40:15,733 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\51.mp4 (确认存在: True)
2025-08-07 17:40:15,733 - INFO - 添加场景ID=51，时长=1.40秒，累计时长=7.12秒
2025-08-07 17:40:15,733 - INFO - 准备合并 3 个场景文件，总时长约 7.12秒
2025-08-07 17:40:15,733 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/32.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/50.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/51.mp4'

2025-08-07 17:40:15,733 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpyq2bdcpo\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpyq2bdcpo\temp_combined.mp4
2025-08-07 17:40:15,881 - INFO - 合并后的视频时长: 7.19秒，目标音频时长: 6.40秒
2025-08-07 17:40:15,881 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpyq2bdcpo\temp_combined.mp4 -ss 0 -to 6.396 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\2_3.mp4
2025-08-07 17:40:16,222 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:16,222 - INFO - 目标音频时长: 6.40秒
2025-08-07 17:40:16,222 - INFO - 实际视频时长: 6.42秒
2025-08-07 17:40:16,222 - INFO - 时长差异: 0.03秒 (0.42%)
2025-08-07 17:40:16,222 - INFO - ==========================================
2025-08-07 17:40:16,222 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:16,222 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\2_3.mp4
2025-08-07 17:40:16,223 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpyq2bdcpo
2025-08-07 17:40:16,268 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:16,268 - INFO -   - 音频时长: 6.40秒
2025-08-07 17:40:16,268 - INFO -   - 视频时长: 6.42秒
2025-08-07 17:40:16,268 - INFO -   - 时长差异: 0.03秒 (0.42%)
2025-08-07 17:40:16,268 - INFO - 
字幕 #2 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:16,268 - INFO - 生成的视频文件:
2025-08-07 17:40:16,268 - INFO -   1. F:/github/aicut_auto/newcut_ai\2_1.mp4
2025-08-07 17:40:16,268 - INFO -   2. F:/github/aicut_auto/newcut_ai\2_2.mp4
2025-08-07 17:40:16,268 - INFO -   3. F:/github/aicut_auto/newcut_ai\2_3.mp4
2025-08-07 17:40:16,268 - INFO - ========== 字幕 #2 处理结束 ==========

