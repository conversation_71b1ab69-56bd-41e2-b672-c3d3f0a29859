2025-08-07 17:40:20,877 - INFO - ========== 字幕 #6 处理开始 ==========
2025-08-07 17:40:20,877 - INFO - 字幕内容: 随后赶来的男人二弟和妹妹不问缘由，便指责大哥是残废还心术不正，让他百口莫辩。
2025-08-07 17:40:20,877 - INFO - 字幕序号: [69, 75]
2025-08-07 17:40:20,877 - INFO - 音频文件详情:
2025-08-07 17:40:20,877 - INFO -   - 路径: output\6.wav
2025-08-07 17:40:20,878 - INFO -   - 时长: 5.09秒
2025-08-07 17:40:20,878 - INFO -   - 验证音频时长: 5.09秒
2025-08-07 17:40:20,878 - INFO - 字幕时间戳信息:
2025-08-07 17:40:20,878 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:20,878 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:20,878 - INFO -   - 根据生成的音频时长(5.09秒)已调整字幕时间戳
2025-08-07 17:40:20,878 - INFO - ========== 新模式：为字幕 #6 生成4套场景方案 ==========
2025-08-07 17:40:20,878 - INFO - 字幕序号列表: [69, 75]
2025-08-07 17:40:20,878 - INFO - 
--- 生成方案 #1：基于字幕序号 #69 ---
2025-08-07 17:40:20,878 - INFO - 开始为单个字幕序号 #69 匹配场景，目标时长: 5.09秒
2025-08-07 17:40:20,878 - INFO - 开始查找字幕序号 [69] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:20,878 - INFO - 找到related_overlap场景: scene_id=115, 字幕#69
2025-08-07 17:40:20,878 - INFO - 找到related_overlap场景: scene_id=116, 字幕#69
2025-08-07 17:40:20,880 - INFO - 字幕 #69 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:40:20,880 - INFO - 字幕序号 #69 找到 2 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:20,880 - INFO - 选择第一个overlap场景作为起点: scene_id=115
2025-08-07 17:40:20,880 - INFO - 添加起点场景: scene_id=115, 时长=1.56秒, 累计时长=1.56秒
2025-08-07 17:40:20,880 - INFO - 起点场景时长不足，需要延伸填充 3.53秒
2025-08-07 17:40:20,880 - INFO - 起点场景在原始列表中的索引: 114
2025-08-07 17:40:20,880 - INFO - 延伸添加场景: scene_id=116 (完整时长 2.92秒)
2025-08-07 17:40:20,880 - INFO - 累计时长: 4.48秒
2025-08-07 17:40:20,880 - INFO - 延伸添加场景: scene_id=117 (裁剪至 0.61秒)
2025-08-07 17:40:20,880 - INFO - 累计时长: 5.09秒
2025-08-07 17:40:20,880 - INFO - 字幕序号 #69 场景匹配完成，共选择 3 个场景，总时长: 5.09秒
2025-08-07 17:40:20,880 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-08-07 17:40:20,880 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-08-07 17:40:20,880 - INFO - 
--- 生成方案 #2：基于字幕序号 #75 ---
2025-08-07 17:40:20,880 - INFO - 开始为单个字幕序号 #75 匹配场景，目标时长: 5.09秒
2025-08-07 17:40:20,880 - INFO - 开始查找字幕序号 [75] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:20,880 - INFO - 找到related_overlap场景: scene_id=119, 字幕#75
2025-08-07 17:40:20,880 - INFO - 找到related_overlap场景: scene_id=120, 字幕#75
2025-08-07 17:40:20,881 - INFO - 字幕 #75 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:40:20,881 - INFO - 字幕序号 #75 找到 2 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:20,881 - INFO - 选择第一个overlap场景作为起点: scene_id=119
2025-08-07 17:40:20,881 - INFO - 添加起点场景: scene_id=119, 时长=1.52秒, 累计时长=1.52秒
2025-08-07 17:40:20,881 - INFO - 起点场景时长不足，需要延伸填充 3.57秒
2025-08-07 17:40:20,881 - INFO - 起点场景在原始列表中的索引: 118
2025-08-07 17:40:20,881 - INFO - 延伸添加场景: scene_id=120 (完整时长 0.44秒)
2025-08-07 17:40:20,881 - INFO - 累计时长: 1.96秒
2025-08-07 17:40:20,881 - INFO - 延伸添加场景: scene_id=121 (完整时长 0.72秒)
2025-08-07 17:40:20,881 - INFO - 累计时长: 2.68秒
2025-08-07 17:40:20,881 - INFO - 延伸添加场景: scene_id=122 (完整时长 2.36秒)
2025-08-07 17:40:20,881 - INFO - 累计时长: 5.04秒
2025-08-07 17:40:20,881 - INFO - 延伸添加场景: scene_id=123 (裁剪至 0.05秒)
2025-08-07 17:40:20,881 - INFO - 累计时长: 5.09秒
2025-08-07 17:40:20,881 - INFO - 字幕序号 #75 场景匹配完成，共选择 5 个场景，总时长: 5.09秒
2025-08-07 17:40:20,881 - INFO - 方案 #2 生成成功，包含 5 个场景
2025-08-07 17:40:20,881 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:20,881 - INFO - ========== 当前模式：为字幕 #6 生成 1 套场景方案 ==========
2025-08-07 17:40:20,881 - INFO - 开始查找字幕序号 [69, 75] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:20,881 - INFO - 找到related_overlap场景: scene_id=115, 字幕#69
2025-08-07 17:40:20,881 - INFO - 找到related_overlap场景: scene_id=116, 字幕#69
2025-08-07 17:40:20,881 - INFO - 找到related_overlap场景: scene_id=119, 字幕#75
2025-08-07 17:40:20,881 - INFO - 找到related_overlap场景: scene_id=120, 字幕#75
2025-08-07 17:40:20,882 - INFO - 字幕 #69 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:40:20,882 - INFO - 字幕 #75 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:40:20,882 - INFO - 共收集 4 个未使用的overlap场景和 0 个未使用的between场景
2025-08-07 17:40:20,882 - INFO - 开始生成方案 #1
2025-08-07 17:40:20,882 - INFO - 方案 #1: 为字幕#69选择初始化overlap场景id=116
2025-08-07 17:40:20,882 - INFO - 方案 #1: 为字幕#75选择初始化overlap场景id=119
2025-08-07 17:40:20,882 - INFO - 方案 #1: 初始选择后，当前总时长=4.44秒
2025-08-07 17:40:20,882 - INFO - 方案 #1: 额外添加overlap场景id=120, 当前总时长=4.88秒
2025-08-07 17:40:20,882 - INFO - 方案 #1: 额外添加overlap场景id=115, 当前总时长=6.44秒
2025-08-07 17:40:20,882 - INFO - 方案 #1: 额外between选择后，当前总时长=6.44秒
2025-08-07 17:40:20,882 - INFO - 方案 #1: 场景总时长(6.44秒)大于音频时长(5.09秒)，需要裁剪
2025-08-07 17:40:20,883 - INFO - 调整前总时长: 6.44秒, 目标时长: 5.09秒
2025-08-07 17:40:20,883 - INFO - 需要裁剪 1.35秒
2025-08-07 17:40:20,883 - INFO - 裁剪最长场景ID=116：从2.92秒裁剪至1.57秒
2025-08-07 17:40:20,883 - INFO - 调整后总时长: 5.09秒，与目标时长差异: 0.00秒
2025-08-07 17:40:20,883 - INFO - 方案 #1 调整/填充后最终总时长: 5.09秒
2025-08-07 17:40:20,883 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:20,883 - INFO - ========== 当前模式：字幕 #6 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:20,883 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:20,883 - INFO - ========== 新模式：字幕 #6 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:20,883 - INFO - 
----- 处理字幕 #6 的方案 #1 -----
2025-08-07 17:40:20,883 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\6_1.mp4
2025-08-07 17:40:20,883 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbz3neh7q
2025-08-07 17:40:20,883 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\115.mp4 (确认存在: True)
2025-08-07 17:40:20,884 - INFO - 添加场景ID=115，时长=1.56秒，累计时长=1.56秒
2025-08-07 17:40:20,884 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\116.mp4 (确认存在: True)
2025-08-07 17:40:20,884 - INFO - 添加场景ID=116，时长=2.92秒，累计时长=4.48秒
2025-08-07 17:40:20,884 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\117.mp4 (确认存在: True)
2025-08-07 17:40:20,884 - INFO - 添加场景ID=117，时长=2.16秒，累计时长=6.64秒
2025-08-07 17:40:20,884 - INFO - 准备合并 3 个场景文件，总时长约 6.64秒
2025-08-07 17:40:20,884 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/115.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/116.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/117.mp4'

2025-08-07 17:40:20,884 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpbz3neh7q\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpbz3neh7q\temp_combined.mp4
2025-08-07 17:40:21,013 - INFO - 合并后的视频时长: 6.71秒，目标音频时长: 5.09秒
2025-08-07 17:40:21,013 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpbz3neh7q\temp_combined.mp4 -ss 0 -to 5.089 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\6_1.mp4
2025-08-07 17:40:21,316 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:21,316 - INFO - 目标音频时长: 5.09秒
2025-08-07 17:40:21,316 - INFO - 实际视频时长: 5.14秒
2025-08-07 17:40:21,316 - INFO - 时长差异: 0.05秒 (1.06%)
2025-08-07 17:40:21,316 - INFO - ==========================================
2025-08-07 17:40:21,316 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:21,316 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\6_1.mp4
2025-08-07 17:40:21,317 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbz3neh7q
2025-08-07 17:40:21,361 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:21,361 - INFO -   - 音频时长: 5.09秒
2025-08-07 17:40:21,361 - INFO -   - 视频时长: 5.14秒
2025-08-07 17:40:21,361 - INFO -   - 时长差异: 0.05秒 (1.06%)
2025-08-07 17:40:21,361 - INFO - 
----- 处理字幕 #6 的方案 #2 -----
2025-08-07 17:40:21,361 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\6_2.mp4
2025-08-07 17:40:21,361 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmps42h_1ly
2025-08-07 17:40:21,361 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\119.mp4 (确认存在: True)
2025-08-07 17:40:21,362 - INFO - 添加场景ID=119，时长=1.52秒，累计时长=1.52秒
2025-08-07 17:40:21,362 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\120.mp4 (确认存在: True)
2025-08-07 17:40:21,362 - INFO - 添加场景ID=120，时长=0.44秒，累计时长=1.96秒
2025-08-07 17:40:21,362 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\121.mp4 (确认存在: True)
2025-08-07 17:40:21,362 - INFO - 添加场景ID=121，时长=0.72秒，累计时长=2.68秒
2025-08-07 17:40:21,362 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\122.mp4 (确认存在: True)
2025-08-07 17:40:21,362 - INFO - 添加场景ID=122，时长=2.36秒，累计时长=5.04秒
2025-08-07 17:40:21,362 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\123.mp4 (确认存在: True)
2025-08-07 17:40:21,362 - INFO - 添加场景ID=123，时长=3.44秒，累计时长=8.48秒
2025-08-07 17:40:21,362 - INFO - 场景总时长(8.48秒)已达到音频时长(5.09秒)的1.5倍，停止添加场景
2025-08-07 17:40:21,362 - INFO - 准备合并 5 个场景文件，总时长约 8.48秒
2025-08-07 17:40:21,362 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/119.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/120.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/121.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/122.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/123.mp4'

2025-08-07 17:40:21,362 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmps42h_1ly\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmps42h_1ly\temp_combined.mp4
2025-08-07 17:40:21,523 - INFO - 合并后的视频时长: 8.60秒，目标音频时长: 5.09秒
2025-08-07 17:40:21,523 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmps42h_1ly\temp_combined.mp4 -ss 0 -to 5.089 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\6_2.mp4
2025-08-07 17:40:21,829 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:21,829 - INFO - 目标音频时长: 5.09秒
2025-08-07 17:40:21,829 - INFO - 实际视频时长: 5.14秒
2025-08-07 17:40:21,829 - INFO - 时长差异: 0.05秒 (1.06%)
2025-08-07 17:40:21,829 - INFO - ==========================================
2025-08-07 17:40:21,829 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:21,829 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\6_2.mp4
2025-08-07 17:40:21,830 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmps42h_1ly
2025-08-07 17:40:21,874 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:21,874 - INFO -   - 音频时长: 5.09秒
2025-08-07 17:40:21,874 - INFO -   - 视频时长: 5.14秒
2025-08-07 17:40:21,874 - INFO -   - 时长差异: 0.05秒 (1.06%)
2025-08-07 17:40:21,874 - INFO - 
----- 处理字幕 #6 的方案 #3 -----
2025-08-07 17:40:21,874 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\6_3.mp4
2025-08-07 17:40:21,874 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpfhpw0fkn
2025-08-07 17:40:21,875 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\116.mp4 (确认存在: True)
2025-08-07 17:40:21,875 - INFO - 添加场景ID=116，时长=2.92秒，累计时长=2.92秒
2025-08-07 17:40:21,875 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\119.mp4 (确认存在: True)
2025-08-07 17:40:21,875 - INFO - 添加场景ID=119，时长=1.52秒，累计时长=4.44秒
2025-08-07 17:40:21,875 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\120.mp4 (确认存在: True)
2025-08-07 17:40:21,875 - INFO - 添加场景ID=120，时长=0.44秒，累计时长=4.88秒
2025-08-07 17:40:21,875 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\115.mp4 (确认存在: True)
2025-08-07 17:40:21,875 - INFO - 添加场景ID=115，时长=1.56秒，累计时长=6.44秒
2025-08-07 17:40:21,875 - INFO - 准备合并 4 个场景文件，总时长约 6.44秒
2025-08-07 17:40:21,875 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/116.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/119.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/120.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/115.mp4'

2025-08-07 17:40:21,875 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpfhpw0fkn\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpfhpw0fkn\temp_combined.mp4
2025-08-07 17:40:22,038 - INFO - 合并后的视频时长: 6.53秒，目标音频时长: 5.09秒
2025-08-07 17:40:22,038 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpfhpw0fkn\temp_combined.mp4 -ss 0 -to 5.089 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\6_3.mp4
2025-08-07 17:40:22,326 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:22,326 - INFO - 目标音频时长: 5.09秒
2025-08-07 17:40:22,326 - INFO - 实际视频时长: 5.14秒
2025-08-07 17:40:22,326 - INFO - 时长差异: 0.05秒 (1.06%)
2025-08-07 17:40:22,326 - INFO - ==========================================
2025-08-07 17:40:22,326 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:22,326 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\6_3.mp4
2025-08-07 17:40:22,327 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpfhpw0fkn
2025-08-07 17:40:22,380 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:22,380 - INFO -   - 音频时长: 5.09秒
2025-08-07 17:40:22,380 - INFO -   - 视频时长: 5.14秒
2025-08-07 17:40:22,380 - INFO -   - 时长差异: 0.05秒 (1.06%)
2025-08-07 17:40:22,380 - INFO - 
字幕 #6 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:22,380 - INFO - 生成的视频文件:
2025-08-07 17:40:22,380 - INFO -   1. F:/github/aicut_auto/newcut_ai\6_1.mp4
2025-08-07 17:40:22,380 - INFO -   2. F:/github/aicut_auto/newcut_ai\6_2.mp4
2025-08-07 17:40:22,380 - INFO -   3. F:/github/aicut_auto/newcut_ai\6_3.mp4
2025-08-07 17:40:22,380 - INFO - ========== 字幕 #6 处理结束 ==========

