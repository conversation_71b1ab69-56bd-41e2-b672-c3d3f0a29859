2025-08-07 17:41:03,372 - INFO - ========== 字幕 #35 处理开始 ==========
2025-08-07 17:41:03,372 - INFO - 字幕内容: 办公室里，心机女和助理正在交易，男人和女孩却突然出现，原来女孩早已预见了一切。
2025-08-07 17:41:03,372 - INFO - 字幕序号: [1228, 1231]
2025-08-07 17:41:03,372 - INFO - 音频文件详情:
2025-08-07 17:41:03,372 - INFO -   - 路径: output\35.wav
2025-08-07 17:41:03,372 - INFO -   - 时长: 5.37秒
2025-08-07 17:41:03,373 - INFO -   - 验证音频时长: 5.37秒
2025-08-07 17:41:03,373 - INFO - 字幕时间戳信息:
2025-08-07 17:41:03,373 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:03,373 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:03,373 - INFO -   - 根据生成的音频时长(5.37秒)已调整字幕时间戳
2025-08-07 17:41:03,373 - INFO - ========== 新模式：为字幕 #35 生成4套场景方案 ==========
2025-08-07 17:41:03,373 - INFO - 字幕序号列表: [1228, 1231]
2025-08-07 17:41:03,373 - INFO - 
--- 生成方案 #1：基于字幕序号 #1228 ---
2025-08-07 17:41:03,373 - INFO - 开始为单个字幕序号 #1228 匹配场景，目标时长: 5.37秒
2025-08-07 17:41:03,373 - INFO - 开始查找字幕序号 [1228] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:03,373 - INFO - 找到related_overlap场景: scene_id=1421, 字幕#1228
2025-08-07 17:41:03,374 - INFO - 找到related_between场景: scene_id=1420, 字幕#1228
2025-08-07 17:41:03,375 - INFO - 字幕 #1228 找到 1 个overlap场景, 1 个between场景
2025-08-07 17:41:03,375 - INFO - 字幕序号 #1228 找到 1 个可用overlap场景, 1 个可用between场景
2025-08-07 17:41:03,375 - INFO - 选择第一个overlap场景作为起点: scene_id=1421
2025-08-07 17:41:03,375 - INFO - 添加起点场景: scene_id=1421, 时长=0.64秒, 累计时长=0.64秒
2025-08-07 17:41:03,375 - INFO - 起点场景时长不足，需要延伸填充 4.73秒
2025-08-07 17:41:03,375 - INFO - 起点场景在原始列表中的索引: 1420
2025-08-07 17:41:03,375 - INFO - 延伸添加场景: scene_id=1422 (完整时长 1.12秒)
2025-08-07 17:41:03,375 - INFO - 累计时长: 1.76秒
2025-08-07 17:41:03,375 - INFO - 延伸添加场景: scene_id=1423 (完整时长 1.76秒)
2025-08-07 17:41:03,375 - INFO - 累计时长: 3.52秒
2025-08-07 17:41:03,375 - INFO - 延伸添加场景: scene_id=1424 (完整时长 1.20秒)
2025-08-07 17:41:03,375 - INFO - 累计时长: 4.72秒
2025-08-07 17:41:03,375 - INFO - 延伸添加场景: scene_id=1425 (裁剪至 0.66秒)
2025-08-07 17:41:03,375 - INFO - 累计时长: 5.37秒
2025-08-07 17:41:03,375 - INFO - 字幕序号 #1228 场景匹配完成，共选择 5 个场景，总时长: 5.37秒
2025-08-07 17:41:03,375 - INFO - 方案 #1 生成成功，包含 5 个场景
2025-08-07 17:41:03,375 - INFO - 新模式：第1套方案的 5 个场景已加入全局已使用集合
2025-08-07 17:41:03,375 - INFO - 
--- 生成方案 #2：基于字幕序号 #1231 ---
2025-08-07 17:41:03,375 - INFO - 开始为单个字幕序号 #1231 匹配场景，目标时长: 5.37秒
2025-08-07 17:41:03,375 - INFO - 开始查找字幕序号 [1231] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:03,375 - INFO - 找到related_overlap场景: scene_id=1423, 字幕#1231
2025-08-07 17:41:03,375 - INFO - 找到related_overlap场景: scene_id=1424, 字幕#1231
2025-08-07 17:41:03,375 - INFO - 找到related_between场景: scene_id=1425, 字幕#1231
2025-08-07 17:41:03,376 - INFO - 字幕 #1231 找到 2 个overlap场景, 1 个between场景
2025-08-07 17:41:03,376 - INFO - 字幕序号 #1231 找到 0 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:03,376 - ERROR - 字幕序号 #1231 没有找到任何可用的匹配场景
2025-08-07 17:41:03,376 - WARNING - 方案 #2 生成失败，未找到合适的场景
2025-08-07 17:41:03,376 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-08-07 17:41:03,376 - INFO - ========== 当前模式：为字幕 #35 生成 1 套场景方案 ==========
2025-08-07 17:41:03,376 - INFO - 开始查找字幕序号 [1228, 1231] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:03,376 - INFO - 找到related_overlap场景: scene_id=1421, 字幕#1228
2025-08-07 17:41:03,376 - INFO - 找到related_overlap场景: scene_id=1423, 字幕#1231
2025-08-07 17:41:03,376 - INFO - 找到related_overlap场景: scene_id=1424, 字幕#1231
2025-08-07 17:41:03,376 - INFO - 找到related_between场景: scene_id=1420, 字幕#1228
2025-08-07 17:41:03,376 - INFO - 找到related_between场景: scene_id=1425, 字幕#1231
2025-08-07 17:41:03,377 - INFO - 字幕 #1228 找到 1 个overlap场景, 1 个between场景
2025-08-07 17:41:03,377 - INFO - 字幕 #1231 找到 2 个overlap场景, 1 个between场景
2025-08-07 17:41:03,377 - INFO - 共收集 3 个未使用的overlap场景和 2 个未使用的between场景
2025-08-07 17:41:03,377 - INFO - 开始生成方案 #1
2025-08-07 17:41:03,377 - INFO - 方案 #1: 为字幕#1228选择初始化overlap场景id=1421
2025-08-07 17:41:03,377 - INFO - 方案 #1: 为字幕#1231选择初始化overlap场景id=1423
2025-08-07 17:41:03,377 - INFO - 方案 #1: 初始选择后，当前总时长=2.40秒
2025-08-07 17:41:03,377 - INFO - 方案 #1: 额外添加overlap场景id=1424, 当前总时长=3.60秒
2025-08-07 17:41:03,377 - INFO - 方案 #1: 额外between选择后，当前总时长=3.60秒
2025-08-07 17:41:03,377 - INFO - 方案 #1: 额外添加between场景id=1425, 当前总时长=4.52秒
2025-08-07 17:41:03,377 - INFO - 方案 #1: 额外添加between场景id=1420, 当前总时长=5.72秒
2025-08-07 17:41:03,377 - INFO - 方案 #1: 场景总时长(5.72秒)大于音频时长(5.37秒)，需要裁剪
2025-08-07 17:41:03,377 - INFO - 调整前总时长: 5.72秒, 目标时长: 5.37秒
2025-08-07 17:41:03,377 - INFO - 需要裁剪 0.34秒
2025-08-07 17:41:03,377 - INFO - 裁剪最长场景ID=1423：从1.76秒裁剪至1.42秒
2025-08-07 17:41:03,377 - INFO - 调整后总时长: 5.37秒，与目标时长差异: 0.00秒
2025-08-07 17:41:03,377 - INFO - 方案 #1 调整/填充后最终总时长: 5.37秒
2025-08-07 17:41:03,377 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:03,377 - INFO - ========== 当前模式：字幕 #35 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:03,377 - INFO - 方案 #2 (传统模式) 生成成功
2025-08-07 17:41:03,377 - INFO - ========== 新模式：字幕 #35 共生成 2 套有效场景方案 ==========
2025-08-07 17:41:03,377 - INFO - 
----- 处理字幕 #35 的方案 #1 -----
2025-08-07 17:41:03,377 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\35_1.mp4
2025-08-07 17:41:03,377 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp43i7cqyf
2025-08-07 17:41:03,378 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1421.mp4 (确认存在: True)
2025-08-07 17:41:03,378 - INFO - 添加场景ID=1421，时长=0.64秒，累计时长=0.64秒
2025-08-07 17:41:03,378 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1422.mp4 (确认存在: True)
2025-08-07 17:41:03,378 - INFO - 添加场景ID=1422，时长=1.12秒，累计时长=1.76秒
2025-08-07 17:41:03,378 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1423.mp4 (确认存在: True)
2025-08-07 17:41:03,378 - INFO - 添加场景ID=1423，时长=1.76秒，累计时长=3.52秒
2025-08-07 17:41:03,378 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1424.mp4 (确认存在: True)
2025-08-07 17:41:03,378 - INFO - 添加场景ID=1424，时长=1.20秒，累计时长=4.72秒
2025-08-07 17:41:03,378 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1425.mp4 (确认存在: True)
2025-08-07 17:41:03,378 - INFO - 添加场景ID=1425，时长=0.92秒，累计时长=5.64秒
2025-08-07 17:41:03,378 - INFO - 准备合并 5 个场景文件，总时长约 5.64秒
2025-08-07 17:41:03,378 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1421.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1422.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1423.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1424.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1425.mp4'

2025-08-07 17:41:03,378 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp43i7cqyf\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp43i7cqyf\temp_combined.mp4
2025-08-07 17:41:03,553 - INFO - 合并后的视频时长: 5.76秒，目标音频时长: 5.37秒
2025-08-07 17:41:03,553 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp43i7cqyf\temp_combined.mp4 -ss 0 -to 5.374 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\35_1.mp4
2025-08-07 17:41:03,881 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:03,881 - INFO - 目标音频时长: 5.37秒
2025-08-07 17:41:03,881 - INFO - 实际视频时长: 5.42秒
2025-08-07 17:41:03,881 - INFO - 时长差异: 0.05秒 (0.91%)
2025-08-07 17:41:03,881 - INFO - ==========================================
2025-08-07 17:41:03,881 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:03,881 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\35_1.mp4
2025-08-07 17:41:03,882 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp43i7cqyf
2025-08-07 17:41:03,927 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:03,927 - INFO -   - 音频时长: 5.37秒
2025-08-07 17:41:03,927 - INFO -   - 视频时长: 5.42秒
2025-08-07 17:41:03,927 - INFO -   - 时长差异: 0.05秒 (0.91%)
2025-08-07 17:41:03,928 - INFO - 
----- 处理字幕 #35 的方案 #2 -----
2025-08-07 17:41:03,928 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\35_2.mp4
2025-08-07 17:41:03,928 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpj22id6qq
2025-08-07 17:41:03,928 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1421.mp4 (确认存在: True)
2025-08-07 17:41:03,928 - INFO - 添加场景ID=1421，时长=0.64秒，累计时长=0.64秒
2025-08-07 17:41:03,929 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1423.mp4 (确认存在: True)
2025-08-07 17:41:03,929 - INFO - 添加场景ID=1423，时长=1.76秒，累计时长=2.40秒
2025-08-07 17:41:03,929 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1424.mp4 (确认存在: True)
2025-08-07 17:41:03,929 - INFO - 添加场景ID=1424，时长=1.20秒，累计时长=3.60秒
2025-08-07 17:41:03,929 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1425.mp4 (确认存在: True)
2025-08-07 17:41:03,929 - INFO - 添加场景ID=1425，时长=0.92秒，累计时长=4.52秒
2025-08-07 17:41:03,929 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1420.mp4 (确认存在: True)
2025-08-07 17:41:03,929 - INFO - 添加场景ID=1420，时长=1.20秒，累计时长=5.72秒
2025-08-07 17:41:03,929 - INFO - 准备合并 5 个场景文件，总时长约 5.72秒
2025-08-07 17:41:03,929 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1421.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1423.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1424.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1425.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1420.mp4'

2025-08-07 17:41:03,929 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpj22id6qq\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpj22id6qq\temp_combined.mp4
2025-08-07 17:41:04,086 - INFO - 合并后的视频时长: 5.84秒，目标音频时长: 5.37秒
2025-08-07 17:41:04,088 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpj22id6qq\temp_combined.mp4 -ss 0 -to 5.374 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\35_2.mp4
2025-08-07 17:41:04,421 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:04,421 - INFO - 目标音频时长: 5.37秒
2025-08-07 17:41:04,421 - INFO - 实际视频时长: 5.42秒
2025-08-07 17:41:04,421 - INFO - 时长差异: 0.05秒 (0.91%)
2025-08-07 17:41:04,421 - INFO - ==========================================
2025-08-07 17:41:04,421 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:04,421 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\35_2.mp4
2025-08-07 17:41:04,422 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpj22id6qq
2025-08-07 17:41:04,466 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:04,466 - INFO -   - 音频时长: 5.37秒
2025-08-07 17:41:04,466 - INFO -   - 视频时长: 5.42秒
2025-08-07 17:41:04,466 - INFO -   - 时长差异: 0.05秒 (0.91%)
2025-08-07 17:41:04,466 - INFO - 
字幕 #35 处理完成，成功生成 2/2 套方案
2025-08-07 17:41:04,466 - INFO - 生成的视频文件:
2025-08-07 17:41:04,466 - INFO -   1. F:/github/aicut_auto/newcut_ai\35_1.mp4
2025-08-07 17:41:04,466 - INFO -   2. F:/github/aicut_auto/newcut_ai\35_2.mp4
2025-08-07 17:41:04,466 - INFO - ========== 字幕 #35 处理结束 ==========

