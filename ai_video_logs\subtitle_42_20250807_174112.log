2025-08-07 17:41:12,915 - INFO - ========== 字幕 #42 处理开始 ==========
2025-08-07 17:41:12,915 - INFO - 字幕内容: 女孩再次看穿阴谋，她反制心机女，将毒茶灌进了她自己的嘴里，让她自食恶果。
2025-08-07 17:41:12,915 - INFO - 字幕序号: [1546, 1574]
2025-08-07 17:41:12,915 - INFO - 音频文件详情:
2025-08-07 17:41:12,915 - INFO -   - 路径: output\42.wav
2025-08-07 17:41:12,915 - INFO -   - 时长: 5.59秒
2025-08-07 17:41:12,916 - INFO -   - 验证音频时长: 5.59秒
2025-08-07 17:41:12,916 - INFO - 字幕时间戳信息:
2025-08-07 17:41:12,916 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:12,916 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:12,916 - INFO -   - 根据生成的音频时长(5.59秒)已调整字幕时间戳
2025-08-07 17:41:12,916 - INFO - ========== 新模式：为字幕 #42 生成4套场景方案 ==========
2025-08-07 17:41:12,916 - INFO - 字幕序号列表: [1546, 1574]
2025-08-07 17:41:12,916 - INFO - 
--- 生成方案 #1：基于字幕序号 #1546 ---
2025-08-07 17:41:12,916 - INFO - 开始为单个字幕序号 #1546 匹配场景，目标时长: 5.59秒
2025-08-07 17:41:12,916 - INFO - 开始查找字幕序号 [1546] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:12,916 - INFO - 找到related_overlap场景: scene_id=1766, 字幕#1546
2025-08-07 17:41:12,916 - INFO - 找到related_overlap场景: scene_id=1767, 字幕#1546
2025-08-07 17:41:12,917 - INFO - 字幕 #1546 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:12,917 - INFO - 字幕序号 #1546 找到 2 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:12,917 - INFO - 选择第一个overlap场景作为起点: scene_id=1766
2025-08-07 17:41:12,917 - INFO - 添加起点场景: scene_id=1766, 时长=3.00秒, 累计时长=3.00秒
2025-08-07 17:41:12,917 - INFO - 起点场景时长不足，需要延伸填充 2.59秒
2025-08-07 17:41:12,918 - INFO - 起点场景在原始列表中的索引: 1765
2025-08-07 17:41:12,918 - INFO - 延伸添加场景: scene_id=1767 (完整时长 1.44秒)
2025-08-07 17:41:12,918 - INFO - 累计时长: 4.44秒
2025-08-07 17:41:12,918 - INFO - 延伸添加场景: scene_id=1768 (裁剪至 1.15秒)
2025-08-07 17:41:12,918 - INFO - 累计时长: 5.59秒
2025-08-07 17:41:12,918 - INFO - 字幕序号 #1546 场景匹配完成，共选择 3 个场景，总时长: 5.59秒
2025-08-07 17:41:12,918 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-08-07 17:41:12,918 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-08-07 17:41:12,918 - INFO - 
--- 生成方案 #2：基于字幕序号 #1574 ---
2025-08-07 17:41:12,918 - INFO - 开始为单个字幕序号 #1574 匹配场景，目标时长: 5.59秒
2025-08-07 17:41:12,918 - INFO - 开始查找字幕序号 [1574] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:12,918 - INFO - 找到related_overlap场景: scene_id=1797, 字幕#1574
2025-08-07 17:41:12,919 - INFO - 字幕 #1574 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:12,919 - INFO - 字幕序号 #1574 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:12,919 - INFO - 选择第一个overlap场景作为起点: scene_id=1797
2025-08-07 17:41:12,919 - INFO - 添加起点场景: scene_id=1797, 时长=3.04秒, 累计时长=3.04秒
2025-08-07 17:41:12,919 - INFO - 起点场景时长不足，需要延伸填充 2.55秒
2025-08-07 17:41:12,919 - INFO - 起点场景在原始列表中的索引: 1796
2025-08-07 17:41:12,919 - INFO - 延伸添加场景: scene_id=1798 (裁剪至 2.55秒)
2025-08-07 17:41:12,919 - INFO - 累计时长: 5.59秒
2025-08-07 17:41:12,919 - INFO - 字幕序号 #1574 场景匹配完成，共选择 2 个场景，总时长: 5.59秒
2025-08-07 17:41:12,919 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-08-07 17:41:12,919 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:12,919 - INFO - ========== 当前模式：为字幕 #42 生成 1 套场景方案 ==========
2025-08-07 17:41:12,919 - INFO - 开始查找字幕序号 [1546, 1574] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:12,919 - INFO - 找到related_overlap场景: scene_id=1766, 字幕#1546
2025-08-07 17:41:12,919 - INFO - 找到related_overlap场景: scene_id=1767, 字幕#1546
2025-08-07 17:41:12,919 - INFO - 找到related_overlap场景: scene_id=1797, 字幕#1574
2025-08-07 17:41:12,920 - INFO - 字幕 #1546 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:12,920 - INFO - 字幕 #1574 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:12,920 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-08-07 17:41:12,920 - INFO - 开始生成方案 #1
2025-08-07 17:41:12,920 - INFO - 方案 #1: 为字幕#1546选择初始化overlap场景id=1766
2025-08-07 17:41:12,920 - INFO - 方案 #1: 为字幕#1574选择初始化overlap场景id=1797
2025-08-07 17:41:12,920 - INFO - 方案 #1: 初始选择后，当前总时长=6.04秒
2025-08-07 17:41:12,920 - INFO - 方案 #1: 额外between选择后，当前总时长=6.04秒
2025-08-07 17:41:12,920 - INFO - 方案 #1: 场景总时长(6.04秒)大于音频时长(5.59秒)，需要裁剪
2025-08-07 17:41:12,920 - INFO - 调整前总时长: 6.04秒, 目标时长: 5.59秒
2025-08-07 17:41:12,920 - INFO - 需要裁剪 0.45秒
2025-08-07 17:41:12,920 - INFO - 裁剪最长场景ID=1797：从3.04秒裁剪至2.59秒
2025-08-07 17:41:12,920 - INFO - 调整后总时长: 5.59秒，与目标时长差异: 0.00秒
2025-08-07 17:41:12,920 - INFO - 方案 #1 调整/填充后最终总时长: 5.59秒
2025-08-07 17:41:12,920 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:12,920 - INFO - ========== 当前模式：字幕 #42 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:12,920 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:12,920 - INFO - ========== 新模式：字幕 #42 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:12,920 - INFO - 
----- 处理字幕 #42 的方案 #1 -----
2025-08-07 17:41:12,921 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\42_1.mp4
2025-08-07 17:41:12,921 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8ygbiyc4
2025-08-07 17:41:12,921 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1766.mp4 (确认存在: True)
2025-08-07 17:41:12,921 - INFO - 添加场景ID=1766，时长=3.00秒，累计时长=3.00秒
2025-08-07 17:41:12,922 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1767.mp4 (确认存在: True)
2025-08-07 17:41:12,922 - INFO - 添加场景ID=1767，时长=1.44秒，累计时长=4.44秒
2025-08-07 17:41:12,922 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1768.mp4 (确认存在: True)
2025-08-07 17:41:12,922 - INFO - 添加场景ID=1768，时长=2.28秒，累计时长=6.72秒
2025-08-07 17:41:12,922 - INFO - 准备合并 3 个场景文件，总时长约 6.72秒
2025-08-07 17:41:12,922 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1766.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1767.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1768.mp4'

2025-08-07 17:41:12,922 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp8ygbiyc4\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp8ygbiyc4\temp_combined.mp4
2025-08-07 17:41:13,065 - INFO - 合并后的视频时长: 6.79秒，目标音频时长: 5.59秒
2025-08-07 17:41:13,065 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp8ygbiyc4\temp_combined.mp4 -ss 0 -to 5.586 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\42_1.mp4
2025-08-07 17:41:13,372 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:13,372 - INFO - 目标音频时长: 5.59秒
2025-08-07 17:41:13,372 - INFO - 实际视频时长: 5.62秒
2025-08-07 17:41:13,372 - INFO - 时长差异: 0.04秒 (0.66%)
2025-08-07 17:41:13,372 - INFO - ==========================================
2025-08-07 17:41:13,372 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:13,372 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\42_1.mp4
2025-08-07 17:41:13,373 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8ygbiyc4
2025-08-07 17:41:13,416 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:13,416 - INFO -   - 音频时长: 5.59秒
2025-08-07 17:41:13,416 - INFO -   - 视频时长: 5.62秒
2025-08-07 17:41:13,416 - INFO -   - 时长差异: 0.04秒 (0.66%)
2025-08-07 17:41:13,416 - INFO - 
----- 处理字幕 #42 的方案 #2 -----
2025-08-07 17:41:13,416 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\42_2.mp4
2025-08-07 17:41:13,417 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpms7ul3ov
2025-08-07 17:41:13,417 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1797.mp4 (确认存在: True)
2025-08-07 17:41:13,417 - INFO - 添加场景ID=1797，时长=3.04秒，累计时长=3.04秒
2025-08-07 17:41:13,418 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1798.mp4 (确认存在: True)
2025-08-07 17:41:13,418 - INFO - 添加场景ID=1798，时长=3.12秒，累计时长=6.16秒
2025-08-07 17:41:13,418 - INFO - 准备合并 2 个场景文件，总时长约 6.16秒
2025-08-07 17:41:13,418 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1797.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1798.mp4'

2025-08-07 17:41:13,418 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpms7ul3ov\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpms7ul3ov\temp_combined.mp4
2025-08-07 17:41:13,532 - INFO - 合并后的视频时长: 6.21秒，目标音频时长: 5.59秒
2025-08-07 17:41:13,532 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpms7ul3ov\temp_combined.mp4 -ss 0 -to 5.586 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\42_2.mp4
2025-08-07 17:41:13,842 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:13,842 - INFO - 目标音频时长: 5.59秒
2025-08-07 17:41:13,842 - INFO - 实际视频时长: 5.62秒
2025-08-07 17:41:13,842 - INFO - 时长差异: 0.04秒 (0.66%)
2025-08-07 17:41:13,842 - INFO - ==========================================
2025-08-07 17:41:13,842 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:13,842 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\42_2.mp4
2025-08-07 17:41:13,844 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpms7ul3ov
2025-08-07 17:41:13,885 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:13,885 - INFO -   - 音频时长: 5.59秒
2025-08-07 17:41:13,886 - INFO -   - 视频时长: 5.62秒
2025-08-07 17:41:13,886 - INFO -   - 时长差异: 0.04秒 (0.66%)
2025-08-07 17:41:13,886 - INFO - 
----- 处理字幕 #42 的方案 #3 -----
2025-08-07 17:41:13,886 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\42_3.mp4
2025-08-07 17:41:13,886 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp9x578cvu
2025-08-07 17:41:13,886 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1766.mp4 (确认存在: True)
2025-08-07 17:41:13,886 - INFO - 添加场景ID=1766，时长=3.00秒，累计时长=3.00秒
2025-08-07 17:41:13,887 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1797.mp4 (确认存在: True)
2025-08-07 17:41:13,887 - INFO - 添加场景ID=1797，时长=3.04秒，累计时长=6.04秒
2025-08-07 17:41:13,887 - INFO - 准备合并 2 个场景文件，总时长约 6.04秒
2025-08-07 17:41:13,887 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1766.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1797.mp4'

2025-08-07 17:41:13,887 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp9x578cvu\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp9x578cvu\temp_combined.mp4
2025-08-07 17:41:14,004 - INFO - 合并后的视频时长: 6.09秒，目标音频时长: 5.59秒
2025-08-07 17:41:14,004 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp9x578cvu\temp_combined.mp4 -ss 0 -to 5.586 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\42_3.mp4
2025-08-07 17:41:14,301 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:14,301 - INFO - 目标音频时长: 5.59秒
2025-08-07 17:41:14,301 - INFO - 实际视频时长: 5.62秒
2025-08-07 17:41:14,301 - INFO - 时长差异: 0.04秒 (0.66%)
2025-08-07 17:41:14,301 - INFO - ==========================================
2025-08-07 17:41:14,301 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:14,301 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\42_3.mp4
2025-08-07 17:41:14,302 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp9x578cvu
2025-08-07 17:41:14,346 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:14,346 - INFO -   - 音频时长: 5.59秒
2025-08-07 17:41:14,346 - INFO -   - 视频时长: 5.62秒
2025-08-07 17:41:14,346 - INFO -   - 时长差异: 0.04秒 (0.66%)
2025-08-07 17:41:14,346 - INFO - 
字幕 #42 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:14,346 - INFO - 生成的视频文件:
2025-08-07 17:41:14,346 - INFO -   1. F:/github/aicut_auto/newcut_ai\42_1.mp4
2025-08-07 17:41:14,346 - INFO -   2. F:/github/aicut_auto/newcut_ai\42_2.mp4
2025-08-07 17:41:14,346 - INFO -   3. F:/github/aicut_auto/newcut_ai\42_3.mp4
2025-08-07 17:41:14,346 - INFO - ========== 字幕 #42 处理结束 ==========

