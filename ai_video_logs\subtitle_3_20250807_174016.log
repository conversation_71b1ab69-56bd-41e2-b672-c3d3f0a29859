2025-08-07 17:40:16,269 - INFO - ========== 字幕 #3 处理开始 ==========
2025-08-07 17:40:16,269 - INFO - 字幕内容: 谁知传送符出错，女孩竟意外出现在一间酒店，撞破了一场仙人跳，一个心机女正伙同他人，陷害轮椅上的男人。
2025-08-07 17:40:16,269 - INFO - 字幕序号: [40, 49]
2025-08-07 17:40:16,269 - INFO - 音频文件详情:
2025-08-07 17:40:16,269 - INFO -   - 路径: output\3.wav
2025-08-07 17:40:16,269 - INFO -   - 时长: 6.61秒
2025-08-07 17:40:16,269 - INFO -   - 验证音频时长: 6.61秒
2025-08-07 17:40:16,269 - INFO - 字幕时间戳信息:
2025-08-07 17:40:16,279 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:16,279 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:16,279 - INFO -   - 根据生成的音频时长(6.61秒)已调整字幕时间戳
2025-08-07 17:40:16,279 - INFO - ========== 新模式：为字幕 #3 生成4套场景方案 ==========
2025-08-07 17:40:16,279 - INFO - 字幕序号列表: [40, 49]
2025-08-07 17:40:16,279 - INFO - 
--- 生成方案 #1：基于字幕序号 #40 ---
2025-08-07 17:40:16,279 - INFO - 开始为单个字幕序号 #40 匹配场景，目标时长: 6.61秒
2025-08-07 17:40:16,279 - INFO - 开始查找字幕序号 [40] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:16,279 - INFO - 找到related_overlap场景: scene_id=75, 字幕#40
2025-08-07 17:40:16,279 - INFO - 找到related_overlap场景: scene_id=76, 字幕#40
2025-08-07 17:40:16,280 - INFO - 找到related_between场景: scene_id=66, 字幕#40
2025-08-07 17:40:16,280 - INFO - 找到related_between场景: scene_id=67, 字幕#40
2025-08-07 17:40:16,280 - INFO - 找到related_between场景: scene_id=68, 字幕#40
2025-08-07 17:40:16,280 - INFO - 找到related_between场景: scene_id=69, 字幕#40
2025-08-07 17:40:16,280 - INFO - 找到related_between场景: scene_id=70, 字幕#40
2025-08-07 17:40:16,280 - INFO - 找到related_between场景: scene_id=71, 字幕#40
2025-08-07 17:40:16,280 - INFO - 找到related_between场景: scene_id=72, 字幕#40
2025-08-07 17:40:16,280 - INFO - 找到related_between场景: scene_id=73, 字幕#40
2025-08-07 17:40:16,280 - INFO - 找到related_between场景: scene_id=74, 字幕#40
2025-08-07 17:40:16,281 - INFO - 字幕 #40 找到 2 个overlap场景, 9 个between场景
2025-08-07 17:40:16,281 - INFO - 字幕序号 #40 找到 2 个可用overlap场景, 9 个可用between场景
2025-08-07 17:40:16,281 - INFO - 选择第一个overlap场景作为起点: scene_id=75
2025-08-07 17:40:16,281 - INFO - 添加起点场景: scene_id=75, 时长=1.68秒, 累计时长=1.68秒
2025-08-07 17:40:16,281 - INFO - 起点场景时长不足，需要延伸填充 4.93秒
2025-08-07 17:40:16,281 - INFO - 起点场景在原始列表中的索引: 74
2025-08-07 17:40:16,281 - INFO - 延伸添加场景: scene_id=76 (完整时长 2.32秒)
2025-08-07 17:40:16,281 - INFO - 累计时长: 4.00秒
2025-08-07 17:40:16,281 - INFO - 延伸添加场景: scene_id=77 (完整时长 2.36秒)
2025-08-07 17:40:16,281 - INFO - 累计时长: 6.36秒
2025-08-07 17:40:16,281 - INFO - 延伸添加场景: scene_id=78 (裁剪至 0.25秒)
2025-08-07 17:40:16,281 - INFO - 累计时长: 6.61秒
2025-08-07 17:40:16,281 - INFO - 字幕序号 #40 场景匹配完成，共选择 4 个场景，总时长: 6.61秒
2025-08-07 17:40:16,281 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-08-07 17:40:16,281 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-08-07 17:40:16,281 - INFO - 
--- 生成方案 #2：基于字幕序号 #49 ---
2025-08-07 17:40:16,281 - INFO - 开始为单个字幕序号 #49 匹配场景，目标时长: 6.61秒
2025-08-07 17:40:16,281 - INFO - 开始查找字幕序号 [49] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:16,281 - INFO - 找到related_overlap场景: scene_id=88, 字幕#49
2025-08-07 17:40:16,281 - INFO - 找到related_overlap场景: scene_id=89, 字幕#49
2025-08-07 17:40:16,282 - INFO - 字幕 #49 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:40:16,282 - INFO - 字幕序号 #49 找到 2 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:16,282 - INFO - 选择第一个overlap场景作为起点: scene_id=88
2025-08-07 17:40:16,282 - INFO - 添加起点场景: scene_id=88, 时长=3.00秒, 累计时长=3.00秒
2025-08-07 17:40:16,282 - INFO - 起点场景时长不足，需要延伸填充 3.61秒
2025-08-07 17:40:16,282 - INFO - 起点场景在原始列表中的索引: 87
2025-08-07 17:40:16,282 - INFO - 延伸添加场景: scene_id=89 (完整时长 1.68秒)
2025-08-07 17:40:16,282 - INFO - 累计时长: 4.68秒
2025-08-07 17:40:16,282 - INFO - 延伸添加场景: scene_id=90 (完整时长 1.52秒)
2025-08-07 17:40:16,282 - INFO - 累计时长: 6.20秒
2025-08-07 17:40:16,282 - INFO - 延伸添加场景: scene_id=91 (裁剪至 0.41秒)
2025-08-07 17:40:16,282 - INFO - 累计时长: 6.61秒
2025-08-07 17:40:16,282 - INFO - 字幕序号 #49 场景匹配完成，共选择 4 个场景，总时长: 6.61秒
2025-08-07 17:40:16,282 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-08-07 17:40:16,282 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:16,282 - INFO - ========== 当前模式：为字幕 #3 生成 1 套场景方案 ==========
2025-08-07 17:40:16,282 - INFO - 开始查找字幕序号 [40, 49] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:16,282 - INFO - 找到related_overlap场景: scene_id=75, 字幕#40
2025-08-07 17:40:16,282 - INFO - 找到related_overlap场景: scene_id=76, 字幕#40
2025-08-07 17:40:16,282 - INFO - 找到related_overlap场景: scene_id=88, 字幕#49
2025-08-07 17:40:16,282 - INFO - 找到related_overlap场景: scene_id=89, 字幕#49
2025-08-07 17:40:16,283 - INFO - 找到related_between场景: scene_id=66, 字幕#40
2025-08-07 17:40:16,283 - INFO - 找到related_between场景: scene_id=67, 字幕#40
2025-08-07 17:40:16,283 - INFO - 找到related_between场景: scene_id=68, 字幕#40
2025-08-07 17:40:16,283 - INFO - 找到related_between场景: scene_id=69, 字幕#40
2025-08-07 17:40:16,283 - INFO - 找到related_between场景: scene_id=70, 字幕#40
2025-08-07 17:40:16,283 - INFO - 找到related_between场景: scene_id=71, 字幕#40
2025-08-07 17:40:16,283 - INFO - 找到related_between场景: scene_id=72, 字幕#40
2025-08-07 17:40:16,283 - INFO - 找到related_between场景: scene_id=73, 字幕#40
2025-08-07 17:40:16,283 - INFO - 找到related_between场景: scene_id=74, 字幕#40
2025-08-07 17:40:16,283 - INFO - 字幕 #40 找到 2 个overlap场景, 9 个between场景
2025-08-07 17:40:16,283 - INFO - 字幕 #49 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:40:16,283 - INFO - 共收集 4 个未使用的overlap场景和 9 个未使用的between场景
2025-08-07 17:40:16,283 - INFO - 开始生成方案 #1
2025-08-07 17:40:16,283 - INFO - 方案 #1: 为字幕#40选择初始化overlap场景id=76
2025-08-07 17:40:16,283 - INFO - 方案 #1: 为字幕#49选择初始化overlap场景id=88
2025-08-07 17:40:16,284 - INFO - 方案 #1: 初始选择后，当前总时长=5.32秒
2025-08-07 17:40:16,284 - INFO - 方案 #1: 额外添加overlap场景id=89, 当前总时长=7.00秒
2025-08-07 17:40:16,284 - INFO - 方案 #1: 额外between选择后，当前总时长=7.00秒
2025-08-07 17:40:16,284 - INFO - 方案 #1: 场景总时长(7.00秒)大于音频时长(6.61秒)，需要裁剪
2025-08-07 17:40:16,284 - INFO - 调整前总时长: 7.00秒, 目标时长: 6.61秒
2025-08-07 17:40:16,284 - INFO - 需要裁剪 0.39秒
2025-08-07 17:40:16,284 - INFO - 裁剪最长场景ID=88：从3.00秒裁剪至2.61秒
2025-08-07 17:40:16,284 - INFO - 调整后总时长: 6.61秒，与目标时长差异: 0.00秒
2025-08-07 17:40:16,284 - INFO - 方案 #1 调整/填充后最终总时长: 6.61秒
2025-08-07 17:40:16,284 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:16,284 - INFO - ========== 当前模式：字幕 #3 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:16,284 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:16,284 - INFO - ========== 新模式：字幕 #3 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:16,284 - INFO - 
----- 处理字幕 #3 的方案 #1 -----
2025-08-07 17:40:16,284 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\3_1.mp4
2025-08-07 17:40:16,284 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxtuaee_s
2025-08-07 17:40:16,285 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\75.mp4 (确认存在: True)
2025-08-07 17:40:16,285 - INFO - 添加场景ID=75，时长=1.68秒，累计时长=1.68秒
2025-08-07 17:40:16,285 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\76.mp4 (确认存在: True)
2025-08-07 17:40:16,285 - INFO - 添加场景ID=76，时长=2.32秒，累计时长=4.00秒
2025-08-07 17:40:16,285 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\77.mp4 (确认存在: True)
2025-08-07 17:40:16,285 - INFO - 添加场景ID=77，时长=2.36秒，累计时长=6.36秒
2025-08-07 17:40:16,285 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\78.mp4 (确认存在: True)
2025-08-07 17:40:16,285 - INFO - 添加场景ID=78，时长=2.32秒，累计时长=8.68秒
2025-08-07 17:40:16,285 - INFO - 准备合并 4 个场景文件，总时长约 8.68秒
2025-08-07 17:40:16,285 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/75.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/76.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/77.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/78.mp4'

2025-08-07 17:40:16,285 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpxtuaee_s\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpxtuaee_s\temp_combined.mp4
2025-08-07 17:40:16,446 - INFO - 合并后的视频时长: 8.77秒，目标音频时长: 6.61秒
2025-08-07 17:40:16,446 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpxtuaee_s\temp_combined.mp4 -ss 0 -to 6.605 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\3_1.mp4
2025-08-07 17:40:16,788 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:16,788 - INFO - 目标音频时长: 6.61秒
2025-08-07 17:40:16,788 - INFO - 实际视频时长: 6.66秒
2025-08-07 17:40:16,788 - INFO - 时长差异: 0.06秒 (0.88%)
2025-08-07 17:40:16,788 - INFO - ==========================================
2025-08-07 17:40:16,788 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:16,788 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\3_1.mp4
2025-08-07 17:40:16,789 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxtuaee_s
2025-08-07 17:40:16,833 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:16,833 - INFO -   - 音频时长: 6.61秒
2025-08-07 17:40:16,833 - INFO -   - 视频时长: 6.66秒
2025-08-07 17:40:16,833 - INFO -   - 时长差异: 0.06秒 (0.88%)
2025-08-07 17:40:16,833 - INFO - 
----- 处理字幕 #3 的方案 #2 -----
2025-08-07 17:40:16,833 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\3_2.mp4
2025-08-07 17:40:16,834 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpvwjj51cg
2025-08-07 17:40:16,834 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\88.mp4 (确认存在: True)
2025-08-07 17:40:16,834 - INFO - 添加场景ID=88，时长=3.00秒，累计时长=3.00秒
2025-08-07 17:40:16,834 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\89.mp4 (确认存在: True)
2025-08-07 17:40:16,834 - INFO - 添加场景ID=89，时长=1.68秒，累计时长=4.68秒
2025-08-07 17:40:16,834 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\90.mp4 (确认存在: True)
2025-08-07 17:40:16,834 - INFO - 添加场景ID=90，时长=1.52秒，累计时长=6.20秒
2025-08-07 17:40:16,834 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\91.mp4 (确认存在: True)
2025-08-07 17:40:16,834 - INFO - 添加场景ID=91，时长=1.24秒，累计时长=7.44秒
2025-08-07 17:40:16,835 - INFO - 准备合并 4 个场景文件，总时长约 7.44秒
2025-08-07 17:40:16,835 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/88.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/89.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/90.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/91.mp4'

2025-08-07 17:40:16,835 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpvwjj51cg\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpvwjj51cg\temp_combined.mp4
2025-08-07 17:40:16,987 - INFO - 合并后的视频时长: 7.53秒，目标音频时长: 6.61秒
2025-08-07 17:40:16,988 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpvwjj51cg\temp_combined.mp4 -ss 0 -to 6.605 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\3_2.mp4
2025-08-07 17:40:17,337 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:17,337 - INFO - 目标音频时长: 6.61秒
2025-08-07 17:40:17,337 - INFO - 实际视频时长: 6.66秒
2025-08-07 17:40:17,337 - INFO - 时长差异: 0.06秒 (0.88%)
2025-08-07 17:40:17,337 - INFO - ==========================================
2025-08-07 17:40:17,337 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:17,337 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\3_2.mp4
2025-08-07 17:40:17,338 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpvwjj51cg
2025-08-07 17:40:17,384 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:17,384 - INFO -   - 音频时长: 6.61秒
2025-08-07 17:40:17,384 - INFO -   - 视频时长: 6.66秒
2025-08-07 17:40:17,384 - INFO -   - 时长差异: 0.06秒 (0.88%)
2025-08-07 17:40:17,384 - INFO - 
----- 处理字幕 #3 的方案 #3 -----
2025-08-07 17:40:17,384 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\3_3.mp4
2025-08-07 17:40:17,384 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpu5g0yy7y
2025-08-07 17:40:17,385 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\76.mp4 (确认存在: True)
2025-08-07 17:40:17,385 - INFO - 添加场景ID=76，时长=2.32秒，累计时长=2.32秒
2025-08-07 17:40:17,385 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\88.mp4 (确认存在: True)
2025-08-07 17:40:17,385 - INFO - 添加场景ID=88，时长=3.00秒，累计时长=5.32秒
2025-08-07 17:40:17,385 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\89.mp4 (确认存在: True)
2025-08-07 17:40:17,385 - INFO - 添加场景ID=89，时长=1.68秒，累计时长=7.00秒
2025-08-07 17:40:17,385 - INFO - 准备合并 3 个场景文件，总时长约 7.00秒
2025-08-07 17:40:17,385 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/76.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/88.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/89.mp4'

2025-08-07 17:40:17,385 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpu5g0yy7y\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpu5g0yy7y\temp_combined.mp4
2025-08-07 17:40:17,532 - INFO - 合并后的视频时长: 7.07秒，目标音频时长: 6.61秒
2025-08-07 17:40:17,532 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpu5g0yy7y\temp_combined.mp4 -ss 0 -to 6.605 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\3_3.mp4
2025-08-07 17:40:17,883 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:17,883 - INFO - 目标音频时长: 6.61秒
2025-08-07 17:40:17,883 - INFO - 实际视频时长: 6.66秒
2025-08-07 17:40:17,883 - INFO - 时长差异: 0.06秒 (0.88%)
2025-08-07 17:40:17,883 - INFO - ==========================================
2025-08-07 17:40:17,883 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:17,883 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\3_3.mp4
2025-08-07 17:40:17,884 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpu5g0yy7y
2025-08-07 17:40:17,929 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:17,929 - INFO -   - 音频时长: 6.61秒
2025-08-07 17:40:17,929 - INFO -   - 视频时长: 6.66秒
2025-08-07 17:40:17,929 - INFO -   - 时长差异: 0.06秒 (0.88%)
2025-08-07 17:40:17,929 - INFO - 
字幕 #3 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:17,929 - INFO - 生成的视频文件:
2025-08-07 17:40:17,929 - INFO -   1. F:/github/aicut_auto/newcut_ai\3_1.mp4
2025-08-07 17:40:17,929 - INFO -   2. F:/github/aicut_auto/newcut_ai\3_2.mp4
2025-08-07 17:40:17,929 - INFO -   3. F:/github/aicut_auto/newcut_ai\3_3.mp4
2025-08-07 17:40:17,929 - INFO - ========== 字幕 #3 处理结束 ==========

