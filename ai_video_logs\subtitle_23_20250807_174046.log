2025-08-07 17:40:46,231 - INFO - ========== 字幕 #23 处理开始 ==========
2025-08-07 17:40:46,231 - INFO - 字幕内容: 然而，无耻的亲戚们立刻转变口风，反过来污蔑男人是装病，心机深沉，博取同情。
2025-08-07 17:40:46,231 - INFO - 字幕序号: [648, 653]
2025-08-07 17:40:46,231 - INFO - 音频文件详情:
2025-08-07 17:40:46,231 - INFO -   - 路径: output\23.wav
2025-08-07 17:40:46,231 - INFO -   - 时长: 5.61秒
2025-08-07 17:40:46,231 - INFO -   - 验证音频时长: 5.61秒
2025-08-07 17:40:46,231 - INFO - 字幕时间戳信息:
2025-08-07 17:40:46,241 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:46,241 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:46,241 - INFO -   - 根据生成的音频时长(5.61秒)已调整字幕时间戳
2025-08-07 17:40:46,241 - INFO - ========== 新模式：为字幕 #23 生成4套场景方案 ==========
2025-08-07 17:40:46,241 - INFO - 字幕序号列表: [648, 653]
2025-08-07 17:40:46,241 - INFO - 
--- 生成方案 #1：基于字幕序号 #648 ---
2025-08-07 17:40:46,241 - INFO - 开始为单个字幕序号 #648 匹配场景，目标时长: 5.61秒
2025-08-07 17:40:46,241 - INFO - 开始查找字幕序号 [648] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:46,241 - INFO - 找到related_overlap场景: scene_id=794, 字幕#648
2025-08-07 17:40:46,241 - INFO - 找到related_overlap场景: scene_id=795, 字幕#648
2025-08-07 17:40:46,242 - INFO - 字幕 #648 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:40:46,242 - INFO - 字幕序号 #648 找到 2 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:46,242 - INFO - 选择第一个overlap场景作为起点: scene_id=794
2025-08-07 17:40:46,242 - INFO - 添加起点场景: scene_id=794, 时长=2.04秒, 累计时长=2.04秒
2025-08-07 17:40:46,242 - INFO - 起点场景时长不足，需要延伸填充 3.57秒
2025-08-07 17:40:46,242 - INFO - 起点场景在原始列表中的索引: 793
2025-08-07 17:40:46,242 - INFO - 延伸添加场景: scene_id=795 (完整时长 1.20秒)
2025-08-07 17:40:46,242 - INFO - 累计时长: 3.24秒
2025-08-07 17:40:46,242 - INFO - 延伸添加场景: scene_id=796 (完整时长 1.52秒)
2025-08-07 17:40:46,242 - INFO - 累计时长: 4.76秒
2025-08-07 17:40:46,242 - INFO - 延伸添加场景: scene_id=797 (裁剪至 0.85秒)
2025-08-07 17:40:46,242 - INFO - 累计时长: 5.61秒
2025-08-07 17:40:46,242 - INFO - 字幕序号 #648 场景匹配完成，共选择 4 个场景，总时长: 5.61秒
2025-08-07 17:40:46,242 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-08-07 17:40:46,242 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-08-07 17:40:46,242 - INFO - 
--- 生成方案 #2：基于字幕序号 #653 ---
2025-08-07 17:40:46,242 - INFO - 开始为单个字幕序号 #653 匹配场景，目标时长: 5.61秒
2025-08-07 17:40:46,242 - INFO - 开始查找字幕序号 [653] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:46,242 - INFO - 找到related_overlap场景: scene_id=799, 字幕#653
2025-08-07 17:40:46,244 - INFO - 字幕 #653 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:46,244 - INFO - 字幕序号 #653 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:46,244 - INFO - 选择第一个overlap场景作为起点: scene_id=799
2025-08-07 17:40:46,244 - INFO - 添加起点场景: scene_id=799, 时长=1.12秒, 累计时长=1.12秒
2025-08-07 17:40:46,244 - INFO - 起点场景时长不足，需要延伸填充 4.49秒
2025-08-07 17:40:46,244 - INFO - 起点场景在原始列表中的索引: 798
2025-08-07 17:40:46,244 - INFO - 延伸添加场景: scene_id=800 (完整时长 0.92秒)
2025-08-07 17:40:46,244 - INFO - 累计时长: 2.04秒
2025-08-07 17:40:46,244 - INFO - 延伸添加场景: scene_id=801 (完整时长 1.04秒)
2025-08-07 17:40:46,244 - INFO - 累计时长: 3.08秒
2025-08-07 17:40:46,244 - INFO - 延伸添加场景: scene_id=802 (完整时长 1.44秒)
2025-08-07 17:40:46,244 - INFO - 累计时长: 4.52秒
2025-08-07 17:40:46,244 - INFO - 延伸添加场景: scene_id=803 (裁剪至 1.09秒)
2025-08-07 17:40:46,244 - INFO - 累计时长: 5.61秒
2025-08-07 17:40:46,244 - INFO - 字幕序号 #653 场景匹配完成，共选择 5 个场景，总时长: 5.61秒
2025-08-07 17:40:46,244 - INFO - 方案 #2 生成成功，包含 5 个场景
2025-08-07 17:40:46,244 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:46,244 - INFO - ========== 当前模式：为字幕 #23 生成 1 套场景方案 ==========
2025-08-07 17:40:46,244 - INFO - 开始查找字幕序号 [648, 653] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:46,245 - INFO - 找到related_overlap场景: scene_id=794, 字幕#648
2025-08-07 17:40:46,245 - INFO - 找到related_overlap场景: scene_id=795, 字幕#648
2025-08-07 17:40:46,245 - INFO - 找到related_overlap场景: scene_id=799, 字幕#653
2025-08-07 17:40:46,245 - INFO - 字幕 #648 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:40:46,245 - INFO - 字幕 #653 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:46,245 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-08-07 17:40:46,245 - INFO - 开始生成方案 #1
2025-08-07 17:40:46,245 - INFO - 方案 #1: 为字幕#648选择初始化overlap场景id=794
2025-08-07 17:40:46,245 - INFO - 方案 #1: 为字幕#653选择初始化overlap场景id=799
2025-08-07 17:40:46,245 - INFO - 方案 #1: 初始选择后，当前总时长=3.16秒
2025-08-07 17:40:46,245 - INFO - 方案 #1: 额外添加overlap场景id=795, 当前总时长=4.36秒
2025-08-07 17:40:46,245 - INFO - 方案 #1: 额外between选择后，当前总时长=4.36秒
2025-08-07 17:40:46,245 - INFO - 方案 #1: 场景总时长(4.36秒)小于音频时长(5.61秒)，需要延伸填充
2025-08-07 17:40:46,245 - INFO - 方案 #1: 最后一个场景ID: 795
2025-08-07 17:40:46,245 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 794
2025-08-07 17:40:46,245 - INFO - 方案 #1: 需要填充时长: 1.25秒
2025-08-07 17:40:46,245 - INFO - 方案 #1: 追加场景 scene_id=796 (裁剪至 1.25秒)
2025-08-07 17:40:46,245 - INFO - 方案 #1: 成功填充至目标时长
2025-08-07 17:40:46,245 - INFO - 方案 #1 调整/填充后最终总时长: 5.61秒
2025-08-07 17:40:46,245 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:46,245 - INFO - ========== 当前模式：字幕 #23 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:46,245 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:46,245 - INFO - ========== 新模式：字幕 #23 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:46,245 - INFO - 
----- 处理字幕 #23 的方案 #1 -----
2025-08-07 17:40:46,245 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\23_1.mp4
2025-08-07 17:40:46,246 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpv5jo0f7v
2025-08-07 17:40:46,246 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\794.mp4 (确认存在: True)
2025-08-07 17:40:46,246 - INFO - 添加场景ID=794，时长=2.04秒，累计时长=2.04秒
2025-08-07 17:40:46,246 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\795.mp4 (确认存在: True)
2025-08-07 17:40:46,246 - INFO - 添加场景ID=795，时长=1.20秒，累计时长=3.24秒
2025-08-07 17:40:46,247 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\796.mp4 (确认存在: True)
2025-08-07 17:40:46,247 - INFO - 添加场景ID=796，时长=1.52秒，累计时长=4.76秒
2025-08-07 17:40:46,247 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\797.mp4 (确认存在: True)
2025-08-07 17:40:46,247 - INFO - 添加场景ID=797，时长=1.40秒，累计时长=6.16秒
2025-08-07 17:40:46,247 - INFO - 准备合并 4 个场景文件，总时长约 6.16秒
2025-08-07 17:40:46,247 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/794.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/795.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/796.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/797.mp4'

2025-08-07 17:40:46,247 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpv5jo0f7v\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpv5jo0f7v\temp_combined.mp4
2025-08-07 17:40:46,395 - INFO - 合并后的视频时长: 6.25秒，目标音频时长: 5.61秒
2025-08-07 17:40:46,395 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpv5jo0f7v\temp_combined.mp4 -ss 0 -to 5.61 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\23_1.mp4
2025-08-07 17:40:46,746 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:46,747 - INFO - 目标音频时长: 5.61秒
2025-08-07 17:40:46,747 - INFO - 实际视频时长: 5.66秒
2025-08-07 17:40:46,747 - INFO - 时长差异: 0.05秒 (0.94%)
2025-08-07 17:40:46,747 - INFO - ==========================================
2025-08-07 17:40:46,747 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:46,747 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\23_1.mp4
2025-08-07 17:40:46,747 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpv5jo0f7v
2025-08-07 17:40:46,799 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:46,799 - INFO -   - 音频时长: 5.61秒
2025-08-07 17:40:46,799 - INFO -   - 视频时长: 5.66秒
2025-08-07 17:40:46,799 - INFO -   - 时长差异: 0.05秒 (0.94%)
2025-08-07 17:40:46,799 - INFO - 
----- 处理字幕 #23 的方案 #2 -----
2025-08-07 17:40:46,799 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\23_2.mp4
2025-08-07 17:40:46,800 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpzw7_xs8y
2025-08-07 17:40:46,800 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\799.mp4 (确认存在: True)
2025-08-07 17:40:46,800 - INFO - 添加场景ID=799，时长=1.12秒，累计时长=1.12秒
2025-08-07 17:40:46,800 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\800.mp4 (确认存在: True)
2025-08-07 17:40:46,801 - INFO - 添加场景ID=800，时长=0.92秒，累计时长=2.04秒
2025-08-07 17:40:46,801 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\801.mp4 (确认存在: True)
2025-08-07 17:40:46,801 - INFO - 添加场景ID=801，时长=1.04秒，累计时长=3.08秒
2025-08-07 17:40:46,801 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\802.mp4 (确认存在: True)
2025-08-07 17:40:46,801 - INFO - 添加场景ID=802，时长=1.44秒，累计时长=4.52秒
2025-08-07 17:40:46,801 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\803.mp4 (确认存在: True)
2025-08-07 17:40:46,801 - INFO - 添加场景ID=803，时长=1.24秒，累计时长=5.76秒
2025-08-07 17:40:46,801 - INFO - 准备合并 5 个场景文件，总时长约 5.76秒
2025-08-07 17:40:46,801 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/799.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/800.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/801.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/802.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/803.mp4'

2025-08-07 17:40:46,801 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpzw7_xs8y\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpzw7_xs8y\temp_combined.mp4
2025-08-07 17:40:46,969 - INFO - 合并后的视频时长: 5.88秒，目标音频时长: 5.61秒
2025-08-07 17:40:46,969 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpzw7_xs8y\temp_combined.mp4 -ss 0 -to 5.61 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\23_2.mp4
2025-08-07 17:40:47,301 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:47,301 - INFO - 目标音频时长: 5.61秒
2025-08-07 17:40:47,301 - INFO - 实际视频时长: 5.66秒
2025-08-07 17:40:47,301 - INFO - 时长差异: 0.05秒 (0.94%)
2025-08-07 17:40:47,301 - INFO - ==========================================
2025-08-07 17:40:47,301 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:47,301 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\23_2.mp4
2025-08-07 17:40:47,302 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpzw7_xs8y
2025-08-07 17:40:47,347 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:47,347 - INFO -   - 音频时长: 5.61秒
2025-08-07 17:40:47,347 - INFO -   - 视频时长: 5.66秒
2025-08-07 17:40:47,347 - INFO -   - 时长差异: 0.05秒 (0.94%)
2025-08-07 17:40:47,347 - INFO - 
----- 处理字幕 #23 的方案 #3 -----
2025-08-07 17:40:47,347 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\23_3.mp4
2025-08-07 17:40:47,348 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpp_h4w5q_
2025-08-07 17:40:47,348 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\794.mp4 (确认存在: True)
2025-08-07 17:40:47,348 - INFO - 添加场景ID=794，时长=2.04秒，累计时长=2.04秒
2025-08-07 17:40:47,348 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\799.mp4 (确认存在: True)
2025-08-07 17:40:47,348 - INFO - 添加场景ID=799，时长=1.12秒，累计时长=3.16秒
2025-08-07 17:40:47,348 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\795.mp4 (确认存在: True)
2025-08-07 17:40:47,348 - INFO - 添加场景ID=795，时长=1.20秒，累计时长=4.36秒
2025-08-07 17:40:47,348 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\796.mp4 (确认存在: True)
2025-08-07 17:40:47,348 - INFO - 添加场景ID=796，时长=1.52秒，累计时长=5.88秒
2025-08-07 17:40:47,349 - INFO - 准备合并 4 个场景文件，总时长约 5.88秒
2025-08-07 17:40:47,349 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/794.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/799.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/795.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/796.mp4'

2025-08-07 17:40:47,349 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpp_h4w5q_\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpp_h4w5q_\temp_combined.mp4
2025-08-07 17:40:47,503 - INFO - 合并后的视频时长: 5.97秒，目标音频时长: 5.61秒
2025-08-07 17:40:47,503 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpp_h4w5q_\temp_combined.mp4 -ss 0 -to 5.61 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\23_3.mp4
2025-08-07 17:40:47,842 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:47,842 - INFO - 目标音频时长: 5.61秒
2025-08-07 17:40:47,842 - INFO - 实际视频时长: 5.66秒
2025-08-07 17:40:47,842 - INFO - 时长差异: 0.05秒 (0.94%)
2025-08-07 17:40:47,842 - INFO - ==========================================
2025-08-07 17:40:47,842 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:47,842 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\23_3.mp4
2025-08-07 17:40:47,843 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpp_h4w5q_
2025-08-07 17:40:47,892 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:47,892 - INFO -   - 音频时长: 5.61秒
2025-08-07 17:40:47,892 - INFO -   - 视频时长: 5.66秒
2025-08-07 17:40:47,892 - INFO -   - 时长差异: 0.05秒 (0.94%)
2025-08-07 17:40:47,892 - INFO - 
字幕 #23 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:47,892 - INFO - 生成的视频文件:
2025-08-07 17:40:47,892 - INFO -   1. F:/github/aicut_auto/newcut_ai\23_1.mp4
2025-08-07 17:40:47,892 - INFO -   2. F:/github/aicut_auto/newcut_ai\23_2.mp4
2025-08-07 17:40:47,892 - INFO -   3. F:/github/aicut_auto/newcut_ai\23_3.mp4
2025-08-07 17:40:47,892 - INFO - ========== 字幕 #23 处理结束 ==========

