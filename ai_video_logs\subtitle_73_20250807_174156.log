2025-08-07 17:41:56,430 - INFO - ========== 字幕 #73 处理开始 ==========
2025-08-07 17:41:56,430 - INFO - 字幕内容: 一声“走啦”，女孩化作一道流光消失在天际，只留下这段传奇的故事，在人间久久流传。
2025-08-07 17:41:56,430 - INFO - 字幕序号: [3122, 3124]
2025-08-07 17:41:56,430 - INFO - 音频文件详情:
2025-08-07 17:41:56,430 - INFO -   - 路径: output\73.wav
2025-08-07 17:41:56,430 - INFO -   - 时长: 5.18秒
2025-08-07 17:41:56,431 - INFO -   - 验证音频时长: 5.18秒
2025-08-07 17:41:56,431 - INFO - 字幕时间戳信息:
2025-08-07 17:41:56,440 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:56,440 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:56,440 - INFO -   - 根据生成的音频时长(5.18秒)已调整字幕时间戳
2025-08-07 17:41:56,440 - INFO - ========== 新模式：为字幕 #73 生成4套场景方案 ==========
2025-08-07 17:41:56,440 - INFO - 字幕序号列表: [3122, 3124]
2025-08-07 17:41:56,440 - INFO - 
--- 生成方案 #1：基于字幕序号 #3122 ---
2025-08-07 17:41:56,440 - INFO - 开始为单个字幕序号 #3122 匹配场景，目标时长: 5.18秒
2025-08-07 17:41:56,440 - INFO - 开始查找字幕序号 [3122] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:56,441 - INFO - 找到related_overlap场景: scene_id=3476, 字幕#3122
2025-08-07 17:41:56,442 - INFO - 找到related_between场景: scene_id=3475, 字幕#3122
2025-08-07 17:41:56,442 - INFO - 字幕 #3122 找到 1 个overlap场景, 1 个between场景
2025-08-07 17:41:56,442 - INFO - 字幕序号 #3122 找到 1 个可用overlap场景, 1 个可用between场景
2025-08-07 17:41:56,442 - INFO - 选择第一个overlap场景作为起点: scene_id=3476
2025-08-07 17:41:56,442 - INFO - 添加起点场景: scene_id=3476, 时长=2.92秒, 累计时长=2.92秒
2025-08-07 17:41:56,442 - INFO - 起点场景时长不足，需要延伸填充 2.26秒
2025-08-07 17:41:56,443 - INFO - 起点场景在原始列表中的索引: 3475
2025-08-07 17:41:56,443 - INFO - 延伸添加场景: scene_id=3477 (完整时长 1.16秒)
2025-08-07 17:41:56,443 - INFO - 累计时长: 4.08秒
2025-08-07 17:41:56,443 - INFO - 延伸添加场景: scene_id=3478 (完整时长 0.92秒)
2025-08-07 17:41:56,443 - INFO - 累计时长: 5.00秒
2025-08-07 17:41:56,443 - INFO - 延伸添加场景: scene_id=3479 (裁剪至 0.18秒)
2025-08-07 17:41:56,443 - INFO - 累计时长: 5.18秒
2025-08-07 17:41:56,443 - INFO - 字幕序号 #3122 场景匹配完成，共选择 4 个场景，总时长: 5.18秒
2025-08-07 17:41:56,443 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-08-07 17:41:56,443 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-08-07 17:41:56,443 - INFO - 
--- 生成方案 #2：基于字幕序号 #3124 ---
2025-08-07 17:41:56,443 - INFO - 开始为单个字幕序号 #3124 匹配场景，目标时长: 5.18秒
2025-08-07 17:41:56,443 - INFO - 开始查找字幕序号 [3124] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:56,444 - INFO - 找到related_overlap场景: scene_id=3480, 字幕#3124
2025-08-07 17:41:56,444 - INFO - 找到related_between场景: scene_id=3478, 字幕#3124
2025-08-07 17:41:56,444 - INFO - 找到related_between场景: scene_id=3479, 字幕#3124
2025-08-07 17:41:56,444 - INFO - 字幕 #3124 找到 1 个overlap场景, 2 个between场景
2025-08-07 17:41:56,444 - INFO - 字幕序号 #3124 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:56,444 - INFO - 选择第一个overlap场景作为起点: scene_id=3480
2025-08-07 17:41:56,444 - INFO - 添加起点场景: scene_id=3480, 时长=1.48秒, 累计时长=1.48秒
2025-08-07 17:41:56,444 - INFO - 起点场景时长不足，需要延伸填充 3.70秒
2025-08-07 17:41:56,444 - INFO - 起点场景在原始列表中的索引: 3479
2025-08-07 17:41:56,444 - INFO - 到达列表末尾，仍需填充 3.70秒，从列表开头继续查找
2025-08-07 17:41:56,444 - INFO - 从列表开头延伸添加场景: scene_id=1 (完整时长 1.84秒)
2025-08-07 17:41:56,444 - INFO - 累计时长: 3.32秒
2025-08-07 17:41:56,444 - INFO - 从列表开头延伸添加场景: scene_id=2 (裁剪至 1.86秒)
2025-08-07 17:41:56,444 - INFO - 累计时长: 5.18秒
2025-08-07 17:41:56,444 - INFO - 字幕序号 #3124 场景匹配完成，共选择 3 个场景，总时长: 5.18秒
2025-08-07 17:41:56,444 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-08-07 17:41:56,444 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:56,444 - INFO - ========== 当前模式：为字幕 #73 生成 1 套场景方案 ==========
2025-08-07 17:41:56,444 - INFO - 开始查找字幕序号 [3122, 3124] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:56,445 - INFO - 找到related_overlap场景: scene_id=3476, 字幕#3122
2025-08-07 17:41:56,445 - INFO - 找到related_overlap场景: scene_id=3480, 字幕#3124
2025-08-07 17:41:56,445 - INFO - 找到related_between场景: scene_id=3475, 字幕#3122
2025-08-07 17:41:56,445 - INFO - 找到related_between场景: scene_id=3478, 字幕#3124
2025-08-07 17:41:56,445 - INFO - 找到related_between场景: scene_id=3479, 字幕#3124
2025-08-07 17:41:56,445 - INFO - 字幕 #3122 找到 1 个overlap场景, 1 个between场景
2025-08-07 17:41:56,445 - INFO - 字幕 #3124 找到 1 个overlap场景, 2 个between场景
2025-08-07 17:41:56,445 - INFO - 共收集 2 个未使用的overlap场景和 3 个未使用的between场景
2025-08-07 17:41:56,445 - INFO - 开始生成方案 #1
2025-08-07 17:41:56,445 - INFO - 方案 #1: 为字幕#3122选择初始化overlap场景id=3476
2025-08-07 17:41:56,445 - INFO - 方案 #1: 为字幕#3124选择初始化overlap场景id=3480
2025-08-07 17:41:56,445 - INFO - 方案 #1: 初始选择后，当前总时长=4.40秒
2025-08-07 17:41:56,445 - INFO - 方案 #1: 额外between选择后，当前总时长=4.40秒
2025-08-07 17:41:56,445 - INFO - 方案 #1: 额外添加between场景id=3478, 当前总时长=5.32秒
2025-08-07 17:41:56,445 - INFO - 方案 #1: 场景总时长(5.32秒)大于音频时长(5.18秒)，需要裁剪
2025-08-07 17:41:56,446 - INFO - 调整前总时长: 5.32秒, 目标时长: 5.18秒
2025-08-07 17:41:56,446 - INFO - 需要裁剪 0.14秒
2025-08-07 17:41:56,446 - INFO - 裁剪最长场景ID=3476：从2.92秒裁剪至2.78秒
2025-08-07 17:41:56,446 - INFO - 调整后总时长: 5.18秒，与目标时长差异: 0.00秒
2025-08-07 17:41:56,446 - INFO - 方案 #1 调整/填充后最终总时长: 5.18秒
2025-08-07 17:41:56,446 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:56,446 - INFO - ========== 当前模式：字幕 #73 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:56,446 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:56,446 - INFO - ========== 新模式：字幕 #73 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:56,446 - INFO - 
----- 处理字幕 #73 的方案 #1 -----
2025-08-07 17:41:56,446 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\73_1.mp4
2025-08-07 17:41:56,446 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpx0234v_w
2025-08-07 17:41:56,447 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3476.mp4 (确认存在: True)
2025-08-07 17:41:56,447 - INFO - 添加场景ID=3476，时长=2.92秒，累计时长=2.92秒
2025-08-07 17:41:56,447 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3477.mp4 (确认存在: True)
2025-08-07 17:41:56,447 - INFO - 添加场景ID=3477，时长=1.16秒，累计时长=4.08秒
2025-08-07 17:41:56,447 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3478.mp4 (确认存在: True)
2025-08-07 17:41:56,447 - INFO - 添加场景ID=3478，时长=0.92秒，累计时长=5.00秒
2025-08-07 17:41:56,447 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3479.mp4 (确认存在: True)
2025-08-07 17:41:56,447 - INFO - 添加场景ID=3479，时长=1.28秒，累计时长=6.28秒
2025-08-07 17:41:56,447 - INFO - 准备合并 4 个场景文件，总时长约 6.28秒
2025-08-07 17:41:56,447 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3476.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3477.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3478.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3479.mp4'

2025-08-07 17:41:56,447 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpx0234v_w\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpx0234v_w\temp_combined.mp4
2025-08-07 17:41:56,595 - INFO - 合并后的视频时长: 6.37秒，目标音频时长: 5.18秒
2025-08-07 17:41:56,595 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpx0234v_w\temp_combined.mp4 -ss 0 -to 5.176 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\73_1.mp4
2025-08-07 17:41:56,925 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:56,925 - INFO - 目标音频时长: 5.18秒
2025-08-07 17:41:56,925 - INFO - 实际视频时长: 5.22秒
2025-08-07 17:41:56,925 - INFO - 时长差异: 0.05秒 (0.91%)
2025-08-07 17:41:56,925 - INFO - ==========================================
2025-08-07 17:41:56,925 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:56,925 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\73_1.mp4
2025-08-07 17:41:56,925 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpx0234v_w
2025-08-07 17:41:56,970 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:56,971 - INFO -   - 音频时长: 5.18秒
2025-08-07 17:41:56,971 - INFO -   - 视频时长: 5.22秒
2025-08-07 17:41:56,971 - INFO -   - 时长差异: 0.05秒 (0.91%)
2025-08-07 17:41:56,971 - INFO - 
----- 处理字幕 #73 的方案 #2 -----
2025-08-07 17:41:56,971 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\73_2.mp4
2025-08-07 17:41:56,971 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpq9x02lc4
2025-08-07 17:41:56,971 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3480.mp4 (确认存在: True)
2025-08-07 17:41:56,972 - INFO - 添加场景ID=3480，时长=1.48秒，累计时长=1.48秒
2025-08-07 17:41:56,972 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1.mp4 (确认存在: True)
2025-08-07 17:41:56,972 - INFO - 添加场景ID=1，时长=1.84秒，累计时长=3.32秒
2025-08-07 17:41:56,972 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2.mp4 (确认存在: True)
2025-08-07 17:41:56,972 - INFO - 添加场景ID=2，时长=4.16秒，累计时长=7.48秒
2025-08-07 17:41:56,972 - INFO - 准备合并 3 个场景文件，总时长约 7.48秒
2025-08-07 17:41:56,972 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3480.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2.mp4'

2025-08-07 17:41:56,972 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpq9x02lc4\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpq9x02lc4\temp_combined.mp4
2025-08-07 17:41:57,112 - INFO - 合并后的视频时长: 7.55秒，目标音频时长: 5.18秒
2025-08-07 17:41:57,112 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpq9x02lc4\temp_combined.mp4 -ss 0 -to 5.176 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\73_2.mp4
2025-08-07 17:41:57,432 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:57,432 - INFO - 目标音频时长: 5.18秒
2025-08-07 17:41:57,432 - INFO - 实际视频时长: 5.22秒
2025-08-07 17:41:57,432 - INFO - 时长差异: 0.05秒 (0.91%)
2025-08-07 17:41:57,432 - INFO - ==========================================
2025-08-07 17:41:57,432 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:57,432 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\73_2.mp4
2025-08-07 17:41:57,433 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpq9x02lc4
2025-08-07 17:41:57,477 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:57,477 - INFO -   - 音频时长: 5.18秒
2025-08-07 17:41:57,477 - INFO -   - 视频时长: 5.22秒
2025-08-07 17:41:57,477 - INFO -   - 时长差异: 0.05秒 (0.91%)
2025-08-07 17:41:57,477 - INFO - 
----- 处理字幕 #73 的方案 #3 -----
2025-08-07 17:41:57,477 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\73_3.mp4
2025-08-07 17:41:57,477 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxtdwy4qh
2025-08-07 17:41:57,478 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3476.mp4 (确认存在: True)
2025-08-07 17:41:57,478 - INFO - 添加场景ID=3476，时长=2.92秒，累计时长=2.92秒
2025-08-07 17:41:57,478 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3480.mp4 (确认存在: True)
2025-08-07 17:41:57,478 - INFO - 添加场景ID=3480，时长=1.48秒，累计时长=4.40秒
2025-08-07 17:41:57,478 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3478.mp4 (确认存在: True)
2025-08-07 17:41:57,478 - INFO - 添加场景ID=3478，时长=0.92秒，累计时长=5.32秒
2025-08-07 17:41:57,478 - INFO - 准备合并 3 个场景文件，总时长约 5.32秒
2025-08-07 17:41:57,478 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3476.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3480.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3478.mp4'

2025-08-07 17:41:57,478 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpxtdwy4qh\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpxtdwy4qh\temp_combined.mp4
2025-08-07 17:41:57,612 - INFO - 合并后的视频时长: 5.39秒，目标音频时长: 5.18秒
2025-08-07 17:41:57,612 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpxtdwy4qh\temp_combined.mp4 -ss 0 -to 5.176 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\73_3.mp4
2025-08-07 17:41:57,944 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:57,944 - INFO - 目标音频时长: 5.18秒
2025-08-07 17:41:57,944 - INFO - 实际视频时长: 5.22秒
2025-08-07 17:41:57,944 - INFO - 时长差异: 0.05秒 (0.91%)
2025-08-07 17:41:57,944 - INFO - ==========================================
2025-08-07 17:41:57,944 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:57,944 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\73_3.mp4
2025-08-07 17:41:57,944 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxtdwy4qh
2025-08-07 17:41:57,988 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:57,988 - INFO -   - 音频时长: 5.18秒
2025-08-07 17:41:57,988 - INFO -   - 视频时长: 5.22秒
2025-08-07 17:41:57,988 - INFO -   - 时长差异: 0.05秒 (0.91%)
2025-08-07 17:41:57,988 - INFO - 
字幕 #73 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:57,988 - INFO - 生成的视频文件:
2025-08-07 17:41:57,988 - INFO -   1. F:/github/aicut_auto/newcut_ai\73_1.mp4
2025-08-07 17:41:57,988 - INFO -   2. F:/github/aicut_auto/newcut_ai\73_2.mp4
2025-08-07 17:41:57,988 - INFO -   3. F:/github/aicut_auto/newcut_ai\73_3.mp4
2025-08-07 17:41:57,988 - INFO - ========== 字幕 #73 处理结束 ==========

