2025-08-07 17:40:41,846 - INFO - ========== 字幕 #20 处理开始 ==========
2025-08-07 17:40:41,846 - INFO - 字幕内容: 女孩被彻底激怒，她与二弟立下赌约，若能治好男人的腿，二弟必须跪下磕头，尊称她为老祖宗。
2025-08-07 17:40:41,846 - INFO - 字幕序号: [595, 602]
2025-08-07 17:40:41,846 - INFO - 音频文件详情:
2025-08-07 17:40:41,846 - INFO -   - 路径: output\20.wav
2025-08-07 17:40:41,846 - INFO -   - 时长: 4.97秒
2025-08-07 17:40:41,847 - INFO -   - 验证音频时长: 4.97秒
2025-08-07 17:40:41,847 - INFO - 字幕时间戳信息:
2025-08-07 17:40:41,847 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:41,847 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:41,847 - INFO -   - 根据生成的音频时长(4.97秒)已调整字幕时间戳
2025-08-07 17:40:41,847 - INFO - ========== 新模式：为字幕 #20 生成4套场景方案 ==========
2025-08-07 17:40:41,847 - INFO - 字幕序号列表: [595, 602]
2025-08-07 17:40:41,847 - INFO - 
--- 生成方案 #1：基于字幕序号 #595 ---
2025-08-07 17:40:41,847 - INFO - 开始为单个字幕序号 #595 匹配场景，目标时长: 4.97秒
2025-08-07 17:40:41,847 - INFO - 开始查找字幕序号 [595] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:41,847 - INFO - 找到related_overlap场景: scene_id=719, 字幕#595
2025-08-07 17:40:41,848 - INFO - 字幕 #595 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:41,848 - INFO - 字幕序号 #595 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:41,848 - INFO - 选择第一个overlap场景作为起点: scene_id=719
2025-08-07 17:40:41,848 - INFO - 添加起点场景: scene_id=719, 时长=3.48秒, 累计时长=3.48秒
2025-08-07 17:40:41,848 - INFO - 起点场景时长不足，需要延伸填充 1.49秒
2025-08-07 17:40:41,849 - INFO - 起点场景在原始列表中的索引: 718
2025-08-07 17:40:41,849 - INFO - 延伸添加场景: scene_id=720 (裁剪至 1.49秒)
2025-08-07 17:40:41,849 - INFO - 累计时长: 4.97秒
2025-08-07 17:40:41,849 - INFO - 字幕序号 #595 场景匹配完成，共选择 2 个场景，总时长: 4.97秒
2025-08-07 17:40:41,849 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-08-07 17:40:41,849 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-08-07 17:40:41,849 - INFO - 
--- 生成方案 #2：基于字幕序号 #602 ---
2025-08-07 17:40:41,849 - INFO - 开始为单个字幕序号 #602 匹配场景，目标时长: 4.97秒
2025-08-07 17:40:41,849 - INFO - 开始查找字幕序号 [602] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:41,849 - INFO - 找到related_overlap场景: scene_id=722, 字幕#602
2025-08-07 17:40:41,850 - INFO - 字幕 #602 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:41,850 - INFO - 字幕序号 #602 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:41,850 - INFO - 选择第一个overlap场景作为起点: scene_id=722
2025-08-07 17:40:41,850 - INFO - 添加起点场景: scene_id=722, 时长=5.48秒, 累计时长=5.48秒
2025-08-07 17:40:41,850 - INFO - 起点场景时长已满足要求，无需延伸
2025-08-07 17:40:41,850 - INFO - 方案 #2 生成成功，包含 1 个场景
2025-08-07 17:40:41,850 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:41,850 - INFO - ========== 当前模式：为字幕 #20 生成 1 套场景方案 ==========
2025-08-07 17:40:41,850 - INFO - 开始查找字幕序号 [595, 602] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:41,850 - INFO - 找到related_overlap场景: scene_id=719, 字幕#595
2025-08-07 17:40:41,850 - INFO - 找到related_overlap场景: scene_id=722, 字幕#602
2025-08-07 17:40:41,851 - INFO - 字幕 #595 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:41,851 - INFO - 字幕 #602 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:41,851 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-08-07 17:40:41,851 - INFO - 开始生成方案 #1
2025-08-07 17:40:41,851 - INFO - 方案 #1: 为字幕#595选择初始化overlap场景id=719
2025-08-07 17:40:41,851 - INFO - 方案 #1: 为字幕#602选择初始化overlap场景id=722
2025-08-07 17:40:41,851 - INFO - 方案 #1: 初始选择后，当前总时长=8.96秒
2025-08-07 17:40:41,851 - INFO - 方案 #1: 额外between选择后，当前总时长=8.96秒
2025-08-07 17:40:41,851 - INFO - 方案 #1: 场景总时长(8.96秒)大于音频时长(4.97秒)，需要裁剪
2025-08-07 17:40:41,851 - INFO - 调整前总时长: 8.96秒, 目标时长: 4.97秒
2025-08-07 17:40:41,851 - INFO - 需要裁剪 3.99秒
2025-08-07 17:40:41,851 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-08-07 17:40:41,851 - INFO - 裁剪场景ID=722：从5.48秒裁剪至1.64秒
2025-08-07 17:40:41,851 - INFO - 裁剪场景ID=719：从3.48秒裁剪至3.32秒
2025-08-07 17:40:41,851 - INFO - 调整后总时长: 4.97秒，与目标时长差异: 0.00秒
2025-08-07 17:40:41,851 - INFO - 方案 #1 调整/填充后最终总时长: 4.97秒
2025-08-07 17:40:41,851 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:41,851 - INFO - ========== 当前模式：字幕 #20 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:41,851 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:41,851 - INFO - ========== 新模式：字幕 #20 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:41,851 - INFO - 
----- 处理字幕 #20 的方案 #1 -----
2025-08-07 17:40:41,851 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\20_1.mp4
2025-08-07 17:40:41,852 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpu2t3wnzp
2025-08-07 17:40:41,852 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\719.mp4 (确认存在: True)
2025-08-07 17:40:41,852 - INFO - 添加场景ID=719，时长=3.48秒，累计时长=3.48秒
2025-08-07 17:40:41,852 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\720.mp4 (确认存在: True)
2025-08-07 17:40:41,852 - INFO - 添加场景ID=720，时长=2.20秒，累计时长=5.68秒
2025-08-07 17:40:41,852 - INFO - 准备合并 2 个场景文件，总时长约 5.68秒
2025-08-07 17:40:41,852 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/719.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/720.mp4'

2025-08-07 17:40:41,853 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpu2t3wnzp\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpu2t3wnzp\temp_combined.mp4
2025-08-07 17:40:41,984 - INFO - 合并后的视频时长: 5.73秒，目标音频时长: 4.97秒
2025-08-07 17:40:41,984 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpu2t3wnzp\temp_combined.mp4 -ss 0 -to 4.968 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\20_1.mp4
2025-08-07 17:40:42,260 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:42,261 - INFO - 目标音频时长: 4.97秒
2025-08-07 17:40:42,261 - INFO - 实际视频时长: 5.02秒
2025-08-07 17:40:42,261 - INFO - 时长差异: 0.05秒 (1.11%)
2025-08-07 17:40:42,261 - INFO - ==========================================
2025-08-07 17:40:42,261 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:42,261 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\20_1.mp4
2025-08-07 17:40:42,261 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpu2t3wnzp
2025-08-07 17:40:42,304 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:42,304 - INFO -   - 音频时长: 4.97秒
2025-08-07 17:40:42,304 - INFO -   - 视频时长: 5.02秒
2025-08-07 17:40:42,304 - INFO -   - 时长差异: 0.05秒 (1.11%)
2025-08-07 17:40:42,304 - INFO - 
----- 处理字幕 #20 的方案 #2 -----
2025-08-07 17:40:42,304 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\20_2.mp4
2025-08-07 17:40:42,305 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6u7izq1j
2025-08-07 17:40:42,305 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\722.mp4 (确认存在: True)
2025-08-07 17:40:42,305 - INFO - 添加场景ID=722，时长=5.48秒，累计时长=5.48秒
2025-08-07 17:40:42,305 - INFO - 准备合并 1 个场景文件，总时长约 5.48秒
2025-08-07 17:40:42,305 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/722.mp4'

2025-08-07 17:40:42,306 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp6u7izq1j\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp6u7izq1j\temp_combined.mp4
2025-08-07 17:40:42,424 - INFO - 合并后的视频时长: 5.50秒，目标音频时长: 4.97秒
2025-08-07 17:40:42,424 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp6u7izq1j\temp_combined.mp4 -ss 0 -to 4.968 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\20_2.mp4
2025-08-07 17:40:42,703 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:42,704 - INFO - 目标音频时长: 4.97秒
2025-08-07 17:40:42,704 - INFO - 实际视频时长: 5.02秒
2025-08-07 17:40:42,704 - INFO - 时长差异: 0.05秒 (1.11%)
2025-08-07 17:40:42,704 - INFO - ==========================================
2025-08-07 17:40:42,704 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:42,704 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\20_2.mp4
2025-08-07 17:40:42,704 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6u7izq1j
2025-08-07 17:40:42,757 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:42,757 - INFO -   - 音频时长: 4.97秒
2025-08-07 17:40:42,757 - INFO -   - 视频时长: 5.02秒
2025-08-07 17:40:42,757 - INFO -   - 时长差异: 0.05秒 (1.11%)
2025-08-07 17:40:42,758 - INFO - 
----- 处理字幕 #20 的方案 #3 -----
2025-08-07 17:40:42,758 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\20_3.mp4
2025-08-07 17:40:42,758 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpyd7xfeur
2025-08-07 17:40:42,758 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\719.mp4 (确认存在: True)
2025-08-07 17:40:42,759 - INFO - 添加场景ID=719，时长=3.48秒，累计时长=3.48秒
2025-08-07 17:40:42,759 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\722.mp4 (确认存在: True)
2025-08-07 17:40:42,759 - INFO - 添加场景ID=722，时长=5.48秒，累计时长=8.96秒
2025-08-07 17:40:42,759 - INFO - 场景总时长(8.96秒)已达到音频时长(4.97秒)的1.5倍，停止添加场景
2025-08-07 17:40:42,759 - INFO - 准备合并 2 个场景文件，总时长约 8.96秒
2025-08-07 17:40:42,759 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/719.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/722.mp4'

2025-08-07 17:40:42,759 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpyd7xfeur\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpyd7xfeur\temp_combined.mp4
2025-08-07 17:40:42,881 - INFO - 合并后的视频时长: 9.01秒，目标音频时长: 4.97秒
2025-08-07 17:40:42,881 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpyd7xfeur\temp_combined.mp4 -ss 0 -to 4.968 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\20_3.mp4
2025-08-07 17:40:43,154 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:43,154 - INFO - 目标音频时长: 4.97秒
2025-08-07 17:40:43,155 - INFO - 实际视频时长: 5.02秒
2025-08-07 17:40:43,155 - INFO - 时长差异: 0.05秒 (1.11%)
2025-08-07 17:40:43,155 - INFO - ==========================================
2025-08-07 17:40:43,155 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:43,155 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\20_3.mp4
2025-08-07 17:40:43,155 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpyd7xfeur
2025-08-07 17:40:43,201 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:43,201 - INFO -   - 音频时长: 4.97秒
2025-08-07 17:40:43,201 - INFO -   - 视频时长: 5.02秒
2025-08-07 17:40:43,201 - INFO -   - 时长差异: 0.05秒 (1.11%)
2025-08-07 17:40:43,201 - INFO - 
字幕 #20 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:43,201 - INFO - 生成的视频文件:
2025-08-07 17:40:43,201 - INFO -   1. F:/github/aicut_auto/newcut_ai\20_1.mp4
2025-08-07 17:40:43,201 - INFO -   2. F:/github/aicut_auto/newcut_ai\20_2.mp4
2025-08-07 17:40:43,201 - INFO -   3. F:/github/aicut_auto/newcut_ai\20_3.mp4
2025-08-07 17:40:43,201 - INFO - ========== 字幕 #20 处理结束 ==========

