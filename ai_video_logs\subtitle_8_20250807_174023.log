2025-08-07 17:40:23,877 - INFO - ========== 字幕 #8 处理开始 ==========
2025-08-07 17:40:23,877 - INFO - 字幕内容: 眼看阴谋要被识破，男人的二弟恼羞成怒，竟要对女孩动手，幸好楚家爷爷及时赶到，一声怒喝制止了闹剧。
2025-08-07 17:40:23,877 - INFO - 字幕序号: [91, 94]
2025-08-07 17:40:23,877 - INFO - 音频文件详情:
2025-08-07 17:40:23,877 - INFO -   - 路径: output\8.wav
2025-08-07 17:40:23,877 - INFO -   - 时长: 5.43秒
2025-08-07 17:40:23,877 - INFO -   - 验证音频时长: 5.43秒
2025-08-07 17:40:23,877 - INFO - 字幕时间戳信息:
2025-08-07 17:40:23,877 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:23,877 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:23,877 - INFO -   - 根据生成的音频时长(5.43秒)已调整字幕时间戳
2025-08-07 17:40:23,877 - INFO - ========== 新模式：为字幕 #8 生成4套场景方案 ==========
2025-08-07 17:40:23,877 - INFO - 字幕序号列表: [91, 94]
2025-08-07 17:40:23,877 - INFO - 
--- 生成方案 #1：基于字幕序号 #91 ---
2025-08-07 17:40:23,877 - INFO - 开始为单个字幕序号 #91 匹配场景，目标时长: 5.43秒
2025-08-07 17:40:23,877 - INFO - 开始查找字幕序号 [91] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:23,877 - INFO - 找到related_overlap场景: scene_id=133, 字幕#91
2025-08-07 17:40:23,877 - INFO - 找到related_overlap场景: scene_id=134, 字幕#91
2025-08-07 17:40:23,878 - INFO - 找到related_between场景: scene_id=135, 字幕#91
2025-08-07 17:40:23,878 - INFO - 找到related_between场景: scene_id=136, 字幕#91
2025-08-07 17:40:23,878 - INFO - 找到related_between场景: scene_id=137, 字幕#91
2025-08-07 17:40:23,878 - INFO - 找到related_between场景: scene_id=138, 字幕#91
2025-08-07 17:40:23,878 - INFO - 找到related_between场景: scene_id=139, 字幕#91
2025-08-07 17:40:23,878 - INFO - 字幕 #91 找到 2 个overlap场景, 5 个between场景
2025-08-07 17:40:23,878 - INFO - 字幕序号 #91 找到 2 个可用overlap场景, 5 个可用between场景
2025-08-07 17:40:23,878 - INFO - 选择第一个overlap场景作为起点: scene_id=133
2025-08-07 17:40:23,878 - INFO - 添加起点场景: scene_id=133, 时长=3.36秒, 累计时长=3.36秒
2025-08-07 17:40:23,879 - INFO - 起点场景时长不足，需要延伸填充 2.07秒
2025-08-07 17:40:23,879 - INFO - 起点场景在原始列表中的索引: 132
2025-08-07 17:40:23,879 - INFO - 延伸添加场景: scene_id=134 (完整时长 1.04秒)
2025-08-07 17:40:23,879 - INFO - 累计时长: 4.40秒
2025-08-07 17:40:23,879 - INFO - 延伸添加场景: scene_id=135 (完整时长 0.52秒)
2025-08-07 17:40:23,879 - INFO - 累计时长: 4.92秒
2025-08-07 17:40:23,879 - INFO - 延伸添加场景: scene_id=136 (裁剪至 0.51秒)
2025-08-07 17:40:23,879 - INFO - 累计时长: 5.43秒
2025-08-07 17:40:23,879 - INFO - 字幕序号 #91 场景匹配完成，共选择 4 个场景，总时长: 5.43秒
2025-08-07 17:40:23,879 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-08-07 17:40:23,879 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-08-07 17:40:23,879 - INFO - 
--- 生成方案 #2：基于字幕序号 #94 ---
2025-08-07 17:40:23,879 - INFO - 开始为单个字幕序号 #94 匹配场景，目标时长: 5.43秒
2025-08-07 17:40:23,879 - INFO - 开始查找字幕序号 [94] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:23,879 - INFO - 找到related_overlap场景: scene_id=140, 字幕#94
2025-08-07 17:40:23,879 - INFO - 找到related_overlap场景: scene_id=141, 字幕#94
2025-08-07 17:40:23,879 - INFO - 找到related_between场景: scene_id=142, 字幕#94
2025-08-07 17:40:23,879 - INFO - 找到related_between场景: scene_id=143, 字幕#94
2025-08-07 17:40:23,879 - INFO - 找到related_between场景: scene_id=144, 字幕#94
2025-08-07 17:40:23,879 - INFO - 找到related_between场景: scene_id=145, 字幕#94
2025-08-07 17:40:23,879 - INFO - 找到related_between场景: scene_id=146, 字幕#94
2025-08-07 17:40:23,879 - INFO - 找到related_between场景: scene_id=147, 字幕#94
2025-08-07 17:40:23,879 - INFO - 找到related_between场景: scene_id=148, 字幕#94
2025-08-07 17:40:23,879 - INFO - 找到related_between场景: scene_id=149, 字幕#94
2025-08-07 17:40:23,879 - INFO - 找到related_between场景: scene_id=150, 字幕#94
2025-08-07 17:40:23,880 - INFO - 字幕 #94 找到 2 个overlap场景, 9 个between场景
2025-08-07 17:40:23,880 - INFO - 字幕序号 #94 找到 2 个可用overlap场景, 9 个可用between场景
2025-08-07 17:40:23,880 - INFO - 选择第一个overlap场景作为起点: scene_id=140
2025-08-07 17:40:23,880 - INFO - 添加起点场景: scene_id=140, 时长=5.68秒, 累计时长=5.68秒
2025-08-07 17:40:23,880 - INFO - 起点场景时长已满足要求，无需延伸
2025-08-07 17:40:23,880 - INFO - 方案 #2 生成成功，包含 1 个场景
2025-08-07 17:40:23,880 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:23,880 - INFO - ========== 当前模式：为字幕 #8 生成 1 套场景方案 ==========
2025-08-07 17:40:23,880 - INFO - 开始查找字幕序号 [91, 94] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:23,880 - INFO - 找到related_overlap场景: scene_id=133, 字幕#91
2025-08-07 17:40:23,880 - INFO - 找到related_overlap场景: scene_id=134, 字幕#91
2025-08-07 17:40:23,880 - INFO - 找到related_overlap场景: scene_id=140, 字幕#94
2025-08-07 17:40:23,880 - INFO - 找到related_overlap场景: scene_id=141, 字幕#94
2025-08-07 17:40:23,881 - INFO - 找到related_between场景: scene_id=135, 字幕#91
2025-08-07 17:40:23,881 - INFO - 找到related_between场景: scene_id=136, 字幕#91
2025-08-07 17:40:23,881 - INFO - 找到related_between场景: scene_id=137, 字幕#91
2025-08-07 17:40:23,881 - INFO - 找到related_between场景: scene_id=138, 字幕#91
2025-08-07 17:40:23,881 - INFO - 找到related_between场景: scene_id=139, 字幕#91
2025-08-07 17:40:23,881 - INFO - 找到related_between场景: scene_id=142, 字幕#94
2025-08-07 17:40:23,881 - INFO - 找到related_between场景: scene_id=143, 字幕#94
2025-08-07 17:40:23,881 - INFO - 找到related_between场景: scene_id=144, 字幕#94
2025-08-07 17:40:23,881 - INFO - 找到related_between场景: scene_id=145, 字幕#94
2025-08-07 17:40:23,881 - INFO - 找到related_between场景: scene_id=146, 字幕#94
2025-08-07 17:40:23,881 - INFO - 找到related_between场景: scene_id=147, 字幕#94
2025-08-07 17:40:23,881 - INFO - 找到related_between场景: scene_id=148, 字幕#94
2025-08-07 17:40:23,881 - INFO - 找到related_between场景: scene_id=149, 字幕#94
2025-08-07 17:40:23,881 - INFO - 找到related_between场景: scene_id=150, 字幕#94
2025-08-07 17:40:23,881 - INFO - 字幕 #91 找到 2 个overlap场景, 5 个between场景
2025-08-07 17:40:23,881 - INFO - 字幕 #94 找到 2 个overlap场景, 9 个between场景
2025-08-07 17:40:23,881 - INFO - 共收集 4 个未使用的overlap场景和 14 个未使用的between场景
2025-08-07 17:40:23,881 - INFO - 开始生成方案 #1
2025-08-07 17:40:23,881 - INFO - 方案 #1: 为字幕#91选择初始化overlap场景id=133
2025-08-07 17:40:23,881 - INFO - 方案 #1: 为字幕#94选择初始化overlap场景id=141
2025-08-07 17:40:23,881 - INFO - 方案 #1: 初始选择后，当前总时长=5.28秒
2025-08-07 17:40:23,881 - INFO - 方案 #1: 额外添加overlap场景id=140, 当前总时长=10.96秒
2025-08-07 17:40:23,881 - INFO - 方案 #1: 额外between选择后，当前总时长=10.96秒
2025-08-07 17:40:23,881 - INFO - 方案 #1: 场景总时长(10.96秒)大于音频时长(5.43秒)，需要裁剪
2025-08-07 17:40:23,881 - INFO - 调整前总时长: 10.96秒, 目标时长: 5.43秒
2025-08-07 17:40:23,881 - INFO - 需要裁剪 5.53秒
2025-08-07 17:40:23,882 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-08-07 17:40:23,882 - INFO - 裁剪场景ID=140：从5.68秒裁剪至1.70秒
2025-08-07 17:40:23,882 - INFO - 裁剪场景ID=133：从3.36秒裁剪至1.80秒
2025-08-07 17:40:23,882 - INFO - 调整后总时长: 5.43秒，与目标时长差异: 0.00秒
2025-08-07 17:40:23,882 - INFO - 方案 #1 调整/填充后最终总时长: 5.43秒
2025-08-07 17:40:23,882 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:23,882 - INFO - ========== 当前模式：字幕 #8 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:23,882 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:23,882 - INFO - ========== 新模式：字幕 #8 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:23,882 - INFO - 
----- 处理字幕 #8 的方案 #1 -----
2025-08-07 17:40:23,882 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\8_1.mp4
2025-08-07 17:40:23,882 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpc00d_a_v
2025-08-07 17:40:23,883 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\133.mp4 (确认存在: True)
2025-08-07 17:40:23,883 - INFO - 添加场景ID=133，时长=3.36秒，累计时长=3.36秒
2025-08-07 17:40:23,883 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\134.mp4 (确认存在: True)
2025-08-07 17:40:23,883 - INFO - 添加场景ID=134，时长=1.04秒，累计时长=4.40秒
2025-08-07 17:40:23,883 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\135.mp4 (确认存在: True)
2025-08-07 17:40:23,883 - INFO - 添加场景ID=135，时长=0.52秒，累计时长=4.92秒
2025-08-07 17:40:23,883 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\136.mp4 (确认存在: True)
2025-08-07 17:40:23,883 - INFO - 添加场景ID=136，时长=0.88秒，累计时长=5.80秒
2025-08-07 17:40:23,883 - INFO - 准备合并 4 个场景文件，总时长约 5.80秒
2025-08-07 17:40:23,883 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/133.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/134.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/135.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/136.mp4'

2025-08-07 17:40:23,883 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpc00d_a_v\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpc00d_a_v\temp_combined.mp4
2025-08-07 17:40:24,035 - INFO - 合并后的视频时长: 5.89秒，目标音频时长: 5.43秒
2025-08-07 17:40:24,035 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpc00d_a_v\temp_combined.mp4 -ss 0 -to 5.428 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\8_1.mp4
2025-08-07 17:40:24,378 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:24,378 - INFO - 目标音频时长: 5.43秒
2025-08-07 17:40:24,378 - INFO - 实际视频时长: 5.46秒
2025-08-07 17:40:24,378 - INFO - 时长差异: 0.04秒 (0.64%)
2025-08-07 17:40:24,378 - INFO - ==========================================
2025-08-07 17:40:24,378 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:24,378 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\8_1.mp4
2025-08-07 17:40:24,378 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpc00d_a_v
2025-08-07 17:40:24,424 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:24,424 - INFO -   - 音频时长: 5.43秒
2025-08-07 17:40:24,424 - INFO -   - 视频时长: 5.46秒
2025-08-07 17:40:24,424 - INFO -   - 时长差异: 0.04秒 (0.64%)
2025-08-07 17:40:24,424 - INFO - 
----- 处理字幕 #8 的方案 #2 -----
2025-08-07 17:40:24,424 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\8_2.mp4
2025-08-07 17:40:24,424 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcp_oak2y
2025-08-07 17:40:24,425 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\140.mp4 (确认存在: True)
2025-08-07 17:40:24,425 - INFO - 添加场景ID=140，时长=5.68秒，累计时长=5.68秒
2025-08-07 17:40:24,425 - INFO - 准备合并 1 个场景文件，总时长约 5.68秒
2025-08-07 17:40:24,425 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/140.mp4'

2025-08-07 17:40:24,425 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpcp_oak2y\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpcp_oak2y\temp_combined.mp4
2025-08-07 17:40:24,540 - INFO - 合并后的视频时长: 5.70秒，目标音频时长: 5.43秒
2025-08-07 17:40:24,540 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpcp_oak2y\temp_combined.mp4 -ss 0 -to 5.428 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\8_2.mp4
2025-08-07 17:40:24,878 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:24,878 - INFO - 目标音频时长: 5.43秒
2025-08-07 17:40:24,878 - INFO - 实际视频时长: 5.46秒
2025-08-07 17:40:24,878 - INFO - 时长差异: 0.04秒 (0.64%)
2025-08-07 17:40:24,878 - INFO - ==========================================
2025-08-07 17:40:24,878 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:24,878 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\8_2.mp4
2025-08-07 17:40:24,879 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcp_oak2y
2025-08-07 17:40:24,922 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:24,922 - INFO -   - 音频时长: 5.43秒
2025-08-07 17:40:24,922 - INFO -   - 视频时长: 5.46秒
2025-08-07 17:40:24,922 - INFO -   - 时长差异: 0.04秒 (0.64%)
2025-08-07 17:40:24,922 - INFO - 
----- 处理字幕 #8 的方案 #3 -----
2025-08-07 17:40:24,922 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\8_3.mp4
2025-08-07 17:40:24,923 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp9waz3p3u
2025-08-07 17:40:24,923 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\133.mp4 (确认存在: True)
2025-08-07 17:40:24,923 - INFO - 添加场景ID=133，时长=3.36秒，累计时长=3.36秒
2025-08-07 17:40:24,923 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\141.mp4 (确认存在: True)
2025-08-07 17:40:24,923 - INFO - 添加场景ID=141，时长=1.92秒，累计时长=5.28秒
2025-08-07 17:40:24,923 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\140.mp4 (确认存在: True)
2025-08-07 17:40:24,923 - INFO - 添加场景ID=140，时长=5.68秒，累计时长=10.96秒
2025-08-07 17:40:24,923 - INFO - 场景总时长(10.96秒)已达到音频时长(5.43秒)的1.5倍，停止添加场景
2025-08-07 17:40:24,923 - INFO - 准备合并 3 个场景文件，总时长约 10.96秒
2025-08-07 17:40:24,923 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/133.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/141.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/140.mp4'

2025-08-07 17:40:24,924 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp9waz3p3u\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp9waz3p3u\temp_combined.mp4
2025-08-07 17:40:25,056 - INFO - 合并后的视频时长: 11.03秒，目标音频时长: 5.43秒
2025-08-07 17:40:25,056 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp9waz3p3u\temp_combined.mp4 -ss 0 -to 5.428 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\8_3.mp4
2025-08-07 17:40:25,393 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:25,393 - INFO - 目标音频时长: 5.43秒
2025-08-07 17:40:25,393 - INFO - 实际视频时长: 5.46秒
2025-08-07 17:40:25,393 - INFO - 时长差异: 0.04秒 (0.64%)
2025-08-07 17:40:25,393 - INFO - ==========================================
2025-08-07 17:40:25,393 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:25,393 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\8_3.mp4
2025-08-07 17:40:25,394 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp9waz3p3u
2025-08-07 17:40:25,437 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:25,437 - INFO -   - 音频时长: 5.43秒
2025-08-07 17:40:25,437 - INFO -   - 视频时长: 5.46秒
2025-08-07 17:40:25,437 - INFO -   - 时长差异: 0.04秒 (0.64%)
2025-08-07 17:40:25,438 - INFO - 
字幕 #8 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:25,438 - INFO - 生成的视频文件:
2025-08-07 17:40:25,438 - INFO -   1. F:/github/aicut_auto/newcut_ai\8_1.mp4
2025-08-07 17:40:25,438 - INFO -   2. F:/github/aicut_auto/newcut_ai\8_2.mp4
2025-08-07 17:40:25,438 - INFO -   3. F:/github/aicut_auto/newcut_ai\8_3.mp4
2025-08-07 17:40:25,438 - INFO - ========== 字幕 #8 处理结束 ==========

