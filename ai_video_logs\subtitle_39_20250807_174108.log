2025-08-07 17:41:08,621 - INFO - ========== 字幕 #39 处理开始 ==========
2025-08-07 17:41:08,621 - INFO - 字幕内容: 在符咒的作用下，心机女承认是自己主动勾引助理，甚至为此精心准备，专门穿了黑丝。
2025-08-07 17:41:08,621 - INFO - 字幕序号: [1399, 1408]
2025-08-07 17:41:08,621 - INFO - 音频文件详情:
2025-08-07 17:41:08,621 - INFO -   - 路径: output\39.wav
2025-08-07 17:41:08,621 - INFO -   - 时长: 4.96秒
2025-08-07 17:41:08,622 - INFO -   - 验证音频时长: 4.96秒
2025-08-07 17:41:08,622 - INFO - 字幕时间戳信息:
2025-08-07 17:41:08,622 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:08,622 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:08,622 - INFO -   - 根据生成的音频时长(4.96秒)已调整字幕时间戳
2025-08-07 17:41:08,622 - INFO - ========== 新模式：为字幕 #39 生成4套场景方案 ==========
2025-08-07 17:41:08,622 - INFO - 字幕序号列表: [1399, 1408]
2025-08-07 17:41:08,622 - INFO - 
--- 生成方案 #1：基于字幕序号 #1399 ---
2025-08-07 17:41:08,622 - INFO - 开始为单个字幕序号 #1399 匹配场景，目标时长: 4.96秒
2025-08-07 17:41:08,622 - INFO - 开始查找字幕序号 [1399] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:08,623 - INFO - 找到related_overlap场景: scene_id=1609, 字幕#1399
2025-08-07 17:41:08,623 - INFO - 找到related_between场景: scene_id=1607, 字幕#1399
2025-08-07 17:41:08,623 - INFO - 找到related_between场景: scene_id=1608, 字幕#1399
2025-08-07 17:41:08,623 - INFO - 找到related_between场景: scene_id=1610, 字幕#1399
2025-08-07 17:41:08,623 - INFO - 找到related_between场景: scene_id=1611, 字幕#1399
2025-08-07 17:41:08,623 - INFO - 找到related_between场景: scene_id=1612, 字幕#1399
2025-08-07 17:41:08,624 - INFO - 字幕 #1399 找到 1 个overlap场景, 5 个between场景
2025-08-07 17:41:08,624 - INFO - 字幕序号 #1399 找到 1 个可用overlap场景, 5 个可用between场景
2025-08-07 17:41:08,624 - INFO - 选择第一个overlap场景作为起点: scene_id=1609
2025-08-07 17:41:08,624 - INFO - 添加起点场景: scene_id=1609, 时长=4.44秒, 累计时长=4.44秒
2025-08-07 17:41:08,624 - INFO - 起点场景时长不足，需要延伸填充 0.52秒
2025-08-07 17:41:08,624 - INFO - 起点场景在原始列表中的索引: 1608
2025-08-07 17:41:08,624 - INFO - 延伸添加场景: scene_id=1610 (裁剪至 0.52秒)
2025-08-07 17:41:08,624 - INFO - 累计时长: 4.96秒
2025-08-07 17:41:08,624 - INFO - 字幕序号 #1399 场景匹配完成，共选择 2 个场景，总时长: 4.96秒
2025-08-07 17:41:08,624 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-08-07 17:41:08,624 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-08-07 17:41:08,624 - INFO - 
--- 生成方案 #2：基于字幕序号 #1408 ---
2025-08-07 17:41:08,624 - INFO - 开始为单个字幕序号 #1408 匹配场景，目标时长: 4.96秒
2025-08-07 17:41:08,624 - INFO - 开始查找字幕序号 [1408] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:08,625 - INFO - 找到related_overlap场景: scene_id=1621, 字幕#1408
2025-08-07 17:41:08,625 - INFO - 字幕 #1408 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:08,625 - INFO - 字幕序号 #1408 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:08,625 - INFO - 选择第一个overlap场景作为起点: scene_id=1621
2025-08-07 17:41:08,625 - INFO - 添加起点场景: scene_id=1621, 时长=2.24秒, 累计时长=2.24秒
2025-08-07 17:41:08,625 - INFO - 起点场景时长不足，需要延伸填充 2.72秒
2025-08-07 17:41:08,626 - INFO - 起点场景在原始列表中的索引: 1620
2025-08-07 17:41:08,626 - INFO - 延伸添加场景: scene_id=1622 (完整时长 1.72秒)
2025-08-07 17:41:08,626 - INFO - 累计时长: 3.96秒
2025-08-07 17:41:08,626 - INFO - 延伸添加场景: scene_id=1623 (裁剪至 1.00秒)
2025-08-07 17:41:08,626 - INFO - 累计时长: 4.96秒
2025-08-07 17:41:08,626 - INFO - 字幕序号 #1408 场景匹配完成，共选择 3 个场景，总时长: 4.96秒
2025-08-07 17:41:08,626 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-08-07 17:41:08,626 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:08,626 - INFO - ========== 当前模式：为字幕 #39 生成 1 套场景方案 ==========
2025-08-07 17:41:08,626 - INFO - 开始查找字幕序号 [1399, 1408] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:08,626 - INFO - 找到related_overlap场景: scene_id=1609, 字幕#1399
2025-08-07 17:41:08,626 - INFO - 找到related_overlap场景: scene_id=1621, 字幕#1408
2025-08-07 17:41:08,626 - INFO - 找到related_between场景: scene_id=1607, 字幕#1399
2025-08-07 17:41:08,627 - INFO - 找到related_between场景: scene_id=1608, 字幕#1399
2025-08-07 17:41:08,627 - INFO - 找到related_between场景: scene_id=1610, 字幕#1399
2025-08-07 17:41:08,627 - INFO - 找到related_between场景: scene_id=1611, 字幕#1399
2025-08-07 17:41:08,627 - INFO - 找到related_between场景: scene_id=1612, 字幕#1399
2025-08-07 17:41:08,627 - INFO - 字幕 #1399 找到 1 个overlap场景, 5 个between场景
2025-08-07 17:41:08,627 - INFO - 字幕 #1408 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:08,627 - INFO - 共收集 2 个未使用的overlap场景和 5 个未使用的between场景
2025-08-07 17:41:08,627 - INFO - 开始生成方案 #1
2025-08-07 17:41:08,627 - INFO - 方案 #1: 为字幕#1399选择初始化overlap场景id=1609
2025-08-07 17:41:08,627 - INFO - 方案 #1: 为字幕#1408选择初始化overlap场景id=1621
2025-08-07 17:41:08,627 - INFO - 方案 #1: 初始选择后，当前总时长=6.68秒
2025-08-07 17:41:08,627 - INFO - 方案 #1: 额外between选择后，当前总时长=6.68秒
2025-08-07 17:41:08,627 - INFO - 方案 #1: 场景总时长(6.68秒)大于音频时长(4.96秒)，需要裁剪
2025-08-07 17:41:08,627 - INFO - 调整前总时长: 6.68秒, 目标时长: 4.96秒
2025-08-07 17:41:08,627 - INFO - 需要裁剪 1.72秒
2025-08-07 17:41:08,627 - INFO - 裁剪最长场景ID=1609：从4.44秒裁剪至2.72秒
2025-08-07 17:41:08,627 - INFO - 调整后总时长: 4.96秒，与目标时长差异: 0.00秒
2025-08-07 17:41:08,627 - INFO - 方案 #1 调整/填充后最终总时长: 4.96秒
2025-08-07 17:41:08,627 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:08,627 - INFO - ========== 当前模式：字幕 #39 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:08,627 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:08,627 - INFO - ========== 新模式：字幕 #39 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:08,627 - INFO - 
----- 处理字幕 #39 的方案 #1 -----
2025-08-07 17:41:08,627 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\39_1.mp4
2025-08-07 17:41:08,628 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpdgq02elh
2025-08-07 17:41:08,628 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1609.mp4 (确认存在: True)
2025-08-07 17:41:08,628 - INFO - 添加场景ID=1609，时长=4.44秒，累计时长=4.44秒
2025-08-07 17:41:08,628 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1610.mp4 (确认存在: True)
2025-08-07 17:41:08,628 - INFO - 添加场景ID=1610，时长=1.40秒，累计时长=5.84秒
2025-08-07 17:41:08,628 - INFO - 准备合并 2 个场景文件，总时长约 5.84秒
2025-08-07 17:41:08,628 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1609.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1610.mp4'

2025-08-07 17:41:08,629 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpdgq02elh\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpdgq02elh\temp_combined.mp4
2025-08-07 17:41:08,744 - INFO - 合并后的视频时长: 5.89秒，目标音频时长: 4.96秒
2025-08-07 17:41:08,744 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpdgq02elh\temp_combined.mp4 -ss 0 -to 4.962 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\39_1.mp4
2025-08-07 17:41:09,056 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:09,056 - INFO - 目标音频时长: 4.96秒
2025-08-07 17:41:09,056 - INFO - 实际视频时长: 5.02秒
2025-08-07 17:41:09,056 - INFO - 时长差异: 0.06秒 (1.23%)
2025-08-07 17:41:09,056 - INFO - ==========================================
2025-08-07 17:41:09,056 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:09,056 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\39_1.mp4
2025-08-07 17:41:09,057 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpdgq02elh
2025-08-07 17:41:09,100 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:09,100 - INFO -   - 音频时长: 4.96秒
2025-08-07 17:41:09,100 - INFO -   - 视频时长: 5.02秒
2025-08-07 17:41:09,100 - INFO -   - 时长差异: 0.06秒 (1.23%)
2025-08-07 17:41:09,100 - INFO - 
----- 处理字幕 #39 的方案 #2 -----
2025-08-07 17:41:09,100 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\39_2.mp4
2025-08-07 17:41:09,100 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp2pgnk4gp
2025-08-07 17:41:09,100 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1621.mp4 (确认存在: True)
2025-08-07 17:41:09,101 - INFO - 添加场景ID=1621，时长=2.24秒，累计时长=2.24秒
2025-08-07 17:41:09,101 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1622.mp4 (确认存在: True)
2025-08-07 17:41:09,101 - INFO - 添加场景ID=1622，时长=1.72秒，累计时长=3.96秒
2025-08-07 17:41:09,101 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1623.mp4 (确认存在: True)
2025-08-07 17:41:09,101 - INFO - 添加场景ID=1623，时长=1.28秒，累计时长=5.24秒
2025-08-07 17:41:09,101 - INFO - 准备合并 3 个场景文件，总时长约 5.24秒
2025-08-07 17:41:09,101 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1621.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1622.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1623.mp4'

2025-08-07 17:41:09,101 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp2pgnk4gp\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp2pgnk4gp\temp_combined.mp4
2025-08-07 17:41:09,231 - INFO - 合并后的视频时长: 5.31秒，目标音频时长: 4.96秒
2025-08-07 17:41:09,231 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp2pgnk4gp\temp_combined.mp4 -ss 0 -to 4.962 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\39_2.mp4
2025-08-07 17:41:09,538 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:09,538 - INFO - 目标音频时长: 4.96秒
2025-08-07 17:41:09,538 - INFO - 实际视频时长: 5.02秒
2025-08-07 17:41:09,538 - INFO - 时长差异: 0.06秒 (1.23%)
2025-08-07 17:41:09,538 - INFO - ==========================================
2025-08-07 17:41:09,538 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:09,538 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\39_2.mp4
2025-08-07 17:41:09,539 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp2pgnk4gp
2025-08-07 17:41:09,583 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:09,583 - INFO -   - 音频时长: 4.96秒
2025-08-07 17:41:09,583 - INFO -   - 视频时长: 5.02秒
2025-08-07 17:41:09,583 - INFO -   - 时长差异: 0.06秒 (1.23%)
2025-08-07 17:41:09,583 - INFO - 
----- 处理字幕 #39 的方案 #3 -----
2025-08-07 17:41:09,584 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\39_3.mp4
2025-08-07 17:41:09,584 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp671fye7c
2025-08-07 17:41:09,584 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1609.mp4 (确认存在: True)
2025-08-07 17:41:09,584 - INFO - 添加场景ID=1609，时长=4.44秒，累计时长=4.44秒
2025-08-07 17:41:09,584 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1621.mp4 (确认存在: True)
2025-08-07 17:41:09,584 - INFO - 添加场景ID=1621，时长=2.24秒，累计时长=6.68秒
2025-08-07 17:41:09,584 - INFO - 准备合并 2 个场景文件，总时长约 6.68秒
2025-08-07 17:41:09,584 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1609.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1621.mp4'

2025-08-07 17:41:09,584 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp671fye7c\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp671fye7c\temp_combined.mp4
2025-08-07 17:41:09,704 - INFO - 合并后的视频时长: 6.73秒，目标音频时长: 4.96秒
2025-08-07 17:41:09,704 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp671fye7c\temp_combined.mp4 -ss 0 -to 4.962 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\39_3.mp4
2025-08-07 17:41:10,009 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:10,009 - INFO - 目标音频时长: 4.96秒
2025-08-07 17:41:10,010 - INFO - 实际视频时长: 5.02秒
2025-08-07 17:41:10,010 - INFO - 时长差异: 0.06秒 (1.23%)
2025-08-07 17:41:10,010 - INFO - ==========================================
2025-08-07 17:41:10,010 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:10,010 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\39_3.mp4
2025-08-07 17:41:10,010 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp671fye7c
2025-08-07 17:41:10,061 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:10,061 - INFO -   - 音频时长: 4.96秒
2025-08-07 17:41:10,061 - INFO -   - 视频时长: 5.02秒
2025-08-07 17:41:10,061 - INFO -   - 时长差异: 0.06秒 (1.23%)
2025-08-07 17:41:10,062 - INFO - 
字幕 #39 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:10,062 - INFO - 生成的视频文件:
2025-08-07 17:41:10,062 - INFO -   1. F:/github/aicut_auto/newcut_ai\39_1.mp4
2025-08-07 17:41:10,062 - INFO -   2. F:/github/aicut_auto/newcut_ai\39_2.mp4
2025-08-07 17:41:10,062 - INFO -   3. F:/github/aicut_auto/newcut_ai\39_3.mp4
2025-08-07 17:41:10,062 - INFO - ========== 字幕 #39 处理结束 ==========

