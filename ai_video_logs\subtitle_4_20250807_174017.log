2025-08-07 17:40:17,930 - INFO - ========== 字幕 #4 处理开始 ==========
2025-08-07 17:40:17,930 - INFO - 字幕内容: 女孩环顾四周，目光锐利地扫过众人，开口便问谁是楚家人，心机女立刻否认，并将矛头指向轮椅上的男人。
2025-08-07 17:40:17,930 - INFO - 字幕序号: [50, 56]
2025-08-07 17:40:17,930 - INFO - 音频文件详情:
2025-08-07 17:40:17,930 - INFO -   - 路径: output\4.wav
2025-08-07 17:40:17,930 - INFO -   - 时长: 4.57秒
2025-08-07 17:40:17,930 - INFO -   - 验证音频时长: 4.57秒
2025-08-07 17:40:17,930 - INFO - 字幕时间戳信息:
2025-08-07 17:40:17,940 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:17,940 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:17,940 - INFO -   - 根据生成的音频时长(4.57秒)已调整字幕时间戳
2025-08-07 17:40:17,940 - INFO - ========== 新模式：为字幕 #4 生成4套场景方案 ==========
2025-08-07 17:40:17,940 - INFO - 字幕序号列表: [50, 56]
2025-08-07 17:40:17,940 - INFO - 
--- 生成方案 #1：基于字幕序号 #50 ---
2025-08-07 17:40:17,940 - INFO - 开始为单个字幕序号 #50 匹配场景，目标时长: 4.57秒
2025-08-07 17:40:17,940 - INFO - 开始查找字幕序号 [50] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:17,940 - INFO - 找到related_overlap场景: scene_id=90, 字幕#50
2025-08-07 17:40:17,940 - INFO - 找到related_overlap场景: scene_id=91, 字幕#50
2025-08-07 17:40:17,941 - INFO - 字幕 #50 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:40:17,941 - INFO - 字幕序号 #50 找到 2 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:17,941 - INFO - 选择第一个overlap场景作为起点: scene_id=90
2025-08-07 17:40:17,941 - INFO - 添加起点场景: scene_id=90, 时长=1.52秒, 累计时长=1.52秒
2025-08-07 17:40:17,941 - INFO - 起点场景时长不足，需要延伸填充 3.05秒
2025-08-07 17:40:17,941 - INFO - 起点场景在原始列表中的索引: 89
2025-08-07 17:40:17,941 - INFO - 延伸添加场景: scene_id=91 (完整时长 1.24秒)
2025-08-07 17:40:17,941 - INFO - 累计时长: 2.76秒
2025-08-07 17:40:17,941 - INFO - 延伸添加场景: scene_id=92 (完整时长 1.20秒)
2025-08-07 17:40:17,941 - INFO - 累计时长: 3.96秒
2025-08-07 17:40:17,941 - INFO - 延伸添加场景: scene_id=93 (裁剪至 0.61秒)
2025-08-07 17:40:17,941 - INFO - 累计时长: 4.57秒
2025-08-07 17:40:17,941 - INFO - 字幕序号 #50 场景匹配完成，共选择 4 个场景，总时长: 4.57秒
2025-08-07 17:40:17,941 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-08-07 17:40:17,941 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-08-07 17:40:17,941 - INFO - 
--- 生成方案 #2：基于字幕序号 #56 ---
2025-08-07 17:40:17,941 - INFO - 开始为单个字幕序号 #56 匹配场景，目标时长: 4.57秒
2025-08-07 17:40:17,941 - INFO - 开始查找字幕序号 [56] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:17,942 - INFO - 找到related_overlap场景: scene_id=100, 字幕#56
2025-08-07 17:40:17,942 - INFO - 找到related_between场景: scene_id=99, 字幕#56
2025-08-07 17:40:17,942 - INFO - 字幕 #56 找到 1 个overlap场景, 1 个between场景
2025-08-07 17:40:17,942 - INFO - 字幕序号 #56 找到 1 个可用overlap场景, 1 个可用between场景
2025-08-07 17:40:17,942 - INFO - 选择第一个overlap场景作为起点: scene_id=100
2025-08-07 17:40:17,942 - INFO - 添加起点场景: scene_id=100, 时长=1.32秒, 累计时长=1.32秒
2025-08-07 17:40:17,942 - INFO - 起点场景时长不足，需要延伸填充 3.25秒
2025-08-07 17:40:17,942 - INFO - 起点场景在原始列表中的索引: 99
2025-08-07 17:40:17,942 - INFO - 延伸添加场景: scene_id=101 (完整时长 2.24秒)
2025-08-07 17:40:17,942 - INFO - 累计时长: 3.56秒
2025-08-07 17:40:17,942 - INFO - 延伸添加场景: scene_id=102 (裁剪至 1.01秒)
2025-08-07 17:40:17,942 - INFO - 累计时长: 4.57秒
2025-08-07 17:40:17,942 - INFO - 字幕序号 #56 场景匹配完成，共选择 3 个场景，总时长: 4.57秒
2025-08-07 17:40:17,942 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-08-07 17:40:17,942 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:17,942 - INFO - ========== 当前模式：为字幕 #4 生成 1 套场景方案 ==========
2025-08-07 17:40:17,942 - INFO - 开始查找字幕序号 [50, 56] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:17,942 - INFO - 找到related_overlap场景: scene_id=90, 字幕#50
2025-08-07 17:40:17,942 - INFO - 找到related_overlap场景: scene_id=91, 字幕#50
2025-08-07 17:40:17,942 - INFO - 找到related_overlap场景: scene_id=100, 字幕#56
2025-08-07 17:40:17,944 - INFO - 找到related_between场景: scene_id=99, 字幕#56
2025-08-07 17:40:17,944 - INFO - 字幕 #50 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:40:17,944 - INFO - 字幕 #56 找到 1 个overlap场景, 1 个between场景
2025-08-07 17:40:17,944 - INFO - 共收集 3 个未使用的overlap场景和 1 个未使用的between场景
2025-08-07 17:40:17,944 - INFO - 开始生成方案 #1
2025-08-07 17:40:17,944 - INFO - 方案 #1: 为字幕#50选择初始化overlap场景id=90
2025-08-07 17:40:17,944 - INFO - 方案 #1: 为字幕#56选择初始化overlap场景id=100
2025-08-07 17:40:17,944 - INFO - 方案 #1: 初始选择后，当前总时长=2.84秒
2025-08-07 17:40:17,944 - INFO - 方案 #1: 额外添加overlap场景id=91, 当前总时长=4.08秒
2025-08-07 17:40:17,944 - INFO - 方案 #1: 额外between选择后，当前总时长=4.08秒
2025-08-07 17:40:17,945 - INFO - 方案 #1: 额外添加between场景id=99, 当前总时长=4.92秒
2025-08-07 17:40:17,945 - INFO - 方案 #1: 场景总时长(4.92秒)大于音频时长(4.57秒)，需要裁剪
2025-08-07 17:40:17,945 - INFO - 调整前总时长: 4.92秒, 目标时长: 4.57秒
2025-08-07 17:40:17,945 - INFO - 需要裁剪 0.35秒
2025-08-07 17:40:17,945 - INFO - 裁剪最长场景ID=90：从1.52秒裁剪至1.17秒
2025-08-07 17:40:17,945 - INFO - 调整后总时长: 4.57秒，与目标时长差异: 0.00秒
2025-08-07 17:40:17,945 - INFO - 方案 #1 调整/填充后最终总时长: 4.57秒
2025-08-07 17:40:17,945 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:17,945 - INFO - ========== 当前模式：字幕 #4 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:17,945 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:17,945 - INFO - ========== 新模式：字幕 #4 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:17,945 - INFO - 
----- 处理字幕 #4 的方案 #1 -----
2025-08-07 17:40:17,945 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\4_1.mp4
2025-08-07 17:40:17,945 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpds55wg_w
2025-08-07 17:40:17,946 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\90.mp4 (确认存在: True)
2025-08-07 17:40:17,946 - INFO - 添加场景ID=90，时长=1.52秒，累计时长=1.52秒
2025-08-07 17:40:17,946 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\91.mp4 (确认存在: True)
2025-08-07 17:40:17,946 - INFO - 添加场景ID=91，时长=1.24秒，累计时长=2.76秒
2025-08-07 17:40:17,946 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\92.mp4 (确认存在: True)
2025-08-07 17:40:17,946 - INFO - 添加场景ID=92，时长=1.20秒，累计时长=3.96秒
2025-08-07 17:40:17,946 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\93.mp4 (确认存在: True)
2025-08-07 17:40:17,946 - INFO - 添加场景ID=93，时长=1.24秒，累计时长=5.20秒
2025-08-07 17:40:17,946 - INFO - 准备合并 4 个场景文件，总时长约 5.20秒
2025-08-07 17:40:17,946 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/90.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/91.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/92.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/93.mp4'

2025-08-07 17:40:17,946 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpds55wg_w\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpds55wg_w\temp_combined.mp4
2025-08-07 17:40:18,119 - INFO - 合并后的视频时长: 5.29秒，目标音频时长: 4.57秒
2025-08-07 17:40:18,119 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpds55wg_w\temp_combined.mp4 -ss 0 -to 4.572 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\4_1.mp4
2025-08-07 17:40:18,439 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:18,439 - INFO - 目标音频时长: 4.57秒
2025-08-07 17:40:18,439 - INFO - 实际视频时长: 4.62秒
2025-08-07 17:40:18,439 - INFO - 时长差异: 0.05秒 (1.12%)
2025-08-07 17:40:18,439 - INFO - ==========================================
2025-08-07 17:40:18,439 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:18,439 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\4_1.mp4
2025-08-07 17:40:18,440 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpds55wg_w
2025-08-07 17:40:18,487 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:18,487 - INFO -   - 音频时长: 4.57秒
2025-08-07 17:40:18,487 - INFO -   - 视频时长: 4.62秒
2025-08-07 17:40:18,487 - INFO -   - 时长差异: 0.05秒 (1.12%)
2025-08-07 17:40:18,487 - INFO - 
----- 处理字幕 #4 的方案 #2 -----
2025-08-07 17:40:18,487 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\4_2.mp4
2025-08-07 17:40:18,487 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6gy2vyy6
2025-08-07 17:40:18,488 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\100.mp4 (确认存在: True)
2025-08-07 17:40:18,488 - INFO - 添加场景ID=100，时长=1.32秒，累计时长=1.32秒
2025-08-07 17:40:18,488 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\101.mp4 (确认存在: True)
2025-08-07 17:40:18,488 - INFO - 添加场景ID=101，时长=2.24秒，累计时长=3.56秒
2025-08-07 17:40:18,488 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\102.mp4 (确认存在: True)
2025-08-07 17:40:18,488 - INFO - 添加场景ID=102，时长=3.64秒，累计时长=7.20秒
2025-08-07 17:40:18,488 - INFO - 场景总时长(7.20秒)已达到音频时长(4.57秒)的1.5倍，停止添加场景
2025-08-07 17:40:18,488 - INFO - 准备合并 3 个场景文件，总时长约 7.20秒
2025-08-07 17:40:18,488 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/100.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/101.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/102.mp4'

2025-08-07 17:40:18,488 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp6gy2vyy6\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp6gy2vyy6\temp_combined.mp4
2025-08-07 17:40:18,612 - INFO - 合并后的视频时长: 7.27秒，目标音频时长: 4.57秒
2025-08-07 17:40:18,612 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp6gy2vyy6\temp_combined.mp4 -ss 0 -to 4.572 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\4_2.mp4
2025-08-07 17:40:18,908 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:18,908 - INFO - 目标音频时长: 4.57秒
2025-08-07 17:40:18,908 - INFO - 实际视频时长: 4.62秒
2025-08-07 17:40:18,908 - INFO - 时长差异: 0.05秒 (1.12%)
2025-08-07 17:40:18,908 - INFO - ==========================================
2025-08-07 17:40:18,908 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:18,908 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\4_2.mp4
2025-08-07 17:40:18,909 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6gy2vyy6
2025-08-07 17:40:18,952 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:18,952 - INFO -   - 音频时长: 4.57秒
2025-08-07 17:40:18,952 - INFO -   - 视频时长: 4.62秒
2025-08-07 17:40:18,952 - INFO -   - 时长差异: 0.05秒 (1.12%)
2025-08-07 17:40:18,952 - INFO - 
----- 处理字幕 #4 的方案 #3 -----
2025-08-07 17:40:18,952 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\4_3.mp4
2025-08-07 17:40:18,953 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkmejxji8
2025-08-07 17:40:18,953 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\90.mp4 (确认存在: True)
2025-08-07 17:40:18,953 - INFO - 添加场景ID=90，时长=1.52秒，累计时长=1.52秒
2025-08-07 17:40:18,953 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\100.mp4 (确认存在: True)
2025-08-07 17:40:18,953 - INFO - 添加场景ID=100，时长=1.32秒，累计时长=2.84秒
2025-08-07 17:40:18,954 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\91.mp4 (确认存在: True)
2025-08-07 17:40:18,954 - INFO - 添加场景ID=91，时长=1.24秒，累计时长=4.08秒
2025-08-07 17:40:18,954 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\99.mp4 (确认存在: True)
2025-08-07 17:40:18,954 - INFO - 添加场景ID=99，时长=0.84秒，累计时长=4.92秒
2025-08-07 17:40:18,954 - INFO - 准备合并 4 个场景文件，总时长约 4.92秒
2025-08-07 17:40:18,954 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/90.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/100.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/91.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/99.mp4'

2025-08-07 17:40:18,954 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpkmejxji8\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpkmejxji8\temp_combined.mp4
2025-08-07 17:40:19,111 - INFO - 合并后的视频时长: 5.01秒，目标音频时长: 4.57秒
2025-08-07 17:40:19,111 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpkmejxji8\temp_combined.mp4 -ss 0 -to 4.572 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\4_3.mp4
2025-08-07 17:40:19,414 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:19,414 - INFO - 目标音频时长: 4.57秒
2025-08-07 17:40:19,414 - INFO - 实际视频时长: 4.62秒
2025-08-07 17:40:19,414 - INFO - 时长差异: 0.05秒 (1.12%)
2025-08-07 17:40:19,414 - INFO - ==========================================
2025-08-07 17:40:19,414 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:19,414 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\4_3.mp4
2025-08-07 17:40:19,415 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkmejxji8
2025-08-07 17:40:19,459 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:19,459 - INFO -   - 音频时长: 4.57秒
2025-08-07 17:40:19,459 - INFO -   - 视频时长: 4.62秒
2025-08-07 17:40:19,459 - INFO -   - 时长差异: 0.05秒 (1.12%)
2025-08-07 17:40:19,459 - INFO - 
字幕 #4 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:19,459 - INFO - 生成的视频文件:
2025-08-07 17:40:19,459 - INFO -   1. F:/github/aicut_auto/newcut_ai\4_1.mp4
2025-08-07 17:40:19,459 - INFO -   2. F:/github/aicut_auto/newcut_ai\4_2.mp4
2025-08-07 17:40:19,459 - INFO -   3. F:/github/aicut_auto/newcut_ai\4_3.mp4
2025-08-07 17:40:19,459 - INFO - ========== 字幕 #4 处理结束 ==========

