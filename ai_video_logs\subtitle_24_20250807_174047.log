2025-08-07 17:40:47,893 - INFO - ========== 字幕 #24 处理开始 ==========
2025-08-07 17:40:47,893 - INFO - 字幕内容: 爷爷力排众议，认为大孙子能力出众，正式提议由他继承家业。
2025-08-07 17:40:47,893 - INFO - 字幕序号: [670, 680]
2025-08-07 17:40:47,893 - INFO - 音频文件详情:
2025-08-07 17:40:47,893 - INFO -   - 路径: output\24.wav
2025-08-07 17:40:47,893 - INFO -   - 时长: 4.24秒
2025-08-07 17:40:47,893 - INFO -   - 验证音频时长: 4.24秒
2025-08-07 17:40:47,893 - INFO - 字幕时间戳信息:
2025-08-07 17:40:47,893 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:47,893 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:47,893 - INFO -   - 根据生成的音频时长(4.24秒)已调整字幕时间戳
2025-08-07 17:40:47,893 - INFO - ========== 新模式：为字幕 #24 生成4套场景方案 ==========
2025-08-07 17:40:47,893 - INFO - 字幕序号列表: [670, 680]
2025-08-07 17:40:47,894 - INFO - 
--- 生成方案 #1：基于字幕序号 #670 ---
2025-08-07 17:40:47,894 - INFO - 开始为单个字幕序号 #670 匹配场景，目标时长: 4.24秒
2025-08-07 17:40:47,894 - INFO - 开始查找字幕序号 [670] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:47,894 - INFO - 找到related_overlap场景: scene_id=816, 字幕#670
2025-08-07 17:40:47,894 - INFO - 找到related_overlap场景: scene_id=817, 字幕#670
2025-08-07 17:40:47,894 - INFO - 字幕 #670 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:40:47,894 - INFO - 字幕序号 #670 找到 2 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:47,894 - INFO - 选择第一个overlap场景作为起点: scene_id=816
2025-08-07 17:40:47,894 - INFO - 添加起点场景: scene_id=816, 时长=1.96秒, 累计时长=1.96秒
2025-08-07 17:40:47,894 - INFO - 起点场景时长不足，需要延伸填充 2.28秒
2025-08-07 17:40:47,896 - INFO - 起点场景在原始列表中的索引: 815
2025-08-07 17:40:47,896 - INFO - 延伸添加场景: scene_id=817 (完整时长 1.72秒)
2025-08-07 17:40:47,896 - INFO - 累计时长: 3.68秒
2025-08-07 17:40:47,896 - INFO - 延伸添加场景: scene_id=818 (裁剪至 0.56秒)
2025-08-07 17:40:47,896 - INFO - 累计时长: 4.24秒
2025-08-07 17:40:47,896 - INFO - 字幕序号 #670 场景匹配完成，共选择 3 个场景，总时长: 4.24秒
2025-08-07 17:40:47,896 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-08-07 17:40:47,896 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-08-07 17:40:47,896 - INFO - 
--- 生成方案 #2：基于字幕序号 #680 ---
2025-08-07 17:40:47,896 - INFO - 开始为单个字幕序号 #680 匹配场景，目标时长: 4.24秒
2025-08-07 17:40:47,896 - INFO - 开始查找字幕序号 [680] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:47,896 - INFO - 找到related_overlap场景: scene_id=825, 字幕#680
2025-08-07 17:40:47,896 - INFO - 找到related_overlap场景: scene_id=826, 字幕#680
2025-08-07 17:40:47,896 - INFO - 字幕 #680 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:40:47,896 - INFO - 字幕序号 #680 找到 2 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:47,896 - INFO - 选择第一个overlap场景作为起点: scene_id=825
2025-08-07 17:40:47,896 - INFO - 添加起点场景: scene_id=825, 时长=3.00秒, 累计时长=3.00秒
2025-08-07 17:40:47,896 - INFO - 起点场景时长不足，需要延伸填充 1.24秒
2025-08-07 17:40:47,896 - INFO - 起点场景在原始列表中的索引: 824
2025-08-07 17:40:47,896 - INFO - 延伸添加场景: scene_id=826 (裁剪至 1.24秒)
2025-08-07 17:40:47,896 - INFO - 累计时长: 4.24秒
2025-08-07 17:40:47,896 - INFO - 字幕序号 #680 场景匹配完成，共选择 2 个场景，总时长: 4.24秒
2025-08-07 17:40:47,896 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-08-07 17:40:47,896 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:47,896 - INFO - ========== 当前模式：为字幕 #24 生成 1 套场景方案 ==========
2025-08-07 17:40:47,896 - INFO - 开始查找字幕序号 [670, 680] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:47,898 - INFO - 找到related_overlap场景: scene_id=816, 字幕#670
2025-08-07 17:40:47,898 - INFO - 找到related_overlap场景: scene_id=817, 字幕#670
2025-08-07 17:40:47,898 - INFO - 找到related_overlap场景: scene_id=825, 字幕#680
2025-08-07 17:40:47,898 - INFO - 找到related_overlap场景: scene_id=826, 字幕#680
2025-08-07 17:40:47,898 - INFO - 字幕 #670 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:40:47,898 - INFO - 字幕 #680 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:40:47,898 - INFO - 共收集 4 个未使用的overlap场景和 0 个未使用的between场景
2025-08-07 17:40:47,898 - INFO - 开始生成方案 #1
2025-08-07 17:40:47,898 - INFO - 方案 #1: 为字幕#670选择初始化overlap场景id=817
2025-08-07 17:40:47,898 - INFO - 方案 #1: 为字幕#680选择初始化overlap场景id=825
2025-08-07 17:40:47,899 - INFO - 方案 #1: 初始选择后，当前总时长=4.72秒
2025-08-07 17:40:47,899 - INFO - 方案 #1: 额外between选择后，当前总时长=4.72秒
2025-08-07 17:40:47,899 - INFO - 方案 #1: 场景总时长(4.72秒)大于音频时长(4.24秒)，需要裁剪
2025-08-07 17:40:47,899 - INFO - 调整前总时长: 4.72秒, 目标时长: 4.24秒
2025-08-07 17:40:47,899 - INFO - 需要裁剪 0.48秒
2025-08-07 17:40:47,899 - INFO - 裁剪最长场景ID=825：从3.00秒裁剪至2.52秒
2025-08-07 17:40:47,899 - INFO - 调整后总时长: 4.24秒，与目标时长差异: 0.00秒
2025-08-07 17:40:47,899 - INFO - 方案 #1 调整/填充后最终总时长: 4.24秒
2025-08-07 17:40:47,899 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:47,899 - INFO - ========== 当前模式：字幕 #24 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:47,899 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:47,899 - INFO - ========== 新模式：字幕 #24 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:47,899 - INFO - 
----- 处理字幕 #24 的方案 #1 -----
2025-08-07 17:40:47,899 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\24_1.mp4
2025-08-07 17:40:47,899 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmprukt0zr6
2025-08-07 17:40:47,900 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\816.mp4 (确认存在: True)
2025-08-07 17:40:47,900 - INFO - 添加场景ID=816，时长=1.96秒，累计时长=1.96秒
2025-08-07 17:40:47,900 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\817.mp4 (确认存在: True)
2025-08-07 17:40:47,900 - INFO - 添加场景ID=817，时长=1.72秒，累计时长=3.68秒
2025-08-07 17:40:47,900 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\818.mp4 (确认存在: True)
2025-08-07 17:40:47,900 - INFO - 添加场景ID=818，时长=1.36秒，累计时长=5.04秒
2025-08-07 17:40:47,900 - INFO - 准备合并 3 个场景文件，总时长约 5.04秒
2025-08-07 17:40:47,900 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/816.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/817.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/818.mp4'

2025-08-07 17:40:47,900 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmprukt0zr6\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmprukt0zr6\temp_combined.mp4
2025-08-07 17:40:48,025 - INFO - 合并后的视频时长: 5.11秒，目标音频时长: 4.24秒
2025-08-07 17:40:48,025 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmprukt0zr6\temp_combined.mp4 -ss 0 -to 4.24 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\24_1.mp4
2025-08-07 17:40:48,328 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:48,328 - INFO - 目标音频时长: 4.24秒
2025-08-07 17:40:48,328 - INFO - 实际视频时长: 4.26秒
2025-08-07 17:40:48,328 - INFO - 时长差异: 0.02秒 (0.55%)
2025-08-07 17:40:48,328 - INFO - ==========================================
2025-08-07 17:40:48,328 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:48,328 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\24_1.mp4
2025-08-07 17:40:48,329 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmprukt0zr6
2025-08-07 17:40:48,373 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:48,373 - INFO -   - 音频时长: 4.24秒
2025-08-07 17:40:48,373 - INFO -   - 视频时长: 4.26秒
2025-08-07 17:40:48,373 - INFO -   - 时长差异: 0.02秒 (0.55%)
2025-08-07 17:40:48,373 - INFO - 
----- 处理字幕 #24 的方案 #2 -----
2025-08-07 17:40:48,373 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\24_2.mp4
2025-08-07 17:40:48,374 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqpu1rv6j
2025-08-07 17:40:48,374 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\825.mp4 (确认存在: True)
2025-08-07 17:40:48,374 - INFO - 添加场景ID=825，时长=3.00秒，累计时长=3.00秒
2025-08-07 17:40:48,374 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\826.mp4 (确认存在: True)
2025-08-07 17:40:48,374 - INFO - 添加场景ID=826，时长=1.32秒，累计时长=4.32秒
2025-08-07 17:40:48,374 - INFO - 准备合并 2 个场景文件，总时长约 4.32秒
2025-08-07 17:40:48,375 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/825.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/826.mp4'

2025-08-07 17:40:48,375 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpqpu1rv6j\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpqpu1rv6j\temp_combined.mp4
2025-08-07 17:40:48,496 - INFO - 合并后的视频时长: 4.37秒，目标音频时长: 4.24秒
2025-08-07 17:40:48,496 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpqpu1rv6j\temp_combined.mp4 -ss 0 -to 4.24 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\24_2.mp4
2025-08-07 17:40:48,776 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:48,778 - INFO - 目标音频时长: 4.24秒
2025-08-07 17:40:48,778 - INFO - 实际视频时长: 4.26秒
2025-08-07 17:40:48,778 - INFO - 时长差异: 0.02秒 (0.55%)
2025-08-07 17:40:48,778 - INFO - ==========================================
2025-08-07 17:40:48,778 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:48,778 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\24_2.mp4
2025-08-07 17:40:48,778 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqpu1rv6j
2025-08-07 17:40:48,821 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:48,821 - INFO -   - 音频时长: 4.24秒
2025-08-07 17:40:48,821 - INFO -   - 视频时长: 4.26秒
2025-08-07 17:40:48,821 - INFO -   - 时长差异: 0.02秒 (0.55%)
2025-08-07 17:40:48,821 - INFO - 
----- 处理字幕 #24 的方案 #3 -----
2025-08-07 17:40:48,821 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\24_3.mp4
2025-08-07 17:40:48,822 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqlw4isfa
2025-08-07 17:40:48,822 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\817.mp4 (确认存在: True)
2025-08-07 17:40:48,822 - INFO - 添加场景ID=817，时长=1.72秒，累计时长=1.72秒
2025-08-07 17:40:48,822 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\825.mp4 (确认存在: True)
2025-08-07 17:40:48,822 - INFO - 添加场景ID=825，时长=3.00秒，累计时长=4.72秒
2025-08-07 17:40:48,822 - INFO - 准备合并 2 个场景文件，总时长约 4.72秒
2025-08-07 17:40:48,822 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/817.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/825.mp4'

2025-08-07 17:40:48,822 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpqlw4isfa\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpqlw4isfa\temp_combined.mp4
2025-08-07 17:40:48,944 - INFO - 合并后的视频时长: 4.77秒，目标音频时长: 4.24秒
2025-08-07 17:40:48,944 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpqlw4isfa\temp_combined.mp4 -ss 0 -to 4.24 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\24_3.mp4
2025-08-07 17:40:49,224 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:49,224 - INFO - 目标音频时长: 4.24秒
2025-08-07 17:40:49,224 - INFO - 实际视频时长: 4.26秒
2025-08-07 17:40:49,224 - INFO - 时长差异: 0.02秒 (0.55%)
2025-08-07 17:40:49,224 - INFO - ==========================================
2025-08-07 17:40:49,224 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:49,224 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\24_3.mp4
2025-08-07 17:40:49,224 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqlw4isfa
2025-08-07 17:40:49,278 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:49,278 - INFO -   - 音频时长: 4.24秒
2025-08-07 17:40:49,278 - INFO -   - 视频时长: 4.26秒
2025-08-07 17:40:49,278 - INFO -   - 时长差异: 0.02秒 (0.55%)
2025-08-07 17:40:49,278 - INFO - 
字幕 #24 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:49,278 - INFO - 生成的视频文件:
2025-08-07 17:40:49,278 - INFO -   1. F:/github/aicut_auto/newcut_ai\24_1.mp4
2025-08-07 17:40:49,278 - INFO -   2. F:/github/aicut_auto/newcut_ai\24_2.mp4
2025-08-07 17:40:49,278 - INFO -   3. F:/github/aicut_auto/newcut_ai\24_3.mp4
2025-08-07 17:40:49,278 - INFO - ========== 字幕 #24 处理结束 ==========

