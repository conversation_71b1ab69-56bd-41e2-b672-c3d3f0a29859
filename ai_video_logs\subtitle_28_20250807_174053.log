2025-08-07 17:40:53,428 - INFO - ========== 字幕 #28 处理开始 ==========
2025-08-07 17:40:53,428 - INFO - 字幕内容: 做贼心虚的二弟惊慌失措，竟想抢走手机，阻止爷爷打电话。
2025-08-07 17:40:53,428 - INFO - 字幕序号: [799, 803]
2025-08-07 17:40:53,428 - INFO - 音频文件详情:
2025-08-07 17:40:53,428 - INFO -   - 路径: output\28.wav
2025-08-07 17:40:53,428 - INFO -   - 时长: 3.58秒
2025-08-07 17:40:53,429 - INFO -   - 验证音频时长: 3.58秒
2025-08-07 17:40:53,429 - INFO - 字幕时间戳信息:
2025-08-07 17:40:53,429 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:53,429 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:53,429 - INFO -   - 根据生成的音频时长(3.58秒)已调整字幕时间戳
2025-08-07 17:40:53,429 - INFO - ========== 新模式：为字幕 #28 生成4套场景方案 ==========
2025-08-07 17:40:53,429 - INFO - 字幕序号列表: [799, 803]
2025-08-07 17:40:53,429 - INFO - 
--- 生成方案 #1：基于字幕序号 #799 ---
2025-08-07 17:40:53,429 - INFO - 开始为单个字幕序号 #799 匹配场景，目标时长: 3.58秒
2025-08-07 17:40:53,429 - INFO - 开始查找字幕序号 [799] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:53,429 - INFO - 找到related_overlap场景: scene_id=974, 字幕#799
2025-08-07 17:40:53,430 - INFO - 找到related_between场景: scene_id=973, 字幕#799
2025-08-07 17:40:53,430 - INFO - 字幕 #799 找到 1 个overlap场景, 1 个between场景
2025-08-07 17:40:53,431 - INFO - 字幕序号 #799 找到 1 个可用overlap场景, 1 个可用between场景
2025-08-07 17:40:53,431 - INFO - 选择第一个overlap场景作为起点: scene_id=974
2025-08-07 17:40:53,431 - INFO - 添加起点场景: scene_id=974, 时长=4.68秒, 累计时长=4.68秒
2025-08-07 17:40:53,431 - INFO - 起点场景时长已满足要求，无需延伸
2025-08-07 17:40:53,431 - INFO - 方案 #1 生成成功，包含 1 个场景
2025-08-07 17:40:53,431 - INFO - 新模式：第1套方案的 1 个场景已加入全局已使用集合
2025-08-07 17:40:53,431 - INFO - 
--- 生成方案 #2：基于字幕序号 #803 ---
2025-08-07 17:40:53,431 - INFO - 开始为单个字幕序号 #803 匹配场景，目标时长: 3.58秒
2025-08-07 17:40:53,431 - INFO - 开始查找字幕序号 [803] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:53,431 - INFO - 找到related_overlap场景: scene_id=976, 字幕#803
2025-08-07 17:40:53,431 - INFO - 找到related_overlap场景: scene_id=977, 字幕#803
2025-08-07 17:40:53,432 - INFO - 字幕 #803 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:40:53,432 - INFO - 字幕序号 #803 找到 2 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:53,432 - INFO - 选择第一个overlap场景作为起点: scene_id=976
2025-08-07 17:40:53,432 - INFO - 添加起点场景: scene_id=976, 时长=0.84秒, 累计时长=0.84秒
2025-08-07 17:40:53,432 - INFO - 起点场景时长不足，需要延伸填充 2.75秒
2025-08-07 17:40:53,432 - INFO - 起点场景在原始列表中的索引: 975
2025-08-07 17:40:53,432 - INFO - 延伸添加场景: scene_id=977 (完整时长 1.28秒)
2025-08-07 17:40:53,432 - INFO - 累计时长: 2.12秒
2025-08-07 17:40:53,432 - INFO - 延伸添加场景: scene_id=978 (裁剪至 1.47秒)
2025-08-07 17:40:53,432 - INFO - 累计时长: 3.58秒
2025-08-07 17:40:53,432 - INFO - 字幕序号 #803 场景匹配完成，共选择 3 个场景，总时长: 3.58秒
2025-08-07 17:40:53,432 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-08-07 17:40:53,432 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:53,432 - INFO - ========== 当前模式：为字幕 #28 生成 1 套场景方案 ==========
2025-08-07 17:40:53,432 - INFO - 开始查找字幕序号 [799, 803] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:53,432 - INFO - 找到related_overlap场景: scene_id=974, 字幕#799
2025-08-07 17:40:53,432 - INFO - 找到related_overlap场景: scene_id=976, 字幕#803
2025-08-07 17:40:53,432 - INFO - 找到related_overlap场景: scene_id=977, 字幕#803
2025-08-07 17:40:53,433 - INFO - 找到related_between场景: scene_id=973, 字幕#799
2025-08-07 17:40:53,433 - INFO - 字幕 #799 找到 1 个overlap场景, 1 个between场景
2025-08-07 17:40:53,433 - INFO - 字幕 #803 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:40:53,433 - INFO - 共收集 3 个未使用的overlap场景和 1 个未使用的between场景
2025-08-07 17:40:53,433 - INFO - 开始生成方案 #1
2025-08-07 17:40:53,433 - INFO - 方案 #1: 为字幕#799选择初始化overlap场景id=974
2025-08-07 17:40:53,433 - INFO - 方案 #1: 为字幕#803选择初始化overlap场景id=977
2025-08-07 17:40:53,433 - INFO - 方案 #1: 初始选择后，当前总时长=5.96秒
2025-08-07 17:40:53,433 - INFO - 方案 #1: 额外between选择后，当前总时长=5.96秒
2025-08-07 17:40:53,433 - INFO - 方案 #1: 场景总时长(5.96秒)大于音频时长(3.58秒)，需要裁剪
2025-08-07 17:40:53,433 - INFO - 调整前总时长: 5.96秒, 目标时长: 3.58秒
2025-08-07 17:40:53,433 - INFO - 需要裁剪 2.37秒
2025-08-07 17:40:53,433 - INFO - 裁剪最长场景ID=974：从4.68秒裁剪至2.30秒
2025-08-07 17:40:53,433 - INFO - 调整后总时长: 3.58秒，与目标时长差异: 0.00秒
2025-08-07 17:40:53,433 - INFO - 方案 #1 调整/填充后最终总时长: 3.58秒
2025-08-07 17:40:53,433 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:53,433 - INFO - ========== 当前模式：字幕 #28 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:53,433 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:53,433 - INFO - ========== 新模式：字幕 #28 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:53,433 - INFO - 
----- 处理字幕 #28 的方案 #1 -----
2025-08-07 17:40:53,433 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\28_1.mp4
2025-08-07 17:40:53,434 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpl81gn87e
2025-08-07 17:40:53,434 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\974.mp4 (确认存在: True)
2025-08-07 17:40:53,434 - INFO - 添加场景ID=974，时长=4.68秒，累计时长=4.68秒
2025-08-07 17:40:53,434 - INFO - 准备合并 1 个场景文件，总时长约 4.68秒
2025-08-07 17:40:53,434 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/974.mp4'

2025-08-07 17:40:53,435 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpl81gn87e\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpl81gn87e\temp_combined.mp4
2025-08-07 17:40:53,551 - INFO - 合并后的视频时长: 4.70秒，目标音频时长: 3.58秒
2025-08-07 17:40:53,551 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpl81gn87e\temp_combined.mp4 -ss 0 -to 3.585 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\28_1.mp4
2025-08-07 17:40:53,805 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:53,805 - INFO - 目标音频时长: 3.58秒
2025-08-07 17:40:53,805 - INFO - 实际视频时长: 3.62秒
2025-08-07 17:40:53,805 - INFO - 时长差异: 0.04秒 (1.06%)
2025-08-07 17:40:53,805 - INFO - ==========================================
2025-08-07 17:40:53,805 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:53,805 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\28_1.mp4
2025-08-07 17:40:53,805 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpl81gn87e
2025-08-07 17:40:53,848 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:53,848 - INFO -   - 音频时长: 3.58秒
2025-08-07 17:40:53,848 - INFO -   - 视频时长: 3.62秒
2025-08-07 17:40:53,848 - INFO -   - 时长差异: 0.04秒 (1.06%)
2025-08-07 17:40:53,849 - INFO - 
----- 处理字幕 #28 的方案 #2 -----
2025-08-07 17:40:53,849 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\28_2.mp4
2025-08-07 17:40:53,849 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpp5x5fjxk
2025-08-07 17:40:53,849 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\976.mp4 (确认存在: True)
2025-08-07 17:40:53,849 - INFO - 添加场景ID=976，时长=0.84秒，累计时长=0.84秒
2025-08-07 17:40:53,850 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\977.mp4 (确认存在: True)
2025-08-07 17:40:53,850 - INFO - 添加场景ID=977，时长=1.28秒，累计时长=2.12秒
2025-08-07 17:40:53,850 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\978.mp4 (确认存在: True)
2025-08-07 17:40:53,850 - INFO - 添加场景ID=978，时长=2.84秒，累计时长=4.96秒
2025-08-07 17:40:53,850 - INFO - 准备合并 3 个场景文件，总时长约 4.96秒
2025-08-07 17:40:53,850 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/976.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/977.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/978.mp4'

2025-08-07 17:40:53,850 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpp5x5fjxk\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpp5x5fjxk\temp_combined.mp4
2025-08-07 17:40:53,993 - INFO - 合并后的视频时长: 5.03秒，目标音频时长: 3.58秒
2025-08-07 17:40:53,993 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpp5x5fjxk\temp_combined.mp4 -ss 0 -to 3.585 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\28_2.mp4
2025-08-07 17:40:54,264 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:54,264 - INFO - 目标音频时长: 3.58秒
2025-08-07 17:40:54,264 - INFO - 实际视频时长: 3.62秒
2025-08-07 17:40:54,264 - INFO - 时长差异: 0.04秒 (1.06%)
2025-08-07 17:40:54,264 - INFO - ==========================================
2025-08-07 17:40:54,264 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:54,264 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\28_2.mp4
2025-08-07 17:40:54,266 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpp5x5fjxk
2025-08-07 17:40:54,309 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:54,309 - INFO -   - 音频时长: 3.58秒
2025-08-07 17:40:54,309 - INFO -   - 视频时长: 3.62秒
2025-08-07 17:40:54,309 - INFO -   - 时长差异: 0.04秒 (1.06%)
2025-08-07 17:40:54,309 - INFO - 
----- 处理字幕 #28 的方案 #3 -----
2025-08-07 17:40:54,309 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\28_3.mp4
2025-08-07 17:40:54,309 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpu3ubby0o
2025-08-07 17:40:54,310 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\974.mp4 (确认存在: True)
2025-08-07 17:40:54,310 - INFO - 添加场景ID=974，时长=4.68秒，累计时长=4.68秒
2025-08-07 17:40:54,310 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\977.mp4 (确认存在: True)
2025-08-07 17:40:54,310 - INFO - 添加场景ID=977，时长=1.28秒，累计时长=5.96秒
2025-08-07 17:40:54,310 - INFO - 场景总时长(5.96秒)已达到音频时长(3.58秒)的1.5倍，停止添加场景
2025-08-07 17:40:54,310 - INFO - 准备合并 2 个场景文件，总时长约 5.96秒
2025-08-07 17:40:54,310 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/974.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/977.mp4'

2025-08-07 17:40:54,310 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpu3ubby0o\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpu3ubby0o\temp_combined.mp4
2025-08-07 17:40:54,428 - INFO - 合并后的视频时长: 6.01秒，目标音频时长: 3.58秒
2025-08-07 17:40:54,428 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpu3ubby0o\temp_combined.mp4 -ss 0 -to 3.585 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\28_3.mp4
2025-08-07 17:40:54,689 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:54,689 - INFO - 目标音频时长: 3.58秒
2025-08-07 17:40:54,689 - INFO - 实际视频时长: 3.62秒
2025-08-07 17:40:54,689 - INFO - 时长差异: 0.04秒 (1.06%)
2025-08-07 17:40:54,689 - INFO - ==========================================
2025-08-07 17:40:54,689 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:54,689 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\28_3.mp4
2025-08-07 17:40:54,689 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpu3ubby0o
2025-08-07 17:40:54,735 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:54,735 - INFO -   - 音频时长: 3.58秒
2025-08-07 17:40:54,735 - INFO -   - 视频时长: 3.62秒
2025-08-07 17:40:54,735 - INFO -   - 时长差异: 0.04秒 (1.06%)
2025-08-07 17:40:54,735 - INFO - 
字幕 #28 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:54,735 - INFO - 生成的视频文件:
2025-08-07 17:40:54,735 - INFO -   1. F:/github/aicut_auto/newcut_ai\28_1.mp4
2025-08-07 17:40:54,735 - INFO -   2. F:/github/aicut_auto/newcut_ai\28_2.mp4
2025-08-07 17:40:54,735 - INFO -   3. F:/github/aicut_auto/newcut_ai\28_3.mp4
2025-08-07 17:40:54,735 - INFO - ========== 字幕 #28 处理结束 ==========

