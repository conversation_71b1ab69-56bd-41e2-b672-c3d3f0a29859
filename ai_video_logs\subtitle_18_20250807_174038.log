2025-08-07 17:40:38,877 - INFO - ========== 字幕 #18 处理开始 ==========
2025-08-07 17:40:38,877 - INFO - 字幕内容: 女孩本想施法教训，谁知关键时刻灵力耗尽，法术失效，陷入了尴尬。
2025-08-07 17:40:38,877 - INFO - 字幕序号: [473, 475]
2025-08-07 17:40:38,877 - INFO - 音频文件详情:
2025-08-07 17:40:38,877 - INFO -   - 路径: output\18.wav
2025-08-07 17:40:38,877 - INFO -   - 时长: 4.27秒
2025-08-07 17:40:38,877 - INFO -   - 验证音频时长: 4.27秒
2025-08-07 17:40:38,878 - INFO - 字幕时间戳信息:
2025-08-07 17:40:38,878 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:38,878 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:38,878 - INFO -   - 根据生成的音频时长(4.27秒)已调整字幕时间戳
2025-08-07 17:40:38,878 - INFO - ========== 新模式：为字幕 #18 生成4套场景方案 ==========
2025-08-07 17:40:38,878 - INFO - 字幕序号列表: [473, 475]
2025-08-07 17:40:38,878 - INFO - 
--- 生成方案 #1：基于字幕序号 #473 ---
2025-08-07 17:40:38,878 - INFO - 开始为单个字幕序号 #473 匹配场景，目标时长: 4.27秒
2025-08-07 17:40:38,878 - INFO - 开始查找字幕序号 [473] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:38,878 - INFO - 找到related_overlap场景: scene_id=638, 字幕#473
2025-08-07 17:40:38,878 - INFO - 找到related_overlap场景: scene_id=639, 字幕#473
2025-08-07 17:40:38,879 - INFO - 找到related_between场景: scene_id=636, 字幕#473
2025-08-07 17:40:38,879 - INFO - 找到related_between场景: scene_id=637, 字幕#473
2025-08-07 17:40:38,879 - INFO - 找到related_between场景: scene_id=640, 字幕#473
2025-08-07 17:40:38,879 - INFO - 找到related_between场景: scene_id=641, 字幕#473
2025-08-07 17:40:38,880 - INFO - 字幕 #473 找到 2 个overlap场景, 4 个between场景
2025-08-07 17:40:38,880 - INFO - 字幕序号 #473 找到 2 个可用overlap场景, 4 个可用between场景
2025-08-07 17:40:38,880 - INFO - 选择第一个overlap场景作为起点: scene_id=638
2025-08-07 17:40:38,880 - INFO - 添加起点场景: scene_id=638, 时长=1.16秒, 累计时长=1.16秒
2025-08-07 17:40:38,880 - INFO - 起点场景时长不足，需要延伸填充 3.11秒
2025-08-07 17:40:38,880 - INFO - 起点场景在原始列表中的索引: 637
2025-08-07 17:40:38,880 - INFO - 延伸添加场景: scene_id=639 (完整时长 1.08秒)
2025-08-07 17:40:38,880 - INFO - 累计时长: 2.24秒
2025-08-07 17:40:38,880 - INFO - 延伸添加场景: scene_id=640 (完整时长 0.92秒)
2025-08-07 17:40:38,880 - INFO - 累计时长: 3.16秒
2025-08-07 17:40:38,880 - INFO - 延伸添加场景: scene_id=641 (裁剪至 1.11秒)
2025-08-07 17:40:38,880 - INFO - 累计时长: 4.27秒
2025-08-07 17:40:38,880 - INFO - 字幕序号 #473 场景匹配完成，共选择 4 个场景，总时长: 4.27秒
2025-08-07 17:40:38,880 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-08-07 17:40:38,880 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-08-07 17:40:38,880 - INFO - 
--- 生成方案 #2：基于字幕序号 #475 ---
2025-08-07 17:40:38,880 - INFO - 开始为单个字幕序号 #475 匹配场景，目标时长: 4.27秒
2025-08-07 17:40:38,880 - INFO - 开始查找字幕序号 [475] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:38,880 - INFO - 找到related_overlap场景: scene_id=642, 字幕#475
2025-08-07 17:40:38,881 - INFO - 字幕 #475 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:38,881 - INFO - 字幕序号 #475 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:38,881 - INFO - 选择第一个overlap场景作为起点: scene_id=642
2025-08-07 17:40:38,881 - INFO - 添加起点场景: scene_id=642, 时长=3.00秒, 累计时长=3.00秒
2025-08-07 17:40:38,881 - INFO - 起点场景时长不足，需要延伸填充 1.27秒
2025-08-07 17:40:38,882 - INFO - 起点场景在原始列表中的索引: 641
2025-08-07 17:40:38,882 - INFO - 延伸添加场景: scene_id=643 (裁剪至 1.27秒)
2025-08-07 17:40:38,882 - INFO - 累计时长: 4.27秒
2025-08-07 17:40:38,882 - INFO - 字幕序号 #475 场景匹配完成，共选择 2 个场景，总时长: 4.27秒
2025-08-07 17:40:38,882 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-08-07 17:40:38,882 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:38,882 - INFO - ========== 当前模式：为字幕 #18 生成 1 套场景方案 ==========
2025-08-07 17:40:38,882 - INFO - 开始查找字幕序号 [473, 475] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:38,882 - INFO - 找到related_overlap场景: scene_id=638, 字幕#473
2025-08-07 17:40:38,882 - INFO - 找到related_overlap场景: scene_id=639, 字幕#473
2025-08-07 17:40:38,882 - INFO - 找到related_overlap场景: scene_id=642, 字幕#475
2025-08-07 17:40:38,882 - INFO - 找到related_between场景: scene_id=636, 字幕#473
2025-08-07 17:40:38,882 - INFO - 找到related_between场景: scene_id=637, 字幕#473
2025-08-07 17:40:38,882 - INFO - 找到related_between场景: scene_id=640, 字幕#473
2025-08-07 17:40:38,882 - INFO - 找到related_between场景: scene_id=641, 字幕#473
2025-08-07 17:40:38,883 - INFO - 字幕 #473 找到 2 个overlap场景, 4 个between场景
2025-08-07 17:40:38,883 - INFO - 字幕 #475 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:38,883 - INFO - 共收集 3 个未使用的overlap场景和 4 个未使用的between场景
2025-08-07 17:40:38,883 - INFO - 开始生成方案 #1
2025-08-07 17:40:38,883 - INFO - 方案 #1: 为字幕#473选择初始化overlap场景id=639
2025-08-07 17:40:38,883 - INFO - 方案 #1: 为字幕#475选择初始化overlap场景id=642
2025-08-07 17:40:38,883 - INFO - 方案 #1: 初始选择后，当前总时长=4.08秒
2025-08-07 17:40:38,883 - INFO - 方案 #1: 额外添加overlap场景id=638, 当前总时长=5.24秒
2025-08-07 17:40:38,883 - INFO - 方案 #1: 额外between选择后，当前总时长=5.24秒
2025-08-07 17:40:38,883 - INFO - 方案 #1: 场景总时长(5.24秒)大于音频时长(4.27秒)，需要裁剪
2025-08-07 17:40:38,883 - INFO - 调整前总时长: 5.24秒, 目标时长: 4.27秒
2025-08-07 17:40:38,883 - INFO - 需要裁剪 0.97秒
2025-08-07 17:40:38,883 - INFO - 裁剪最长场景ID=642：从3.00秒裁剪至2.03秒
2025-08-07 17:40:38,883 - INFO - 调整后总时长: 4.27秒，与目标时长差异: 0.00秒
2025-08-07 17:40:38,883 - INFO - 方案 #1 调整/填充后最终总时长: 4.27秒
2025-08-07 17:40:38,883 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:38,883 - INFO - ========== 当前模式：字幕 #18 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:38,883 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:38,883 - INFO - ========== 新模式：字幕 #18 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:38,883 - INFO - 
----- 处理字幕 #18 的方案 #1 -----
2025-08-07 17:40:38,883 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\18_1.mp4
2025-08-07 17:40:38,884 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpegm4r0w3
2025-08-07 17:40:38,884 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\638.mp4 (确认存在: True)
2025-08-07 17:40:38,884 - INFO - 添加场景ID=638，时长=1.16秒，累计时长=1.16秒
2025-08-07 17:40:38,884 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\639.mp4 (确认存在: True)
2025-08-07 17:40:38,884 - INFO - 添加场景ID=639，时长=1.08秒，累计时长=2.24秒
2025-08-07 17:40:38,884 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\640.mp4 (确认存在: True)
2025-08-07 17:40:38,884 - INFO - 添加场景ID=640，时长=0.92秒，累计时长=3.16秒
2025-08-07 17:40:38,885 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\641.mp4 (确认存在: True)
2025-08-07 17:40:38,885 - INFO - 添加场景ID=641，时长=1.32秒，累计时长=4.48秒
2025-08-07 17:40:38,885 - INFO - 准备合并 4 个场景文件，总时长约 4.48秒
2025-08-07 17:40:38,885 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/638.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/639.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/640.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/641.mp4'

2025-08-07 17:40:38,885 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpegm4r0w3\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpegm4r0w3\temp_combined.mp4
2025-08-07 17:40:39,033 - INFO - 合并后的视频时长: 4.57秒，目标音频时长: 4.27秒
2025-08-07 17:40:39,033 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpegm4r0w3\temp_combined.mp4 -ss 0 -to 4.271 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\18_1.mp4
2025-08-07 17:40:39,351 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:39,351 - INFO - 目标音频时长: 4.27秒
2025-08-07 17:40:39,351 - INFO - 实际视频时长: 4.30秒
2025-08-07 17:40:39,351 - INFO - 时长差异: 0.03秒 (0.75%)
2025-08-07 17:40:39,351 - INFO - ==========================================
2025-08-07 17:40:39,351 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:39,351 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\18_1.mp4
2025-08-07 17:40:39,352 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpegm4r0w3
2025-08-07 17:40:39,394 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:39,394 - INFO -   - 音频时长: 4.27秒
2025-08-07 17:40:39,394 - INFO -   - 视频时长: 4.30秒
2025-08-07 17:40:39,394 - INFO -   - 时长差异: 0.03秒 (0.75%)
2025-08-07 17:40:39,394 - INFO - 
----- 处理字幕 #18 的方案 #2 -----
2025-08-07 17:40:39,394 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\18_2.mp4
2025-08-07 17:40:39,395 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpiv9grdsn
2025-08-07 17:40:39,395 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\642.mp4 (确认存在: True)
2025-08-07 17:40:39,395 - INFO - 添加场景ID=642，时长=3.00秒，累计时长=3.00秒
2025-08-07 17:40:39,395 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\643.mp4 (确认存在: True)
2025-08-07 17:40:39,395 - INFO - 添加场景ID=643，时长=1.56秒，累计时长=4.56秒
2025-08-07 17:40:39,395 - INFO - 准备合并 2 个场景文件，总时长约 4.56秒
2025-08-07 17:40:39,395 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/642.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/643.mp4'

2025-08-07 17:40:39,395 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpiv9grdsn\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpiv9grdsn\temp_combined.mp4
2025-08-07 17:40:39,527 - INFO - 合并后的视频时长: 4.61秒，目标音频时长: 4.27秒
2025-08-07 17:40:39,527 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpiv9grdsn\temp_combined.mp4 -ss 0 -to 4.271 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\18_2.mp4
2025-08-07 17:40:39,802 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:39,803 - INFO - 目标音频时长: 4.27秒
2025-08-07 17:40:39,803 - INFO - 实际视频时长: 4.30秒
2025-08-07 17:40:39,803 - INFO - 时长差异: 0.03秒 (0.75%)
2025-08-07 17:40:39,803 - INFO - ==========================================
2025-08-07 17:40:39,803 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:39,803 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\18_2.mp4
2025-08-07 17:40:39,803 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpiv9grdsn
2025-08-07 17:40:39,848 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:39,848 - INFO -   - 音频时长: 4.27秒
2025-08-07 17:40:39,848 - INFO -   - 视频时长: 4.30秒
2025-08-07 17:40:39,848 - INFO -   - 时长差异: 0.03秒 (0.75%)
2025-08-07 17:40:39,848 - INFO - 
----- 处理字幕 #18 的方案 #3 -----
2025-08-07 17:40:39,848 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\18_3.mp4
2025-08-07 17:40:39,848 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsj_r8l57
2025-08-07 17:40:39,848 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\639.mp4 (确认存在: True)
2025-08-07 17:40:39,849 - INFO - 添加场景ID=639，时长=1.08秒，累计时长=1.08秒
2025-08-07 17:40:39,849 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\642.mp4 (确认存在: True)
2025-08-07 17:40:39,849 - INFO - 添加场景ID=642，时长=3.00秒，累计时长=4.08秒
2025-08-07 17:40:39,849 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\638.mp4 (确认存在: True)
2025-08-07 17:40:39,849 - INFO - 添加场景ID=638，时长=1.16秒，累计时长=5.24秒
2025-08-07 17:40:39,849 - INFO - 准备合并 3 个场景文件，总时长约 5.24秒
2025-08-07 17:40:39,849 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/639.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/642.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/638.mp4'

2025-08-07 17:40:39,849 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpsj_r8l57\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpsj_r8l57\temp_combined.mp4
2025-08-07 17:40:39,978 - INFO - 合并后的视频时长: 5.31秒，目标音频时长: 4.27秒
2025-08-07 17:40:39,978 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpsj_r8l57\temp_combined.mp4 -ss 0 -to 4.271 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\18_3.mp4
2025-08-07 17:40:40,274 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:40,274 - INFO - 目标音频时长: 4.27秒
2025-08-07 17:40:40,274 - INFO - 实际视频时长: 4.30秒
2025-08-07 17:40:40,274 - INFO - 时长差异: 0.03秒 (0.75%)
2025-08-07 17:40:40,274 - INFO - ==========================================
2025-08-07 17:40:40,274 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:40,274 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\18_3.mp4
2025-08-07 17:40:40,275 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsj_r8l57
2025-08-07 17:40:40,329 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:40,329 - INFO -   - 音频时长: 4.27秒
2025-08-07 17:40:40,329 - INFO -   - 视频时长: 4.30秒
2025-08-07 17:40:40,329 - INFO -   - 时长差异: 0.03秒 (0.75%)
2025-08-07 17:40:40,329 - INFO - 
字幕 #18 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:40,329 - INFO - 生成的视频文件:
2025-08-07 17:40:40,329 - INFO -   1. F:/github/aicut_auto/newcut_ai\18_1.mp4
2025-08-07 17:40:40,329 - INFO -   2. F:/github/aicut_auto/newcut_ai\18_2.mp4
2025-08-07 17:40:40,329 - INFO -   3. F:/github/aicut_auto/newcut_ai\18_3.mp4
2025-08-07 17:40:40,329 - INFO - ========== 字幕 #18 处理结束 ==========

