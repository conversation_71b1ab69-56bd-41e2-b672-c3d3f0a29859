2025-08-07 17:40:56,040 - INFO - ========== 字幕 #30 处理开始 ==========
2025-08-07 17:40:56,040 - INFO - 字幕内容: 在女孩的威逼下，二弟终于当众承认，血书是自己伪造的，这场闹剧才得以收场。
2025-08-07 17:40:56,040 - INFO - 字幕序号: [825, 862]
2025-08-07 17:40:56,040 - INFO - 音频文件详情:
2025-08-07 17:40:56,040 - INFO -   - 路径: output\30.wav
2025-08-07 17:40:56,040 - INFO -   - 时长: 4.29秒
2025-08-07 17:40:56,040 - INFO -   - 验证音频时长: 4.29秒
2025-08-07 17:40:56,040 - INFO - 字幕时间戳信息:
2025-08-07 17:40:56,041 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:56,041 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:56,041 - INFO -   - 根据生成的音频时长(4.29秒)已调整字幕时间戳
2025-08-07 17:40:56,041 - INFO - ========== 新模式：为字幕 #30 生成4套场景方案 ==========
2025-08-07 17:40:56,041 - INFO - 字幕序号列表: [825, 862]
2025-08-07 17:40:56,041 - INFO - 
--- 生成方案 #1：基于字幕序号 #825 ---
2025-08-07 17:40:56,041 - INFO - 开始为单个字幕序号 #825 匹配场景，目标时长: 4.29秒
2025-08-07 17:40:56,041 - INFO - 开始查找字幕序号 [825] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:56,041 - INFO - 找到related_overlap场景: scene_id=1012, 字幕#825
2025-08-07 17:40:56,042 - INFO - 字幕 #825 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:56,042 - INFO - 字幕序号 #825 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:56,042 - INFO - 选择第一个overlap场景作为起点: scene_id=1012
2025-08-07 17:40:56,042 - INFO - 添加起点场景: scene_id=1012, 时长=1.92秒, 累计时长=1.92秒
2025-08-07 17:40:56,042 - INFO - 起点场景时长不足，需要延伸填充 2.37秒
2025-08-07 17:40:56,042 - INFO - 起点场景在原始列表中的索引: 1011
2025-08-07 17:40:56,042 - INFO - 延伸添加场景: scene_id=1013 (完整时长 1.64秒)
2025-08-07 17:40:56,042 - INFO - 累计时长: 3.56秒
2025-08-07 17:40:56,042 - INFO - 延伸添加场景: scene_id=1014 (裁剪至 0.73秒)
2025-08-07 17:40:56,042 - INFO - 累计时长: 4.29秒
2025-08-07 17:40:56,042 - INFO - 字幕序号 #825 场景匹配完成，共选择 3 个场景，总时长: 4.29秒
2025-08-07 17:40:56,042 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-08-07 17:40:56,042 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-08-07 17:40:56,042 - INFO - 
--- 生成方案 #2：基于字幕序号 #862 ---
2025-08-07 17:40:56,042 - INFO - 开始为单个字幕序号 #862 匹配场景，目标时长: 4.29秒
2025-08-07 17:40:56,043 - INFO - 开始查找字幕序号 [862] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:56,043 - INFO - 找到related_overlap场景: scene_id=1049, 字幕#862
2025-08-07 17:40:56,044 - INFO - 字幕 #862 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:56,044 - INFO - 字幕序号 #862 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:56,044 - INFO - 选择第一个overlap场景作为起点: scene_id=1049
2025-08-07 17:40:56,044 - INFO - 添加起点场景: scene_id=1049, 时长=1.52秒, 累计时长=1.52秒
2025-08-07 17:40:56,044 - INFO - 起点场景时长不足，需要延伸填充 2.77秒
2025-08-07 17:40:56,044 - INFO - 起点场景在原始列表中的索引: 1048
2025-08-07 17:40:56,044 - INFO - 延伸添加场景: scene_id=1050 (完整时长 1.04秒)
2025-08-07 17:40:56,044 - INFO - 累计时长: 2.56秒
2025-08-07 17:40:56,044 - INFO - 延伸添加场景: scene_id=1051 (完整时长 0.72秒)
2025-08-07 17:40:56,044 - INFO - 累计时长: 3.28秒
2025-08-07 17:40:56,044 - INFO - 延伸添加场景: scene_id=1052 (裁剪至 1.01秒)
2025-08-07 17:40:56,044 - INFO - 累计时长: 4.29秒
2025-08-07 17:40:56,044 - INFO - 字幕序号 #862 场景匹配完成，共选择 4 个场景，总时长: 4.29秒
2025-08-07 17:40:56,044 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-08-07 17:40:56,044 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:56,044 - INFO - ========== 当前模式：为字幕 #30 生成 1 套场景方案 ==========
2025-08-07 17:40:56,044 - INFO - 开始查找字幕序号 [825, 862] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:56,045 - INFO - 找到related_overlap场景: scene_id=1012, 字幕#825
2025-08-07 17:40:56,045 - INFO - 找到related_overlap场景: scene_id=1049, 字幕#862
2025-08-07 17:40:56,045 - INFO - 字幕 #825 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:56,045 - INFO - 字幕 #862 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:56,045 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-08-07 17:40:56,045 - INFO - 开始生成方案 #1
2025-08-07 17:40:56,045 - INFO - 方案 #1: 为字幕#825选择初始化overlap场景id=1012
2025-08-07 17:40:56,045 - INFO - 方案 #1: 为字幕#862选择初始化overlap场景id=1049
2025-08-07 17:40:56,045 - INFO - 方案 #1: 初始选择后，当前总时长=3.44秒
2025-08-07 17:40:56,045 - INFO - 方案 #1: 额外between选择后，当前总时长=3.44秒
2025-08-07 17:40:56,045 - INFO - 方案 #1: 场景总时长(3.44秒)小于音频时长(4.29秒)，需要延伸填充
2025-08-07 17:40:56,045 - INFO - 方案 #1: 最后一个场景ID: 1049
2025-08-07 17:40:56,045 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 1048
2025-08-07 17:40:56,045 - INFO - 方案 #1: 需要填充时长: 0.85秒
2025-08-07 17:40:56,045 - INFO - 方案 #1: 追加场景 scene_id=1050 (裁剪至 0.85秒)
2025-08-07 17:40:56,045 - INFO - 方案 #1: 成功填充至目标时长
2025-08-07 17:40:56,045 - INFO - 方案 #1 调整/填充后最终总时长: 4.29秒
2025-08-07 17:40:56,045 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:56,045 - INFO - ========== 当前模式：字幕 #30 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:56,045 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:56,045 - INFO - ========== 新模式：字幕 #30 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:56,045 - INFO - 
----- 处理字幕 #30 的方案 #1 -----
2025-08-07 17:40:56,045 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\30_1.mp4
2025-08-07 17:40:56,046 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1o2s2rpg
2025-08-07 17:40:56,046 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1012.mp4 (确认存在: True)
2025-08-07 17:40:56,046 - INFO - 添加场景ID=1012，时长=1.92秒，累计时长=1.92秒
2025-08-07 17:40:56,046 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1013.mp4 (确认存在: True)
2025-08-07 17:40:56,046 - INFO - 添加场景ID=1013，时长=1.64秒，累计时长=3.56秒
2025-08-07 17:40:56,046 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1014.mp4 (确认存在: True)
2025-08-07 17:40:56,046 - INFO - 添加场景ID=1014，时长=1.36秒，累计时长=4.92秒
2025-08-07 17:40:56,046 - INFO - 准备合并 3 个场景文件，总时长约 4.92秒
2025-08-07 17:40:56,047 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1012.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1013.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1014.mp4'

2025-08-07 17:40:56,047 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp1o2s2rpg\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp1o2s2rpg\temp_combined.mp4
2025-08-07 17:40:56,210 - INFO - 合并后的视频时长: 4.99秒，目标音频时长: 4.29秒
2025-08-07 17:40:56,210 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp1o2s2rpg\temp_combined.mp4 -ss 0 -to 4.286 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\30_1.mp4
2025-08-07 17:40:56,530 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:56,531 - INFO - 目标音频时长: 4.29秒
2025-08-07 17:40:56,531 - INFO - 实际视频时长: 4.34秒
2025-08-07 17:40:56,531 - INFO - 时长差异: 0.06秒 (1.33%)
2025-08-07 17:40:56,531 - INFO - ==========================================
2025-08-07 17:40:56,531 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:56,531 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\30_1.mp4
2025-08-07 17:40:56,531 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1o2s2rpg
2025-08-07 17:40:56,574 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:56,574 - INFO -   - 音频时长: 4.29秒
2025-08-07 17:40:56,574 - INFO -   - 视频时长: 4.34秒
2025-08-07 17:40:56,574 - INFO -   - 时长差异: 0.06秒 (1.33%)
2025-08-07 17:40:56,575 - INFO - 
----- 处理字幕 #30 的方案 #2 -----
2025-08-07 17:40:56,575 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\30_2.mp4
2025-08-07 17:40:56,575 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpuwjr_so3
2025-08-07 17:40:56,575 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1049.mp4 (确认存在: True)
2025-08-07 17:40:56,575 - INFO - 添加场景ID=1049，时长=1.52秒，累计时长=1.52秒
2025-08-07 17:40:56,575 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1050.mp4 (确认存在: True)
2025-08-07 17:40:56,576 - INFO - 添加场景ID=1050，时长=1.04秒，累计时长=2.56秒
2025-08-07 17:40:56,576 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1051.mp4 (确认存在: True)
2025-08-07 17:40:56,576 - INFO - 添加场景ID=1051，时长=0.72秒，累计时长=3.28秒
2025-08-07 17:40:56,576 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1052.mp4 (确认存在: True)
2025-08-07 17:40:56,576 - INFO - 添加场景ID=1052，时长=1.52秒，累计时长=4.80秒
2025-08-07 17:40:56,576 - INFO - 准备合并 4 个场景文件，总时长约 4.80秒
2025-08-07 17:40:56,576 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1049.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1050.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1051.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1052.mp4'

2025-08-07 17:40:56,577 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpuwjr_so3\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpuwjr_so3\temp_combined.mp4
2025-08-07 17:40:56,733 - INFO - 合并后的视频时长: 4.89秒，目标音频时长: 4.29秒
2025-08-07 17:40:56,734 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpuwjr_so3\temp_combined.mp4 -ss 0 -to 4.286 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\30_2.mp4
2025-08-07 17:40:57,034 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:57,034 - INFO - 目标音频时长: 4.29秒
2025-08-07 17:40:57,034 - INFO - 实际视频时长: 4.34秒
2025-08-07 17:40:57,034 - INFO - 时长差异: 0.06秒 (1.33%)
2025-08-07 17:40:57,034 - INFO - ==========================================
2025-08-07 17:40:57,034 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:57,034 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\30_2.mp4
2025-08-07 17:40:57,035 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpuwjr_so3
2025-08-07 17:40:57,083 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:57,083 - INFO -   - 音频时长: 4.29秒
2025-08-07 17:40:57,083 - INFO -   - 视频时长: 4.34秒
2025-08-07 17:40:57,083 - INFO -   - 时长差异: 0.06秒 (1.33%)
2025-08-07 17:40:57,083 - INFO - 
----- 处理字幕 #30 的方案 #3 -----
2025-08-07 17:40:57,083 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\30_3.mp4
2025-08-07 17:40:57,083 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpruznqka5
2025-08-07 17:40:57,084 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1012.mp4 (确认存在: True)
2025-08-07 17:40:57,084 - INFO - 添加场景ID=1012，时长=1.92秒，累计时长=1.92秒
2025-08-07 17:40:57,084 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1049.mp4 (确认存在: True)
2025-08-07 17:40:57,084 - INFO - 添加场景ID=1049，时长=1.52秒，累计时长=3.44秒
2025-08-07 17:40:57,084 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1050.mp4 (确认存在: True)
2025-08-07 17:40:57,084 - INFO - 添加场景ID=1050，时长=1.04秒，累计时长=4.48秒
2025-08-07 17:40:57,084 - INFO - 准备合并 3 个场景文件，总时长约 4.48秒
2025-08-07 17:40:57,084 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1012.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1049.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1050.mp4'

2025-08-07 17:40:57,084 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpruznqka5\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpruznqka5\temp_combined.mp4
2025-08-07 17:40:57,238 - INFO - 合并后的视频时长: 4.55秒，目标音频时长: 4.29秒
2025-08-07 17:40:57,239 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpruznqka5\temp_combined.mp4 -ss 0 -to 4.286 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\30_3.mp4
2025-08-07 17:40:57,532 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:57,532 - INFO - 目标音频时长: 4.29秒
2025-08-07 17:40:57,532 - INFO - 实际视频时长: 4.34秒
2025-08-07 17:40:57,532 - INFO - 时长差异: 0.06秒 (1.33%)
2025-08-07 17:40:57,532 - INFO - ==========================================
2025-08-07 17:40:57,532 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:57,532 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\30_3.mp4
2025-08-07 17:40:57,533 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpruznqka5
2025-08-07 17:40:57,576 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:57,576 - INFO -   - 音频时长: 4.29秒
2025-08-07 17:40:57,576 - INFO -   - 视频时长: 4.34秒
2025-08-07 17:40:57,576 - INFO -   - 时长差异: 0.06秒 (1.33%)
2025-08-07 17:40:57,576 - INFO - 
字幕 #30 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:57,576 - INFO - 生成的视频文件:
2025-08-07 17:40:57,576 - INFO -   1. F:/github/aicut_auto/newcut_ai\30_1.mp4
2025-08-07 17:40:57,576 - INFO -   2. F:/github/aicut_auto/newcut_ai\30_2.mp4
2025-08-07 17:40:57,576 - INFO -   3. F:/github/aicut_auto/newcut_ai\30_3.mp4
2025-08-07 17:40:57,576 - INFO - ========== 字幕 #30 处理结束 ==========

