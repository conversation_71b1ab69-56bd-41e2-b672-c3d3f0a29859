2025-08-07 17:40:54,736 - INFO - ========== 字幕 #29 处理开始 ==========
2025-08-07 17:40:54,736 - INFO - 字幕内容: 他想摔碎手机毁灭证据，却被女孩抢先一步，单手将他整个人都拎了起来。
2025-08-07 17:40:54,736 - INFO - 字幕序号: [816, 819]
2025-08-07 17:40:54,736 - INFO - 音频文件详情:
2025-08-07 17:40:54,736 - INFO -   - 路径: output\29.wav
2025-08-07 17:40:54,736 - INFO -   - 时长: 3.17秒
2025-08-07 17:40:54,736 - INFO -   - 验证音频时长: 3.17秒
2025-08-07 17:40:54,736 - INFO - 字幕时间戳信息:
2025-08-07 17:40:54,736 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:54,737 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:54,737 - INFO -   - 根据生成的音频时长(3.17秒)已调整字幕时间戳
2025-08-07 17:40:54,737 - INFO - ========== 新模式：为字幕 #29 生成4套场景方案 ==========
2025-08-07 17:40:54,737 - INFO - 字幕序号列表: [816, 819]
2025-08-07 17:40:54,737 - INFO - 
--- 生成方案 #1：基于字幕序号 #816 ---
2025-08-07 17:40:54,737 - INFO - 开始为单个字幕序号 #816 匹配场景，目标时长: 3.17秒
2025-08-07 17:40:54,737 - INFO - 开始查找字幕序号 [816] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:54,737 - INFO - 找到related_overlap场景: scene_id=988, 字幕#816
2025-08-07 17:40:54,738 - INFO - 字幕 #816 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:54,738 - INFO - 字幕序号 #816 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:54,738 - INFO - 选择第一个overlap场景作为起点: scene_id=988
2025-08-07 17:40:54,738 - INFO - 添加起点场景: scene_id=988, 时长=2.56秒, 累计时长=2.56秒
2025-08-07 17:40:54,738 - INFO - 起点场景时长不足，需要延伸填充 0.61秒
2025-08-07 17:40:54,738 - INFO - 起点场景在原始列表中的索引: 987
2025-08-07 17:40:54,738 - INFO - 延伸添加场景: scene_id=989 (裁剪至 0.61秒)
2025-08-07 17:40:54,738 - INFO - 累计时长: 3.17秒
2025-08-07 17:40:54,738 - INFO - 字幕序号 #816 场景匹配完成，共选择 2 个场景，总时长: 3.17秒
2025-08-07 17:40:54,738 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-08-07 17:40:54,738 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-08-07 17:40:54,738 - INFO - 
--- 生成方案 #2：基于字幕序号 #819 ---
2025-08-07 17:40:54,738 - INFO - 开始为单个字幕序号 #819 匹配场景，目标时长: 3.17秒
2025-08-07 17:40:54,738 - INFO - 开始查找字幕序号 [819] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:54,738 - INFO - 找到related_overlap场景: scene_id=992, 字幕#819
2025-08-07 17:40:54,739 - INFO - 找到related_between场景: scene_id=993, 字幕#819
2025-08-07 17:40:54,739 - INFO - 找到related_between场景: scene_id=994, 字幕#819
2025-08-07 17:40:54,739 - INFO - 找到related_between场景: scene_id=995, 字幕#819
2025-08-07 17:40:54,739 - INFO - 找到related_between场景: scene_id=996, 字幕#819
2025-08-07 17:40:54,739 - INFO - 找到related_between场景: scene_id=997, 字幕#819
2025-08-07 17:40:54,739 - INFO - 找到related_between场景: scene_id=998, 字幕#819
2025-08-07 17:40:54,739 - INFO - 找到related_between场景: scene_id=999, 字幕#819
2025-08-07 17:40:54,739 - INFO - 找到related_between场景: scene_id=1000, 字幕#819
2025-08-07 17:40:54,740 - INFO - 字幕 #819 找到 1 个overlap场景, 8 个between场景
2025-08-07 17:40:54,740 - INFO - 字幕序号 #819 找到 1 个可用overlap场景, 8 个可用between场景
2025-08-07 17:40:54,740 - INFO - 选择第一个overlap场景作为起点: scene_id=992
2025-08-07 17:40:54,740 - INFO - 添加起点场景: scene_id=992, 时长=2.48秒, 累计时长=2.48秒
2025-08-07 17:40:54,740 - INFO - 起点场景时长不足，需要延伸填充 0.69秒
2025-08-07 17:40:54,740 - INFO - 起点场景在原始列表中的索引: 991
2025-08-07 17:40:54,740 - INFO - 延伸添加场景: scene_id=993 (裁剪至 0.69秒)
2025-08-07 17:40:54,740 - INFO - 累计时长: 3.17秒
2025-08-07 17:40:54,740 - INFO - 字幕序号 #819 场景匹配完成，共选择 2 个场景，总时长: 3.17秒
2025-08-07 17:40:54,740 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-08-07 17:40:54,740 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:54,740 - INFO - ========== 当前模式：为字幕 #29 生成 1 套场景方案 ==========
2025-08-07 17:40:54,740 - INFO - 开始查找字幕序号 [816, 819] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:54,740 - INFO - 找到related_overlap场景: scene_id=988, 字幕#816
2025-08-07 17:40:54,740 - INFO - 找到related_overlap场景: scene_id=992, 字幕#819
2025-08-07 17:40:54,740 - INFO - 找到related_between场景: scene_id=993, 字幕#819
2025-08-07 17:40:54,741 - INFO - 找到related_between场景: scene_id=994, 字幕#819
2025-08-07 17:40:54,741 - INFO - 找到related_between场景: scene_id=995, 字幕#819
2025-08-07 17:40:54,741 - INFO - 找到related_between场景: scene_id=996, 字幕#819
2025-08-07 17:40:54,741 - INFO - 找到related_between场景: scene_id=997, 字幕#819
2025-08-07 17:40:54,741 - INFO - 找到related_between场景: scene_id=998, 字幕#819
2025-08-07 17:40:54,741 - INFO - 找到related_between场景: scene_id=999, 字幕#819
2025-08-07 17:40:54,741 - INFO - 找到related_between场景: scene_id=1000, 字幕#819
2025-08-07 17:40:54,741 - INFO - 字幕 #816 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:54,741 - INFO - 字幕 #819 找到 1 个overlap场景, 8 个between场景
2025-08-07 17:40:54,741 - INFO - 共收集 2 个未使用的overlap场景和 8 个未使用的between场景
2025-08-07 17:40:54,741 - INFO - 开始生成方案 #1
2025-08-07 17:40:54,741 - INFO - 方案 #1: 为字幕#816选择初始化overlap场景id=988
2025-08-07 17:40:54,741 - INFO - 方案 #1: 为字幕#819选择初始化overlap场景id=992
2025-08-07 17:40:54,741 - INFO - 方案 #1: 初始选择后，当前总时长=5.04秒
2025-08-07 17:40:54,741 - INFO - 方案 #1: 额外between选择后，当前总时长=5.04秒
2025-08-07 17:40:54,741 - INFO - 方案 #1: 场景总时长(5.04秒)大于音频时长(3.17秒)，需要裁剪
2025-08-07 17:40:54,741 - INFO - 调整前总时长: 5.04秒, 目标时长: 3.17秒
2025-08-07 17:40:54,741 - INFO - 需要裁剪 1.87秒
2025-08-07 17:40:54,741 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-08-07 17:40:54,741 - INFO - 裁剪场景ID=988：从2.56秒裁剪至1.00秒
2025-08-07 17:40:54,741 - INFO - 裁剪场景ID=992：从2.48秒裁剪至2.17秒
2025-08-07 17:40:54,741 - INFO - 调整后总时长: 3.17秒，与目标时长差异: 0.00秒
2025-08-07 17:40:54,741 - INFO - 方案 #1 调整/填充后最终总时长: 3.17秒
2025-08-07 17:40:54,741 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:54,741 - INFO - ========== 当前模式：字幕 #29 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:54,741 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:54,741 - INFO - ========== 新模式：字幕 #29 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:54,741 - INFO - 
----- 处理字幕 #29 的方案 #1 -----
2025-08-07 17:40:54,741 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\29_1.mp4
2025-08-07 17:40:54,742 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpv7kirma8
2025-08-07 17:40:54,742 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\988.mp4 (确认存在: True)
2025-08-07 17:40:54,742 - INFO - 添加场景ID=988，时长=2.56秒，累计时长=2.56秒
2025-08-07 17:40:54,742 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\989.mp4 (确认存在: True)
2025-08-07 17:40:54,742 - INFO - 添加场景ID=989，时长=1.20秒，累计时长=3.76秒
2025-08-07 17:40:54,742 - INFO - 准备合并 2 个场景文件，总时长约 3.76秒
2025-08-07 17:40:54,742 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/988.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/989.mp4'

2025-08-07 17:40:54,743 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpv7kirma8\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpv7kirma8\temp_combined.mp4
2025-08-07 17:40:54,862 - INFO - 合并后的视频时长: 3.81秒，目标音频时长: 3.17秒
2025-08-07 17:40:54,862 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpv7kirma8\temp_combined.mp4 -ss 0 -to 3.168 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\29_1.mp4
2025-08-07 17:40:55,124 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:55,124 - INFO - 目标音频时长: 3.17秒
2025-08-07 17:40:55,124 - INFO - 实际视频时长: 3.22秒
2025-08-07 17:40:55,124 - INFO - 时长差异: 0.05秒 (1.74%)
2025-08-07 17:40:55,124 - INFO - ==========================================
2025-08-07 17:40:55,124 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:55,124 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\29_1.mp4
2025-08-07 17:40:55,125 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpv7kirma8
2025-08-07 17:40:55,168 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:55,168 - INFO -   - 音频时长: 3.17秒
2025-08-07 17:40:55,168 - INFO -   - 视频时长: 3.22秒
2025-08-07 17:40:55,168 - INFO -   - 时长差异: 0.05秒 (1.74%)
2025-08-07 17:40:55,168 - INFO - 
----- 处理字幕 #29 的方案 #2 -----
2025-08-07 17:40:55,168 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\29_2.mp4
2025-08-07 17:40:55,168 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8rfnpixo
2025-08-07 17:40:55,169 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\992.mp4 (确认存在: True)
2025-08-07 17:40:55,169 - INFO - 添加场景ID=992，时长=2.48秒，累计时长=2.48秒
2025-08-07 17:40:55,169 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\993.mp4 (确认存在: True)
2025-08-07 17:40:55,169 - INFO - 添加场景ID=993，时长=0.80秒，累计时长=3.28秒
2025-08-07 17:40:55,169 - INFO - 准备合并 2 个场景文件，总时长约 3.28秒
2025-08-07 17:40:55,169 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/992.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/993.mp4'

2025-08-07 17:40:55,169 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp8rfnpixo\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp8rfnpixo\temp_combined.mp4
2025-08-07 17:40:55,287 - INFO - 合并后的视频时长: 3.33秒，目标音频时长: 3.17秒
2025-08-07 17:40:55,287 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp8rfnpixo\temp_combined.mp4 -ss 0 -to 3.168 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\29_2.mp4
2025-08-07 17:40:55,562 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:55,562 - INFO - 目标音频时长: 3.17秒
2025-08-07 17:40:55,562 - INFO - 实际视频时长: 3.22秒
2025-08-07 17:40:55,562 - INFO - 时长差异: 0.05秒 (1.74%)
2025-08-07 17:40:55,562 - INFO - ==========================================
2025-08-07 17:40:55,562 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:55,562 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\29_2.mp4
2025-08-07 17:40:55,563 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8rfnpixo
2025-08-07 17:40:55,607 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:55,608 - INFO -   - 音频时长: 3.17秒
2025-08-07 17:40:55,608 - INFO -   - 视频时长: 3.22秒
2025-08-07 17:40:55,608 - INFO -   - 时长差异: 0.05秒 (1.74%)
2025-08-07 17:40:55,608 - INFO - 
----- 处理字幕 #29 的方案 #3 -----
2025-08-07 17:40:55,608 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\29_3.mp4
2025-08-07 17:40:55,617 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpeo40a83s
2025-08-07 17:40:55,617 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\988.mp4 (确认存在: True)
2025-08-07 17:40:55,618 - INFO - 添加场景ID=988，时长=2.56秒，累计时长=2.56秒
2025-08-07 17:40:55,618 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\992.mp4 (确认存在: True)
2025-08-07 17:40:55,618 - INFO - 添加场景ID=992，时长=2.48秒，累计时长=5.04秒
2025-08-07 17:40:55,618 - INFO - 场景总时长(5.04秒)已达到音频时长(3.17秒)的1.5倍，停止添加场景
2025-08-07 17:40:55,618 - INFO - 准备合并 2 个场景文件，总时长约 5.04秒
2025-08-07 17:40:55,618 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/988.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/992.mp4'

2025-08-07 17:40:55,618 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpeo40a83s\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpeo40a83s\temp_combined.mp4
2025-08-07 17:40:55,737 - INFO - 合并后的视频时长: 5.09秒，目标音频时长: 3.17秒
2025-08-07 17:40:55,737 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpeo40a83s\temp_combined.mp4 -ss 0 -to 3.168 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\29_3.mp4
2025-08-07 17:40:55,993 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:55,993 - INFO - 目标音频时长: 3.17秒
2025-08-07 17:40:55,993 - INFO - 实际视频时长: 3.22秒
2025-08-07 17:40:55,993 - INFO - 时长差异: 0.05秒 (1.74%)
2025-08-07 17:40:55,993 - INFO - ==========================================
2025-08-07 17:40:55,993 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:55,993 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\29_3.mp4
2025-08-07 17:40:55,994 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpeo40a83s
2025-08-07 17:40:56,039 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:56,039 - INFO -   - 音频时长: 3.17秒
2025-08-07 17:40:56,039 - INFO -   - 视频时长: 3.22秒
2025-08-07 17:40:56,039 - INFO -   - 时长差异: 0.05秒 (1.74%)
2025-08-07 17:40:56,039 - INFO - 
字幕 #29 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:56,039 - INFO - 生成的视频文件:
2025-08-07 17:40:56,039 - INFO -   1. F:/github/aicut_auto/newcut_ai\29_1.mp4
2025-08-07 17:40:56,039 - INFO -   2. F:/github/aicut_auto/newcut_ai\29_2.mp4
2025-08-07 17:40:56,039 - INFO -   3. F:/github/aicut_auto/newcut_ai\29_3.mp4
2025-08-07 17:40:56,039 - INFO - ========== 字幕 #29 处理结束 ==========

