2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,648 - INFO -     - 扩展后区域: (195, 910, 525, 992)
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,667 - INFO - 已保存可视化结果到: temp_frames\7_1_cleaned_frame_22_visualization.png
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,669 - INFO - 步骤3: 掩码已保存到 temp_frames\7_1_mask_22.png
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,669 - INFO - 步骤4: 调用IOPaint API去除水印
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: GPU内存使用情况: 空闲 10.78GB / 总共 11.99GB
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: - 正在发送API请求 (尝试 1/5)
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: - IOPaint API返回PNG图片数据
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,850 - INFO - 水印去除成功，结果保存到: temp_frames\7_1_cleaned_frame_130_success.png
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,850 - INFO - ==================================================
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,850 - INFO - 图片处理完成: temp_frames\7_1_frame_130.png
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,850 - INFO - ==================================================
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: GPU内存已清理
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,881 - INFO - ==================================================
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,881 - INFO - 开始处理图片: temp_frames\7_1_frame_23.png
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,881 - INFO - 处理参数:
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,881 - INFO - - 区域模式: 1
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,881 - INFO - - 区域坐标: [0, 895, 720, 1008]
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,881 - INFO - - 使用高斯模糊: False
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,881 - INFO - - 红色填充像素: 20
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,881 - INFO - - 帧信息: {'frame_idx': 23, 'original_video': '7_1'}
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,881 - INFO - ==================================================
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: GPU内存已清理
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,910 - INFO - === 开始第一次 OCR 尝试 (端口 1224) ===
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,910 - INFO - 第一次OCR尝试 (端口 1224)，重试 1/3
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: - IOPaint API返回PNG图片数据
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,926 - INFO - 水印去除成功，结果保存到: temp_frames\7_1_cleaned_frame_131_success.png
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,926 - INFO - ==================================================
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,926 - INFO - 图片处理完成: temp_frames\7_1_frame_131.png
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,926 - INFO - ==================================================
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: GPU内存已清理
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,957 - INFO - ==================================================
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,957 - INFO - 开始处理图片: temp_frames\7_1_frame_24.png
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,957 - INFO - 处理参数:
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,957 - INFO - - 区域模式: 1
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,957 - INFO - - 区域坐标: [0, 895, 720, 1008]
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,957 - INFO - - 使用高斯模糊: False
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,957 - INFO - - 红色填充像素: 20
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,957 - INFO - - 帧信息: {'frame_idx': 24, 'original_video': '7_1'}
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,957 - INFO - ==================================================
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,977 - INFO - API响应内容: {'code': 100, 'data': [{'box': [[215, 36], [504, 36], [504, 78], [215, 78]], 'score': 0.9876880049705505, 'text': '就谢毅臣也配', 'end': '\n'}], 'score': 0.9876880049705505, 'time': 0.06105470657348633, 'timestamp': 1754552341.9764302}
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,977 - INFO - OCR识别成功，检测到文字 (尝试 1/3)
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,977 - INFO - 检测到的文字区域数量: 1
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: GPU内存已清理
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,977 - INFO - 第一次OCR尝试 (端口 1224) 完成，成功检测到文字区域数量: 1
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,977 - INFO - === 第一次 OCR 尝试结束 ===
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,977 - INFO - === 处理检测到的文字区域 (总数: 1) ===
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,977 - INFO -     - 文字: 就谢毅臣也配 (置信度: 0.988)
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,978 - INFO -     - 原始文字区域: (215, 931, 504, 973)
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,978 - INFO -     - 扩展后区域: (195, 911, 524, 993)
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,989 - INFO - === 开始第一次 OCR 尝试 (端口 1224) ===
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,989 - INFO - 第一次OCR尝试 (端口 1224)，重试 1/3
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,997 - INFO - 已保存可视化结果到: temp_frames\7_1_cleaned_frame_23_visualization.png
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,998 - INFO - 步骤3: 掩码已保存到 temp_frames\7_1_mask_23.png
2025-08-07 15:39:01 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:01,998 - INFO - 步骤4: 调用IOPaint API去除水印
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: GPU内存使用情况: 空闲 10.78GB / 总共 11.99GB
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: - 正在发送API请求 (尝试 1/5)
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: - IOPaint API返回PNG图片数据
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,025 - INFO - 水印去除成功，结果保存到: temp_frames\7_1_cleaned_frame_132_success.png
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,025 - INFO - ==================================================
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,026 - INFO - 图片处理完成: temp_frames\7_1_frame_132.png
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,026 - INFO - ==================================================
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: GPU内存已清理
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,057 - INFO - ==================================================
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,057 - INFO - 开始处理图片: temp_frames\7_1_frame_25.png
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,057 - INFO - 处理参数:
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,057 - INFO - - 区域模式: 1
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,057 - INFO - - 区域坐标: [0, 895, 720, 1008]
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,057 - INFO - - 使用高斯模糊: False
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,057 - INFO - - 红色填充像素: 20
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,058 - INFO - - 帧信息: {'frame_idx': 25, 'original_video': '7_1'}
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,058 - INFO - ==================================================
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,075 - INFO - API响应内容: {'code': 100, 'data': [{'box': [[214, 35], [505, 33], [505, 76], [214, 78]], 'score': 0.903357744216919, 'text': '就谢毅臣也配', 'end': '\n'}], 'score': 0.903357744216919, 'time': 0.06395220756530762, 'timestamp': 1754552342.05892}
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,075 - INFO - OCR识别成功，检测到文字 (尝试 1/3)
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,075 - INFO - 检测到的文字区域数量: 1
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: GPU内存已清理
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,075 - INFO - 第一次OCR尝试 (端口 1224) 完成，成功检测到文字区域数量: 1
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,075 - INFO - === 第一次 OCR 尝试结束 ===
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,075 - INFO - === 处理检测到的文字区域 (总数: 1) ===
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,076 - INFO -     - 文字: 就谢毅臣也配 (置信度: 0.903)
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,076 - INFO -     - 原始文字区域: (214, 928, 505, 973)
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,076 - INFO -     - 扩展后区域: (194, 908, 525, 993)
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: - IOPaint API返回PNG图片数据
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,077 - INFO - 水印去除成功，结果保存到: temp_frames\7_1_cleaned_frame_133_success.png
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,077 - INFO - ==================================================
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,077 - INFO - 图片处理完成: temp_frames\7_1_frame_133.png
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,078 - INFO - ==================================================
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: GPU内存已清理
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,097 - INFO - === 开始第一次 OCR 尝试 (端口 1224) ===
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,097 - INFO - 第一次OCR尝试 (端口 1224)，重试 1/3
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,098 - INFO - 已保存可视化结果到: temp_frames\7_1_cleaned_frame_24_visualization.png
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,100 - INFO - 步骤3: 掩码已保存到 temp_frames\7_1_mask_24.png
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,100 - INFO - 步骤4: 调用IOPaint API去除水印
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: GPU内存使用情况: 空闲 10.78GB / 总共 11.99GB
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: - 正在发送API请求 (尝试 1/5)
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,109 - INFO - ==================================================
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,110 - INFO - 开始处理图片: temp_frames\7_1_frame_26.png
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,110 - INFO - 处理参数:
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,110 - INFO - - 区域模式: 1
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,110 - INFO - - 区域坐标: [0, 895, 720, 1008]
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,110 - INFO - - 使用高斯模糊: False
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,110 - INFO - - 红色填充像素: 20
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,110 - INFO - - 帧信息: {'frame_idx': 26, 'original_video': '7_1'}
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,110 - INFO - ==================================================
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: GPU内存已清理
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,141 - INFO - === 开始第一次 OCR 尝试 (端口 1224) ===
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,141 - INFO - 第一次OCR尝试 (端口 1224)，重试 1/3
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,167 - INFO - API响应内容: {'code': 100, 'data': [{'box': [[215, 36], [505, 36], [505, 78], [215, 78]], 'score': 0.9875917434692383, 'text': '就谢毅臣也配', 'end': '\n'}], 'score': 0.9875917434692383, 'time': 0.06128883361816406, 'timestamp': 1754552342.1668186}
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,167 - INFO - OCR识别成功，检测到文字 (尝试 1/3)
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,167 - INFO - 检测到的文字区域数量: 1
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,167 - INFO - 第一次OCR尝试 (端口 1224) 完成，成功检测到文字区域数量: 1
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,168 - INFO - === 第一次 OCR 尝试结束 ===
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,168 - INFO - === 处理检测到的文字区域 (总数: 1) ===
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,168 - INFO -     - 文字: 就谢毅臣也配 (置信度: 0.988)
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,168 - INFO -     - 原始文字区域: (215, 931, 505, 973)
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,168 - INFO -     - 扩展后区域: (195, 911, 525, 993)
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,188 - INFO - 已保存可视化结果到: temp_frames\7_1_cleaned_frame_25_visualization.png
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,189 - INFO - 步骤3: 掩码已保存到 temp_frames\7_1_mask_25.png
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,189 - INFO - 步骤4: 调用IOPaint API去除水印
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,223 - INFO - API响应内容: {'code': 100, 'data': [{'box': [[215, 36], [505, 36], [505, 78], [215, 78]], 'score': 0.9863224029541016, 'text': '就谢毅臣也配', 'end': '\n'}], 'score': 0.9863224029541016, 'time': 0.053694963455200195, 'timestamp': 1754552342.2225177}
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,223 - INFO - OCR识别成功，检测到文字 (尝试 1/3)
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,223 - INFO - 检测到的文字区域数量: 1
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: GPU内存使用情况: 空闲 10.78GB / 总共 11.99GB
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: - 正在发送API请求 (尝试 1/5)
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,223 - INFO - 第一次OCR尝试 (端口 1224) 完成，成功检测到文字区域数量: 1
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,223 - INFO - === 第一次 OCR 尝试结束 ===
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,223 - INFO - === 处理检测到的文字区域 (总数: 1) ===
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,223 - INFO -     - 文字: 就谢毅臣也配 (置信度: 0.986)
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,223 - INFO -     - 原始文字区域: (215, 931, 505, 973)
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,223 - INFO -     - 扩展后区域: (195, 911, 525, 993)
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,244 - INFO - 已保存可视化结果到: temp_frames\7_1_cleaned_frame_26_visualization.png
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,246 - INFO - 步骤3: 掩码已保存到 temp_frames\7_1_mask_26.png
2025-08-07 15:39:02 - aicut_auto.core.watermark_remover - INFO - _execute_watermark_removal_script:264 - [watermark_removal2]: 2025-08-07 15:39:02,246 - INFO - 步骤4: 调用IOPaint API去除水印
