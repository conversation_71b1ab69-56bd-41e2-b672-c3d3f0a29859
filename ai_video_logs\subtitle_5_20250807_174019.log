2025-08-07 17:40:19,460 - INFO - ========== 字幕 #5 处理开始 ==========
2025-08-07 17:40:19,460 - INFO - 字幕内容: 心机女颠倒黑白，哭诉男人身为大哥，竟对自己的弟媳图谋不轨。
2025-08-07 17:40:19,460 - INFO - 字幕序号: [57, 68]
2025-08-07 17:40:19,460 - INFO - 音频文件详情:
2025-08-07 17:40:19,460 - INFO -   - 路径: output\5.wav
2025-08-07 17:40:19,460 - INFO -   - 时长: 4.95秒
2025-08-07 17:40:19,460 - INFO -   - 验证音频时长: 4.95秒
2025-08-07 17:40:19,460 - INFO - 字幕时间戳信息:
2025-08-07 17:40:19,460 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:19,460 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:19,460 - INFO -   - 根据生成的音频时长(4.95秒)已调整字幕时间戳
2025-08-07 17:40:19,460 - INFO - ========== 新模式：为字幕 #5 生成4套场景方案 ==========
2025-08-07 17:40:19,460 - INFO - 字幕序号列表: [57, 68]
2025-08-07 17:40:19,460 - INFO - 
--- 生成方案 #1：基于字幕序号 #57 ---
2025-08-07 17:40:19,460 - INFO - 开始为单个字幕序号 #57 匹配场景，目标时长: 4.95秒
2025-08-07 17:40:19,460 - INFO - 开始查找字幕序号 [57] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:19,460 - INFO - 找到related_overlap场景: scene_id=101, 字幕#57
2025-08-07 17:40:19,460 - INFO - 找到related_overlap场景: scene_id=102, 字幕#57
2025-08-07 17:40:19,462 - INFO - 字幕 #57 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:40:19,462 - INFO - 字幕序号 #57 找到 2 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:19,462 - INFO - 选择第一个overlap场景作为起点: scene_id=101
2025-08-07 17:40:19,462 - INFO - 添加起点场景: scene_id=101, 时长=2.24秒, 累计时长=2.24秒
2025-08-07 17:40:19,462 - INFO - 起点场景时长不足，需要延伸填充 2.71秒
2025-08-07 17:40:19,462 - INFO - 起点场景在原始列表中的索引: 100
2025-08-07 17:40:19,462 - INFO - 延伸添加场景: scene_id=102 (裁剪至 2.71秒)
2025-08-07 17:40:19,462 - INFO - 累计时长: 4.95秒
2025-08-07 17:40:19,462 - INFO - 字幕序号 #57 场景匹配完成，共选择 2 个场景，总时长: 4.95秒
2025-08-07 17:40:19,462 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-08-07 17:40:19,462 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-08-07 17:40:19,462 - INFO - 
--- 生成方案 #2：基于字幕序号 #68 ---
2025-08-07 17:40:19,462 - INFO - 开始为单个字幕序号 #68 匹配场景，目标时长: 4.95秒
2025-08-07 17:40:19,462 - INFO - 开始查找字幕序号 [68] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:19,462 - INFO - 找到related_overlap场景: scene_id=113, 字幕#68
2025-08-07 17:40:19,462 - INFO - 找到related_overlap场景: scene_id=114, 字幕#68
2025-08-07 17:40:19,464 - INFO - 字幕 #68 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:40:19,464 - INFO - 字幕序号 #68 找到 2 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:19,464 - INFO - 选择第一个overlap场景作为起点: scene_id=113
2025-08-07 17:40:19,464 - INFO - 添加起点场景: scene_id=113, 时长=2.00秒, 累计时长=2.00秒
2025-08-07 17:40:19,464 - INFO - 起点场景时长不足，需要延伸填充 2.95秒
2025-08-07 17:40:19,464 - INFO - 起点场景在原始列表中的索引: 112
2025-08-07 17:40:19,464 - INFO - 延伸添加场景: scene_id=114 (完整时长 1.64秒)
2025-08-07 17:40:19,464 - INFO - 累计时长: 3.64秒
2025-08-07 17:40:19,464 - INFO - 延伸添加场景: scene_id=115 (裁剪至 1.31秒)
2025-08-07 17:40:19,464 - INFO - 累计时长: 4.95秒
2025-08-07 17:40:19,464 - INFO - 字幕序号 #68 场景匹配完成，共选择 3 个场景，总时长: 4.95秒
2025-08-07 17:40:19,464 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-08-07 17:40:19,464 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:19,464 - INFO - ========== 当前模式：为字幕 #5 生成 1 套场景方案 ==========
2025-08-07 17:40:19,464 - INFO - 开始查找字幕序号 [57, 68] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:19,464 - INFO - 找到related_overlap场景: scene_id=101, 字幕#57
2025-08-07 17:40:19,464 - INFO - 找到related_overlap场景: scene_id=102, 字幕#57
2025-08-07 17:40:19,464 - INFO - 找到related_overlap场景: scene_id=113, 字幕#68
2025-08-07 17:40:19,464 - INFO - 找到related_overlap场景: scene_id=114, 字幕#68
2025-08-07 17:40:19,465 - INFO - 字幕 #57 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:40:19,465 - INFO - 字幕 #68 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:40:19,465 - INFO - 共收集 4 个未使用的overlap场景和 0 个未使用的between场景
2025-08-07 17:40:19,465 - INFO - 开始生成方案 #1
2025-08-07 17:40:19,465 - INFO - 方案 #1: 为字幕#57选择初始化overlap场景id=101
2025-08-07 17:40:19,465 - INFO - 方案 #1: 为字幕#68选择初始化overlap场景id=113
2025-08-07 17:40:19,465 - INFO - 方案 #1: 初始选择后，当前总时长=4.24秒
2025-08-07 17:40:19,465 - INFO - 方案 #1: 额外添加overlap场景id=102, 当前总时长=7.88秒
2025-08-07 17:40:19,465 - INFO - 方案 #1: 额外between选择后，当前总时长=7.88秒
2025-08-07 17:40:19,465 - INFO - 方案 #1: 场景总时长(7.88秒)大于音频时长(4.95秒)，需要裁剪
2025-08-07 17:40:19,465 - INFO - 调整前总时长: 7.88秒, 目标时长: 4.95秒
2025-08-07 17:40:19,465 - INFO - 需要裁剪 2.93秒
2025-08-07 17:40:19,465 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-08-07 17:40:19,465 - INFO - 裁剪场景ID=102：从3.64秒裁剪至1.09秒
2025-08-07 17:40:19,465 - INFO - 裁剪场景ID=101：从2.24秒裁剪至1.86秒
2025-08-07 17:40:19,465 - INFO - 调整后总时长: 4.95秒，与目标时长差异: 0.00秒
2025-08-07 17:40:19,465 - INFO - 方案 #1 调整/填充后最终总时长: 4.95秒
2025-08-07 17:40:19,465 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:19,465 - INFO - ========== 当前模式：字幕 #5 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:19,465 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:19,465 - INFO - ========== 新模式：字幕 #5 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:19,465 - INFO - 
----- 处理字幕 #5 的方案 #1 -----
2025-08-07 17:40:19,465 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\5_1.mp4
2025-08-07 17:40:19,466 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpisuf9rcz
2025-08-07 17:40:19,466 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\101.mp4 (确认存在: True)
2025-08-07 17:40:19,466 - INFO - 添加场景ID=101，时长=2.24秒，累计时长=2.24秒
2025-08-07 17:40:19,466 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\102.mp4 (确认存在: True)
2025-08-07 17:40:19,466 - INFO - 添加场景ID=102，时长=3.64秒，累计时长=5.88秒
2025-08-07 17:40:19,466 - INFO - 准备合并 2 个场景文件，总时长约 5.88秒
2025-08-07 17:40:19,466 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/101.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/102.mp4'

2025-08-07 17:40:19,467 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpisuf9rcz\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpisuf9rcz\temp_combined.mp4
2025-08-07 17:40:19,580 - INFO - 合并后的视频时长: 5.93秒，目标音频时长: 4.95秒
2025-08-07 17:40:19,580 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpisuf9rcz\temp_combined.mp4 -ss 0 -to 4.951 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\5_1.mp4
2025-08-07 17:40:19,857 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:19,857 - INFO - 目标音频时长: 4.95秒
2025-08-07 17:40:19,857 - INFO - 实际视频时长: 4.98秒
2025-08-07 17:40:19,857 - INFO - 时长差异: 0.03秒 (0.65%)
2025-08-07 17:40:19,857 - INFO - ==========================================
2025-08-07 17:40:19,857 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:19,857 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\5_1.mp4
2025-08-07 17:40:19,857 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpisuf9rcz
2025-08-07 17:40:19,900 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:19,900 - INFO -   - 音频时长: 4.95秒
2025-08-07 17:40:19,900 - INFO -   - 视频时长: 4.98秒
2025-08-07 17:40:19,900 - INFO -   - 时长差异: 0.03秒 (0.65%)
2025-08-07 17:40:19,900 - INFO - 
----- 处理字幕 #5 的方案 #2 -----
2025-08-07 17:40:19,900 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\5_2.mp4
2025-08-07 17:40:19,901 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_h_9ze0g
2025-08-07 17:40:19,901 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\113.mp4 (确认存在: True)
2025-08-07 17:40:19,901 - INFO - 添加场景ID=113，时长=2.00秒，累计时长=2.00秒
2025-08-07 17:40:19,901 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\114.mp4 (确认存在: True)
2025-08-07 17:40:19,901 - INFO - 添加场景ID=114，时长=1.64秒，累计时长=3.64秒
2025-08-07 17:40:19,901 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\115.mp4 (确认存在: True)
2025-08-07 17:40:19,901 - INFO - 添加场景ID=115，时长=1.56秒，累计时长=5.20秒
2025-08-07 17:40:19,901 - INFO - 准备合并 3 个场景文件，总时长约 5.20秒
2025-08-07 17:40:19,902 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/113.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/114.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/115.mp4'

2025-08-07 17:40:19,902 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp_h_9ze0g\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp_h_9ze0g\temp_combined.mp4
2025-08-07 17:40:20,053 - INFO - 合并后的视频时长: 5.27秒，目标音频时长: 4.95秒
2025-08-07 17:40:20,053 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp_h_9ze0g\temp_combined.mp4 -ss 0 -to 4.951 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\5_2.mp4
2025-08-07 17:40:20,368 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:20,368 - INFO - 目标音频时长: 4.95秒
2025-08-07 17:40:20,368 - INFO - 实际视频时长: 4.98秒
2025-08-07 17:40:20,368 - INFO - 时长差异: 0.03秒 (0.65%)
2025-08-07 17:40:20,368 - INFO - ==========================================
2025-08-07 17:40:20,368 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:20,368 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\5_2.mp4
2025-08-07 17:40:20,369 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp_h_9ze0g
2025-08-07 17:40:20,412 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:20,412 - INFO -   - 音频时长: 4.95秒
2025-08-07 17:40:20,412 - INFO -   - 视频时长: 4.98秒
2025-08-07 17:40:20,412 - INFO -   - 时长差异: 0.03秒 (0.65%)
2025-08-07 17:40:20,412 - INFO - 
----- 处理字幕 #5 的方案 #3 -----
2025-08-07 17:40:20,413 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\5_3.mp4
2025-08-07 17:40:20,413 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpr5dlj5fc
2025-08-07 17:40:20,413 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\101.mp4 (确认存在: True)
2025-08-07 17:40:20,413 - INFO - 添加场景ID=101，时长=2.24秒，累计时长=2.24秒
2025-08-07 17:40:20,413 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\113.mp4 (确认存在: True)
2025-08-07 17:40:20,413 - INFO - 添加场景ID=113，时长=2.00秒，累计时长=4.24秒
2025-08-07 17:40:20,413 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\102.mp4 (确认存在: True)
2025-08-07 17:40:20,413 - INFO - 添加场景ID=102，时长=3.64秒，累计时长=7.88秒
2025-08-07 17:40:20,414 - INFO - 场景总时长(7.88秒)已达到音频时长(4.95秒)的1.5倍，停止添加场景
2025-08-07 17:40:20,414 - INFO - 准备合并 3 个场景文件，总时长约 7.88秒
2025-08-07 17:40:20,414 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/101.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/113.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/102.mp4'

2025-08-07 17:40:20,414 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpr5dlj5fc\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpr5dlj5fc\temp_combined.mp4
2025-08-07 17:40:20,545 - INFO - 合并后的视频时长: 7.95秒，目标音频时长: 4.95秒
2025-08-07 17:40:20,545 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpr5dlj5fc\temp_combined.mp4 -ss 0 -to 4.951 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\5_3.mp4
2025-08-07 17:40:20,832 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:20,832 - INFO - 目标音频时长: 4.95秒
2025-08-07 17:40:20,832 - INFO - 实际视频时长: 4.98秒
2025-08-07 17:40:20,832 - INFO - 时长差异: 0.03秒 (0.65%)
2025-08-07 17:40:20,832 - INFO - ==========================================
2025-08-07 17:40:20,832 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:20,832 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\5_3.mp4
2025-08-07 17:40:20,833 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpr5dlj5fc
2025-08-07 17:40:20,877 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:20,877 - INFO -   - 音频时长: 4.95秒
2025-08-07 17:40:20,877 - INFO -   - 视频时长: 4.98秒
2025-08-07 17:40:20,877 - INFO -   - 时长差异: 0.03秒 (0.65%)
2025-08-07 17:40:20,877 - INFO - 
字幕 #5 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:20,877 - INFO - 生成的视频文件:
2025-08-07 17:40:20,877 - INFO -   1. F:/github/aicut_auto/newcut_ai\5_1.mp4
2025-08-07 17:40:20,877 - INFO -   2. F:/github/aicut_auto/newcut_ai\5_2.mp4
2025-08-07 17:40:20,877 - INFO -   3. F:/github/aicut_auto/newcut_ai\5_3.mp4
2025-08-07 17:40:20,877 - INFO - ========== 字幕 #5 处理结束 ==========

