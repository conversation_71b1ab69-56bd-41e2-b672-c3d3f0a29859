2025-08-07 17:41:45,796 - INFO - ========== 字幕 #66 处理开始 ==========
2025-08-07 17:41:45,797 - INFO - 字幕内容: 他因当年的误会怀恨在心，扭曲的恨意让他犯下滔天大罪，如今要回来报复所有人。
2025-08-07 17:41:45,797 - INFO - 字幕序号: [3012, 3016]
2025-08-07 17:41:45,797 - INFO - 音频文件详情:
2025-08-07 17:41:45,797 - INFO -   - 路径: output\66.wav
2025-08-07 17:41:45,797 - INFO -   - 时长: 5.48秒
2025-08-07 17:41:45,797 - INFO -   - 验证音频时长: 5.48秒
2025-08-07 17:41:45,797 - INFO - 字幕时间戳信息:
2025-08-07 17:41:45,797 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:45,797 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:45,797 - INFO -   - 根据生成的音频时长(5.48秒)已调整字幕时间戳
2025-08-07 17:41:45,797 - INFO - ========== 新模式：为字幕 #66 生成4套场景方案 ==========
2025-08-07 17:41:45,797 - INFO - 字幕序号列表: [3012, 3016]
2025-08-07 17:41:45,797 - INFO - 
--- 生成方案 #1：基于字幕序号 #3012 ---
2025-08-07 17:41:45,797 - INFO - 开始为单个字幕序号 #3012 匹配场景，目标时长: 5.48秒
2025-08-07 17:41:45,797 - INFO - 开始查找字幕序号 [3012] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:45,798 - INFO - 找到related_overlap场景: scene_id=3304, 字幕#3012
2025-08-07 17:41:45,799 - INFO - 字幕 #3012 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:45,799 - INFO - 字幕序号 #3012 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:45,799 - INFO - 选择第一个overlap场景作为起点: scene_id=3304
2025-08-07 17:41:45,799 - INFO - 添加起点场景: scene_id=3304, 时长=1.20秒, 累计时长=1.20秒
2025-08-07 17:41:45,799 - INFO - 起点场景时长不足，需要延伸填充 4.28秒
2025-08-07 17:41:45,799 - INFO - 起点场景在原始列表中的索引: 3303
2025-08-07 17:41:45,799 - INFO - 延伸添加场景: scene_id=3305 (完整时长 1.52秒)
2025-08-07 17:41:45,799 - INFO - 累计时长: 2.72秒
2025-08-07 17:41:45,799 - INFO - 延伸添加场景: scene_id=3306 (完整时长 1.40秒)
2025-08-07 17:41:45,799 - INFO - 累计时长: 4.12秒
2025-08-07 17:41:45,799 - INFO - 延伸添加场景: scene_id=3307 (完整时长 1.36秒)
2025-08-07 17:41:45,799 - INFO - 累计时长: 5.48秒
2025-08-07 17:41:45,799 - INFO - 延伸添加场景: scene_id=3308 (裁剪至 0.00秒)
2025-08-07 17:41:45,799 - INFO - 累计时长: 5.48秒
2025-08-07 17:41:45,799 - INFO - 字幕序号 #3012 场景匹配完成，共选择 5 个场景，总时长: 5.48秒
2025-08-07 17:41:45,799 - INFO - 方案 #1 生成成功，包含 5 个场景
2025-08-07 17:41:45,799 - INFO - 新模式：第1套方案的 5 个场景已加入全局已使用集合
2025-08-07 17:41:45,799 - INFO - 
--- 生成方案 #2：基于字幕序号 #3016 ---
2025-08-07 17:41:45,799 - INFO - 开始为单个字幕序号 #3016 匹配场景，目标时长: 5.48秒
2025-08-07 17:41:45,799 - INFO - 开始查找字幕序号 [3016] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:45,800 - INFO - 找到related_overlap场景: scene_id=3308, 字幕#3016
2025-08-07 17:41:45,800 - INFO - 找到related_between场景: scene_id=3309, 字幕#3016
2025-08-07 17:41:45,800 - INFO - 字幕 #3016 找到 1 个overlap场景, 1 个between场景
2025-08-07 17:41:45,800 - INFO - 字幕序号 #3016 找到 0 个可用overlap场景, 1 个可用between场景
2025-08-07 17:41:45,800 - INFO - 没有overlap场景，选择第一个between场景作为起点: scene_id=3309
2025-08-07 17:41:45,800 - INFO - 添加起点场景: scene_id=3309, 时长=1.72秒, 累计时长=1.72秒
2025-08-07 17:41:45,800 - INFO - 起点场景时长不足，需要延伸填充 3.76秒
2025-08-07 17:41:45,800 - INFO - 起点场景在原始列表中的索引: 3308
2025-08-07 17:41:45,800 - INFO - 延伸添加场景: scene_id=3310 (完整时长 1.08秒)
2025-08-07 17:41:45,800 - INFO - 累计时长: 2.80秒
2025-08-07 17:41:45,800 - INFO - 延伸添加场景: scene_id=3311 (完整时长 1.08秒)
2025-08-07 17:41:45,800 - INFO - 累计时长: 3.88秒
2025-08-07 17:41:45,800 - INFO - 延伸添加场景: scene_id=3312 (完整时长 1.20秒)
2025-08-07 17:41:45,800 - INFO - 累计时长: 5.08秒
2025-08-07 17:41:45,800 - INFO - 延伸添加场景: scene_id=3313 (裁剪至 0.40秒)
2025-08-07 17:41:45,800 - INFO - 累计时长: 5.48秒
2025-08-07 17:41:45,801 - INFO - 字幕序号 #3016 场景匹配完成，共选择 5 个场景，总时长: 5.48秒
2025-08-07 17:41:45,801 - INFO - 方案 #2 生成成功，包含 5 个场景
2025-08-07 17:41:45,801 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:45,801 - INFO - ========== 当前模式：为字幕 #66 生成 1 套场景方案 ==========
2025-08-07 17:41:45,801 - INFO - 开始查找字幕序号 [3012, 3016] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:45,801 - INFO - 找到related_overlap场景: scene_id=3304, 字幕#3012
2025-08-07 17:41:45,801 - INFO - 找到related_overlap场景: scene_id=3308, 字幕#3016
2025-08-07 17:41:45,802 - INFO - 找到related_between场景: scene_id=3309, 字幕#3016
2025-08-07 17:41:45,802 - INFO - 字幕 #3012 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:45,802 - INFO - 字幕 #3016 找到 1 个overlap场景, 1 个between场景
2025-08-07 17:41:45,802 - INFO - 共收集 2 个未使用的overlap场景和 1 个未使用的between场景
2025-08-07 17:41:45,802 - INFO - 开始生成方案 #1
2025-08-07 17:41:45,802 - INFO - 方案 #1: 为字幕#3012选择初始化overlap场景id=3304
2025-08-07 17:41:45,802 - INFO - 方案 #1: 为字幕#3016选择初始化overlap场景id=3308
2025-08-07 17:41:45,802 - INFO - 方案 #1: 初始选择后，当前总时长=4.16秒
2025-08-07 17:41:45,802 - INFO - 方案 #1: 额外between选择后，当前总时长=4.16秒
2025-08-07 17:41:45,802 - INFO - 方案 #1: 额外添加between场景id=3309, 当前总时长=5.88秒
2025-08-07 17:41:45,802 - INFO - 方案 #1: 场景总时长(5.88秒)大于音频时长(5.48秒)，需要裁剪
2025-08-07 17:41:45,802 - INFO - 调整前总时长: 5.88秒, 目标时长: 5.48秒
2025-08-07 17:41:45,802 - INFO - 需要裁剪 0.40秒
2025-08-07 17:41:45,802 - INFO - 裁剪最长场景ID=3308：从2.96秒裁剪至2.56秒
2025-08-07 17:41:45,802 - INFO - 调整后总时长: 5.48秒，与目标时长差异: 0.00秒
2025-08-07 17:41:45,802 - INFO - 方案 #1 调整/填充后最终总时长: 5.48秒
2025-08-07 17:41:45,802 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:45,802 - INFO - ========== 当前模式：字幕 #66 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:45,802 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:45,802 - INFO - ========== 新模式：字幕 #66 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:45,802 - INFO - 
----- 处理字幕 #66 的方案 #1 -----
2025-08-07 17:41:45,802 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\66_1.mp4
2025-08-07 17:41:45,802 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpom2au52l
2025-08-07 17:41:45,803 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3304.mp4 (确认存在: True)
2025-08-07 17:41:45,803 - INFO - 添加场景ID=3304，时长=1.20秒，累计时长=1.20秒
2025-08-07 17:41:45,803 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3305.mp4 (确认存在: True)
2025-08-07 17:41:45,803 - INFO - 添加场景ID=3305，时长=1.52秒，累计时长=2.72秒
2025-08-07 17:41:45,803 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3306.mp4 (确认存在: True)
2025-08-07 17:41:45,803 - INFO - 添加场景ID=3306，时长=1.40秒，累计时长=4.12秒
2025-08-07 17:41:45,803 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3307.mp4 (确认存在: True)
2025-08-07 17:41:45,803 - INFO - 添加场景ID=3307，时长=1.36秒，累计时长=5.48秒
2025-08-07 17:41:45,803 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3308.mp4 (确认存在: True)
2025-08-07 17:41:45,803 - INFO - 添加场景ID=3308，时长=2.96秒，累计时长=8.44秒
2025-08-07 17:41:45,803 - INFO - 场景总时长(8.44秒)已达到音频时长(5.48秒)的1.5倍，停止添加场景
2025-08-07 17:41:45,803 - INFO - 准备合并 5 个场景文件，总时长约 8.44秒
2025-08-07 17:41:45,803 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3304.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3305.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3306.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3307.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3308.mp4'

2025-08-07 17:41:45,803 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpom2au52l\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpom2au52l\temp_combined.mp4
2025-08-07 17:41:45,978 - INFO - 合并后的视频时长: 8.56秒，目标音频时长: 5.48秒
2025-08-07 17:41:45,978 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpom2au52l\temp_combined.mp4 -ss 0 -to 5.48 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\66_1.mp4
2025-08-07 17:41:46,317 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:46,317 - INFO - 目标音频时长: 5.48秒
2025-08-07 17:41:46,317 - INFO - 实际视频时长: 5.50秒
2025-08-07 17:41:46,317 - INFO - 时长差异: 0.02秒 (0.42%)
2025-08-07 17:41:46,317 - INFO - ==========================================
2025-08-07 17:41:46,317 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:46,317 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\66_1.mp4
2025-08-07 17:41:46,318 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpom2au52l
2025-08-07 17:41:46,361 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:46,361 - INFO -   - 音频时长: 5.48秒
2025-08-07 17:41:46,361 - INFO -   - 视频时长: 5.50秒
2025-08-07 17:41:46,362 - INFO -   - 时长差异: 0.02秒 (0.42%)
2025-08-07 17:41:46,362 - INFO - 
----- 处理字幕 #66 的方案 #2 -----
2025-08-07 17:41:46,362 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\66_2.mp4
2025-08-07 17:41:46,362 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpw_ogto0o
2025-08-07 17:41:46,362 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3309.mp4 (确认存在: True)
2025-08-07 17:41:46,362 - INFO - 添加场景ID=3309，时长=1.72秒，累计时长=1.72秒
2025-08-07 17:41:46,362 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3310.mp4 (确认存在: True)
2025-08-07 17:41:46,362 - INFO - 添加场景ID=3310，时长=1.08秒，累计时长=2.80秒
2025-08-07 17:41:46,363 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3311.mp4 (确认存在: True)
2025-08-07 17:41:46,363 - INFO - 添加场景ID=3311，时长=1.08秒，累计时长=3.88秒
2025-08-07 17:41:46,363 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3312.mp4 (确认存在: True)
2025-08-07 17:41:46,363 - INFO - 添加场景ID=3312，时长=1.20秒，累计时长=5.08秒
2025-08-07 17:41:46,363 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3313.mp4 (确认存在: True)
2025-08-07 17:41:46,363 - INFO - 添加场景ID=3313，时长=1.80秒，累计时长=6.88秒
2025-08-07 17:41:46,363 - INFO - 准备合并 5 个场景文件，总时长约 6.88秒
2025-08-07 17:41:46,363 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3309.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3310.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3311.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3312.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3313.mp4'

2025-08-07 17:41:46,363 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpw_ogto0o\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpw_ogto0o\temp_combined.mp4
2025-08-07 17:41:46,535 - INFO - 合并后的视频时长: 7.00秒，目标音频时长: 5.48秒
2025-08-07 17:41:46,535 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpw_ogto0o\temp_combined.mp4 -ss 0 -to 5.48 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\66_2.mp4
2025-08-07 17:41:46,852 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:46,852 - INFO - 目标音频时长: 5.48秒
2025-08-07 17:41:46,852 - INFO - 实际视频时长: 5.50秒
2025-08-07 17:41:46,852 - INFO - 时长差异: 0.02秒 (0.42%)
2025-08-07 17:41:46,852 - INFO - ==========================================
2025-08-07 17:41:46,852 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:46,852 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\66_2.mp4
2025-08-07 17:41:46,853 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpw_ogto0o
2025-08-07 17:41:46,898 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:46,898 - INFO -   - 音频时长: 5.48秒
2025-08-07 17:41:46,898 - INFO -   - 视频时长: 5.50秒
2025-08-07 17:41:46,898 - INFO -   - 时长差异: 0.02秒 (0.42%)
2025-08-07 17:41:46,898 - INFO - 
----- 处理字幕 #66 的方案 #3 -----
2025-08-07 17:41:46,898 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\66_3.mp4
2025-08-07 17:41:46,899 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpvapc7q69
2025-08-07 17:41:46,899 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3304.mp4 (确认存在: True)
2025-08-07 17:41:46,899 - INFO - 添加场景ID=3304，时长=1.20秒，累计时长=1.20秒
2025-08-07 17:41:46,899 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3308.mp4 (确认存在: True)
2025-08-07 17:41:46,899 - INFO - 添加场景ID=3308，时长=2.96秒，累计时长=4.16秒
2025-08-07 17:41:46,899 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3309.mp4 (确认存在: True)
2025-08-07 17:41:46,900 - INFO - 添加场景ID=3309，时长=1.72秒，累计时长=5.88秒
2025-08-07 17:41:46,900 - INFO - 准备合并 3 个场景文件，总时长约 5.88秒
2025-08-07 17:41:46,900 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3304.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3308.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3309.mp4'

2025-08-07 17:41:46,900 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpvapc7q69\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpvapc7q69\temp_combined.mp4
2025-08-07 17:41:47,049 - INFO - 合并后的视频时长: 5.95秒，目标音频时长: 5.48秒
2025-08-07 17:41:47,049 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpvapc7q69\temp_combined.mp4 -ss 0 -to 5.48 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\66_3.mp4
2025-08-07 17:41:47,361 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:47,361 - INFO - 目标音频时长: 5.48秒
2025-08-07 17:41:47,361 - INFO - 实际视频时长: 5.50秒
2025-08-07 17:41:47,361 - INFO - 时长差异: 0.02秒 (0.42%)
2025-08-07 17:41:47,361 - INFO - ==========================================
2025-08-07 17:41:47,361 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:47,361 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\66_3.mp4
2025-08-07 17:41:47,362 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpvapc7q69
2025-08-07 17:41:47,409 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:47,409 - INFO -   - 音频时长: 5.48秒
2025-08-07 17:41:47,409 - INFO -   - 视频时长: 5.50秒
2025-08-07 17:41:47,409 - INFO -   - 时长差异: 0.02秒 (0.42%)
2025-08-07 17:41:47,409 - INFO - 
字幕 #66 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:47,409 - INFO - 生成的视频文件:
2025-08-07 17:41:47,409 - INFO -   1. F:/github/aicut_auto/newcut_ai\66_1.mp4
2025-08-07 17:41:47,409 - INFO -   2. F:/github/aicut_auto/newcut_ai\66_2.mp4
2025-08-07 17:41:47,409 - INFO -   3. F:/github/aicut_auto/newcut_ai\66_3.mp4
2025-08-07 17:41:47,409 - INFO - ========== 字幕 #66 处理结束 ==========

