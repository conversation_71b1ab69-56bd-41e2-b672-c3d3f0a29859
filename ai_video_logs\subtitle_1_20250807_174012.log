2025-08-07 17:40:12,953 - INFO - ========== 字幕 #1 处理开始 ==========
2025-08-07 17:40:12,953 - INFO - 字幕内容: 只差一步便可飞升成仙，女孩却因凡间楚家后辈作恶，被一道雷劫打落凡间，渡劫失败。
2025-08-07 17:40:12,953 - INFO - 字幕序号: [1, 5]
2025-08-07 17:40:12,953 - INFO - 音频文件详情:
2025-08-07 17:40:12,953 - INFO -   - 路径: output\1.wav
2025-08-07 17:40:12,953 - INFO -   - 时长: 6.33秒
2025-08-07 17:40:12,953 - INFO -   - 验证音频时长: 6.33秒
2025-08-07 17:40:12,953 - INFO - 字幕时间戳信息:
2025-08-07 17:40:12,954 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:12,954 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:12,954 - INFO -   - 根据生成的音频时长(6.33秒)已调整字幕时间戳
2025-08-07 17:40:12,954 - INFO - ========== 新模式：为字幕 #1 生成4套场景方案 ==========
2025-08-07 17:40:12,954 - INFO - 字幕序号列表: [1, 5]
2025-08-07 17:40:12,954 - INFO - 
--- 生成方案 #1：基于字幕序号 #1 ---
2025-08-07 17:40:12,954 - INFO - 开始为单个字幕序号 #1 匹配场景，目标时长: 6.33秒
2025-08-07 17:40:12,954 - INFO - 开始查找字幕序号 [1] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:12,954 - INFO - 找到related_overlap场景: scene_id=7, 字幕#1
2025-08-07 17:40:12,954 - INFO - 找到related_overlap场景: scene_id=8, 字幕#1
2025-08-07 17:40:12,954 - INFO - 找到related_between场景: scene_id=9, 字幕#1
2025-08-07 17:40:12,955 - INFO - 字幕 #1 找到 2 个overlap场景, 1 个between场景
2025-08-07 17:40:12,955 - INFO - 字幕序号 #1 找到 2 个可用overlap场景, 1 个可用between场景
2025-08-07 17:40:12,955 - INFO - 选择第一个overlap场景作为起点: scene_id=7
2025-08-07 17:40:12,955 - INFO - 添加起点场景: scene_id=7, 时长=1.68秒, 累计时长=1.68秒
2025-08-07 17:40:12,955 - INFO - 起点场景时长不足，需要延伸填充 4.65秒
2025-08-07 17:40:12,955 - INFO - 起点场景在原始列表中的索引: 6
2025-08-07 17:40:12,955 - INFO - 延伸添加场景: scene_id=8 (完整时长 2.28秒)
2025-08-07 17:40:12,955 - INFO - 累计时长: 3.96秒
2025-08-07 17:40:12,955 - INFO - 延伸添加场景: scene_id=9 (完整时长 1.24秒)
2025-08-07 17:40:12,955 - INFO - 累计时长: 5.20秒
2025-08-07 17:40:12,955 - INFO - 延伸添加场景: scene_id=10 (完整时长 0.68秒)
2025-08-07 17:40:12,955 - INFO - 累计时长: 5.88秒
2025-08-07 17:40:12,955 - INFO - 延伸添加场景: scene_id=11 (裁剪至 0.46秒)
2025-08-07 17:40:12,955 - INFO - 累计时长: 6.33秒
2025-08-07 17:40:12,955 - INFO - 字幕序号 #1 场景匹配完成，共选择 5 个场景，总时长: 6.33秒
2025-08-07 17:40:12,955 - INFO - 方案 #1 生成成功，包含 5 个场景
2025-08-07 17:40:12,955 - INFO - 新模式：第1套方案的 5 个场景已加入全局已使用集合
2025-08-07 17:40:12,955 - INFO - 
--- 生成方案 #2：基于字幕序号 #5 ---
2025-08-07 17:40:12,955 - INFO - 开始为单个字幕序号 #5 匹配场景，目标时长: 6.33秒
2025-08-07 17:40:12,955 - INFO - 开始查找字幕序号 [5] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:12,955 - INFO - 找到related_overlap场景: scene_id=22, 字幕#5
2025-08-07 17:40:12,956 - INFO - 找到related_between场景: scene_id=20, 字幕#5
2025-08-07 17:40:12,956 - INFO - 找到related_between场景: scene_id=21, 字幕#5
2025-08-07 17:40:12,956 - INFO - 字幕 #5 找到 1 个overlap场景, 2 个between场景
2025-08-07 17:40:12,956 - INFO - 字幕序号 #5 找到 1 个可用overlap场景, 2 个可用between场景
2025-08-07 17:40:12,956 - INFO - 选择第一个overlap场景作为起点: scene_id=22
2025-08-07 17:40:12,956 - INFO - 添加起点场景: scene_id=22, 时长=1.80秒, 累计时长=1.80秒
2025-08-07 17:40:12,956 - INFO - 起点场景时长不足，需要延伸填充 4.54秒
2025-08-07 17:40:12,956 - INFO - 起点场景在原始列表中的索引: 21
2025-08-07 17:40:12,956 - INFO - 延伸添加场景: scene_id=23 (完整时长 1.00秒)
2025-08-07 17:40:12,956 - INFO - 累计时长: 2.80秒
2025-08-07 17:40:12,956 - INFO - 延伸添加场景: scene_id=24 (完整时长 1.24秒)
2025-08-07 17:40:12,957 - INFO - 累计时长: 4.04秒
2025-08-07 17:40:12,957 - INFO - 延伸添加场景: scene_id=25 (完整时长 1.08秒)
2025-08-07 17:40:12,957 - INFO - 累计时长: 5.12秒
2025-08-07 17:40:12,957 - INFO - 延伸添加场景: scene_id=26 (裁剪至 1.22秒)
2025-08-07 17:40:12,957 - INFO - 累计时长: 6.33秒
2025-08-07 17:40:12,957 - INFO - 字幕序号 #5 场景匹配完成，共选择 5 个场景，总时长: 6.33秒
2025-08-07 17:40:12,957 - INFO - 方案 #2 生成成功，包含 5 个场景
2025-08-07 17:40:12,957 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:12,957 - INFO - ========== 当前模式：为字幕 #1 生成 1 套场景方案 ==========
2025-08-07 17:40:12,957 - INFO - 开始查找字幕序号 [1, 5] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:12,957 - INFO - 找到related_overlap场景: scene_id=7, 字幕#1
2025-08-07 17:40:12,957 - INFO - 找到related_overlap场景: scene_id=8, 字幕#1
2025-08-07 17:40:12,957 - INFO - 找到related_overlap场景: scene_id=22, 字幕#5
2025-08-07 17:40:12,957 - INFO - 找到related_between场景: scene_id=9, 字幕#1
2025-08-07 17:40:12,957 - INFO - 找到related_between场景: scene_id=20, 字幕#5
2025-08-07 17:40:12,957 - INFO - 找到related_between场景: scene_id=21, 字幕#5
2025-08-07 17:40:12,958 - INFO - 字幕 #1 找到 2 个overlap场景, 1 个between场景
2025-08-07 17:40:12,958 - INFO - 字幕 #5 找到 1 个overlap场景, 2 个between场景
2025-08-07 17:40:12,958 - INFO - 共收集 3 个未使用的overlap场景和 3 个未使用的between场景
2025-08-07 17:40:12,958 - INFO - 开始生成方案 #1
2025-08-07 17:40:12,958 - INFO - 方案 #1: 为字幕#1选择初始化overlap场景id=7
2025-08-07 17:40:12,958 - INFO - 方案 #1: 为字幕#5选择初始化overlap场景id=22
2025-08-07 17:40:12,958 - INFO - 方案 #1: 初始选择后，当前总时长=3.48秒
2025-08-07 17:40:12,958 - INFO - 方案 #1: 额外添加overlap场景id=8, 当前总时长=5.76秒
2025-08-07 17:40:12,958 - INFO - 方案 #1: 额外between选择后，当前总时长=5.76秒
2025-08-07 17:40:12,958 - INFO - 方案 #1: 额外添加between场景id=20, 当前总时长=8.08秒
2025-08-07 17:40:12,958 - INFO - 方案 #1: 场景总时长(8.08秒)大于音频时长(6.33秒)，需要裁剪
2025-08-07 17:40:12,958 - INFO - 调整前总时长: 8.08秒, 目标时长: 6.33秒
2025-08-07 17:40:12,958 - INFO - 需要裁剪 1.74秒
2025-08-07 17:40:12,958 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-08-07 17:40:12,958 - INFO - 裁剪场景ID=20：从2.32秒裁剪至1.00秒
2025-08-07 17:40:12,958 - INFO - 裁剪场景ID=8：从2.28秒裁剪至1.86秒
2025-08-07 17:40:12,958 - INFO - 调整后总时长: 6.33秒，与目标时长差异: 0.00秒
2025-08-07 17:40:12,958 - INFO - 方案 #1 调整/填充后最终总时长: 6.33秒
2025-08-07 17:40:12,958 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:12,958 - INFO - ========== 当前模式：字幕 #1 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:12,958 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:12,958 - INFO - ========== 新模式：字幕 #1 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:12,958 - INFO - 
----- 处理字幕 #1 的方案 #1 -----
2025-08-07 17:40:12,958 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\1_1.mp4
2025-08-07 17:40:12,969 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjkz8k4gv
2025-08-07 17:40:12,969 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\7.mp4 (确认存在: True)
2025-08-07 17:40:12,969 - INFO - 添加场景ID=7，时长=1.68秒，累计时长=1.68秒
2025-08-07 17:40:12,969 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\8.mp4 (确认存在: True)
2025-08-07 17:40:12,969 - INFO - 添加场景ID=8，时长=2.28秒，累计时长=3.96秒
2025-08-07 17:40:12,970 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\9.mp4 (确认存在: True)
2025-08-07 17:40:12,970 - INFO - 添加场景ID=9，时长=1.24秒，累计时长=5.20秒
2025-08-07 17:40:12,970 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\10.mp4 (确认存在: True)
2025-08-07 17:40:12,970 - INFO - 添加场景ID=10，时长=0.68秒，累计时长=5.88秒
2025-08-07 17:40:12,970 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\11.mp4 (确认存在: True)
2025-08-07 17:40:12,970 - INFO - 添加场景ID=11，时长=1.52秒，累计时长=7.40秒
2025-08-07 17:40:12,970 - INFO - 准备合并 5 个场景文件，总时长约 7.40秒
2025-08-07 17:40:12,970 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/7.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/8.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/9.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/10.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/11.mp4'

2025-08-07 17:40:12,970 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpjkz8k4gv\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpjkz8k4gv\temp_combined.mp4
2025-08-07 17:40:13,125 - INFO - 合并后的视频时长: 7.52秒，目标音频时长: 6.33秒
2025-08-07 17:40:13,127 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpjkz8k4gv\temp_combined.mp4 -ss 0 -to 6.334 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\1_1.mp4
2025-08-07 17:40:13,470 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:13,470 - INFO - 目标音频时长: 6.33秒
2025-08-07 17:40:13,470 - INFO - 实际视频时长: 6.38秒
2025-08-07 17:40:13,470 - INFO - 时长差异: 0.05秒 (0.77%)
2025-08-07 17:40:13,470 - INFO - ==========================================
2025-08-07 17:40:13,470 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:13,470 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\1_1.mp4
2025-08-07 17:40:13,470 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjkz8k4gv
2025-08-07 17:40:13,514 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:13,514 - INFO -   - 音频时长: 6.33秒
2025-08-07 17:40:13,514 - INFO -   - 视频时长: 6.38秒
2025-08-07 17:40:13,514 - INFO -   - 时长差异: 0.05秒 (0.77%)
2025-08-07 17:40:13,514 - INFO - 
----- 处理字幕 #1 的方案 #2 -----
2025-08-07 17:40:13,514 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\1_2.mp4
2025-08-07 17:40:13,515 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsx36qkzm
2025-08-07 17:40:13,515 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\22.mp4 (确认存在: True)
2025-08-07 17:40:13,515 - INFO - 添加场景ID=22，时长=1.80秒，累计时长=1.80秒
2025-08-07 17:40:13,515 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\23.mp4 (确认存在: True)
2025-08-07 17:40:13,515 - INFO - 添加场景ID=23，时长=1.00秒，累计时长=2.80秒
2025-08-07 17:40:13,515 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\24.mp4 (确认存在: True)
2025-08-07 17:40:13,515 - INFO - 添加场景ID=24，时长=1.24秒，累计时长=4.04秒
2025-08-07 17:40:13,515 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\25.mp4 (确认存在: True)
2025-08-07 17:40:13,515 - INFO - 添加场景ID=25，时长=1.08秒，累计时长=5.12秒
2025-08-07 17:40:13,515 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\26.mp4 (确认存在: True)
2025-08-07 17:40:13,515 - INFO - 添加场景ID=26，时长=1.32秒，累计时长=6.44秒
2025-08-07 17:40:13,515 - INFO - 准备合并 5 个场景文件，总时长约 6.44秒
2025-08-07 17:40:13,515 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/22.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/23.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/24.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/25.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/26.mp4'

2025-08-07 17:40:13,517 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpsx36qkzm\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpsx36qkzm\temp_combined.mp4
2025-08-07 17:40:13,698 - INFO - 合并后的视频时长: 6.56秒，目标音频时长: 6.33秒
2025-08-07 17:40:13,698 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpsx36qkzm\temp_combined.mp4 -ss 0 -to 6.334 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\1_2.mp4
2025-08-07 17:40:14,058 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:14,058 - INFO - 目标音频时长: 6.33秒
2025-08-07 17:40:14,058 - INFO - 实际视频时长: 6.38秒
2025-08-07 17:40:14,058 - INFO - 时长差异: 0.05秒 (0.77%)
2025-08-07 17:40:14,058 - INFO - ==========================================
2025-08-07 17:40:14,058 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:14,058 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\1_2.mp4
2025-08-07 17:40:14,059 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsx36qkzm
2025-08-07 17:40:14,103 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:14,103 - INFO -   - 音频时长: 6.33秒
2025-08-07 17:40:14,103 - INFO -   - 视频时长: 6.38秒
2025-08-07 17:40:14,103 - INFO -   - 时长差异: 0.05秒 (0.77%)
2025-08-07 17:40:14,103 - INFO - 
----- 处理字幕 #1 的方案 #3 -----
2025-08-07 17:40:14,103 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\1_3.mp4
2025-08-07 17:40:14,103 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpwz95i9ty
2025-08-07 17:40:14,104 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\7.mp4 (确认存在: True)
2025-08-07 17:40:14,104 - INFO - 添加场景ID=7，时长=1.68秒，累计时长=1.68秒
2025-08-07 17:40:14,104 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\22.mp4 (确认存在: True)
2025-08-07 17:40:14,104 - INFO - 添加场景ID=22，时长=1.80秒，累计时长=3.48秒
2025-08-07 17:40:14,104 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\8.mp4 (确认存在: True)
2025-08-07 17:40:14,104 - INFO - 添加场景ID=8，时长=2.28秒，累计时长=5.76秒
2025-08-07 17:40:14,104 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\20.mp4 (确认存在: True)
2025-08-07 17:40:14,104 - INFO - 添加场景ID=20，时长=2.32秒，累计时长=8.08秒
2025-08-07 17:40:14,104 - INFO - 准备合并 4 个场景文件，总时长约 8.08秒
2025-08-07 17:40:14,104 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/7.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/22.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/8.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/20.mp4'

2025-08-07 17:40:14,104 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpwz95i9ty\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpwz95i9ty\temp_combined.mp4
2025-08-07 17:40:14,265 - INFO - 合并后的视频时长: 8.17秒，目标音频时长: 6.33秒
2025-08-07 17:40:14,265 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpwz95i9ty\temp_combined.mp4 -ss 0 -to 6.334 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\1_3.mp4
2025-08-07 17:40:14,615 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:14,616 - INFO - 目标音频时长: 6.33秒
2025-08-07 17:40:14,616 - INFO - 实际视频时长: 6.38秒
2025-08-07 17:40:14,616 - INFO - 时长差异: 0.05秒 (0.77%)
2025-08-07 17:40:14,616 - INFO - ==========================================
2025-08-07 17:40:14,616 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:14,616 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\1_3.mp4
2025-08-07 17:40:14,617 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpwz95i9ty
2025-08-07 17:40:14,660 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:14,660 - INFO -   - 音频时长: 6.33秒
2025-08-07 17:40:14,660 - INFO -   - 视频时长: 6.38秒
2025-08-07 17:40:14,660 - INFO -   - 时长差异: 0.05秒 (0.77%)
2025-08-07 17:40:14,660 - INFO - 
字幕 #1 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:14,660 - INFO - 生成的视频文件:
2025-08-07 17:40:14,660 - INFO -   1. F:/github/aicut_auto/newcut_ai\1_1.mp4
2025-08-07 17:40:14,660 - INFO -   2. F:/github/aicut_auto/newcut_ai\1_2.mp4
2025-08-07 17:40:14,660 - INFO -   3. F:/github/aicut_auto/newcut_ai\1_3.mp4
2025-08-07 17:40:14,660 - INFO - ========== 字幕 #1 处理结束 ==========

