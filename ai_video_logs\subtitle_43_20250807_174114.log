2025-08-07 17:41:14,347 - INFO - ========== 字幕 #43 处理开始 ==========
2025-08-07 17:41:14,347 - INFO - 字幕内容: 面对众人，心机女还想用假怀孕脱罪，女孩却让爷爷当场为她把脉，谎言瞬间被揭穿。
2025-08-07 17:41:14,347 - INFO - 字幕序号: [1746, 1762]
2025-08-07 17:41:14,347 - INFO - 音频文件详情:
2025-08-07 17:41:14,347 - INFO -   - 路径: output\43.wav
2025-08-07 17:41:14,347 - INFO -   - 时长: 6.34秒
2025-08-07 17:41:14,347 - INFO -   - 验证音频时长: 6.34秒
2025-08-07 17:41:14,347 - INFO - 字幕时间戳信息:
2025-08-07 17:41:14,349 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:14,349 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:14,349 - INFO -   - 根据生成的音频时长(6.34秒)已调整字幕时间戳
2025-08-07 17:41:14,349 - INFO - ========== 新模式：为字幕 #43 生成4套场景方案 ==========
2025-08-07 17:41:14,349 - INFO - 字幕序号列表: [1746, 1762]
2025-08-07 17:41:14,349 - INFO - 
--- 生成方案 #1：基于字幕序号 #1746 ---
2025-08-07 17:41:14,349 - INFO - 开始为单个字幕序号 #1746 匹配场景，目标时长: 6.34秒
2025-08-07 17:41:14,349 - INFO - 开始查找字幕序号 [1746] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:14,349 - INFO - 找到related_overlap场景: scene_id=1956, 字幕#1746
2025-08-07 17:41:14,350 - INFO - 字幕 #1746 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:14,350 - INFO - 字幕序号 #1746 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:14,350 - INFO - 选择第一个overlap场景作为起点: scene_id=1956
2025-08-07 17:41:14,350 - INFO - 添加起点场景: scene_id=1956, 时长=4.12秒, 累计时长=4.12秒
2025-08-07 17:41:14,350 - INFO - 起点场景时长不足，需要延伸填充 2.22秒
2025-08-07 17:41:14,350 - INFO - 起点场景在原始列表中的索引: 1955
2025-08-07 17:41:14,350 - INFO - 延伸添加场景: scene_id=1957 (完整时长 1.24秒)
2025-08-07 17:41:14,350 - INFO - 累计时长: 5.36秒
2025-08-07 17:41:14,350 - INFO - 延伸添加场景: scene_id=1958 (裁剪至 0.98秒)
2025-08-07 17:41:14,350 - INFO - 累计时长: 6.34秒
2025-08-07 17:41:14,350 - INFO - 字幕序号 #1746 场景匹配完成，共选择 3 个场景，总时长: 6.34秒
2025-08-07 17:41:14,350 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-08-07 17:41:14,350 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-08-07 17:41:14,350 - INFO - 
--- 生成方案 #2：基于字幕序号 #1762 ---
2025-08-07 17:41:14,350 - INFO - 开始为单个字幕序号 #1762 匹配场景，目标时长: 6.34秒
2025-08-07 17:41:14,350 - INFO - 开始查找字幕序号 [1762] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:14,351 - INFO - 找到related_overlap场景: scene_id=1974, 字幕#1762
2025-08-07 17:41:14,351 - INFO - 找到related_overlap场景: scene_id=1975, 字幕#1762
2025-08-07 17:41:14,351 - INFO - 字幕 #1762 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:14,351 - INFO - 字幕序号 #1762 找到 2 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:14,351 - INFO - 选择第一个overlap场景作为起点: scene_id=1974
2025-08-07 17:41:14,351 - INFO - 添加起点场景: scene_id=1974, 时长=1.16秒, 累计时长=1.16秒
2025-08-07 17:41:14,351 - INFO - 起点场景时长不足，需要延伸填充 5.18秒
2025-08-07 17:41:14,352 - INFO - 起点场景在原始列表中的索引: 1973
2025-08-07 17:41:14,352 - INFO - 延伸添加场景: scene_id=1975 (完整时长 2.20秒)
2025-08-07 17:41:14,352 - INFO - 累计时长: 3.36秒
2025-08-07 17:41:14,352 - INFO - 延伸添加场景: scene_id=1976 (完整时长 0.92秒)
2025-08-07 17:41:14,352 - INFO - 累计时长: 4.28秒
2025-08-07 17:41:14,352 - INFO - 延伸添加场景: scene_id=1977 (完整时长 1.28秒)
2025-08-07 17:41:14,352 - INFO - 累计时长: 5.56秒
2025-08-07 17:41:14,352 - INFO - 延伸添加场景: scene_id=1978 (裁剪至 0.78秒)
2025-08-07 17:41:14,352 - INFO - 累计时长: 6.34秒
2025-08-07 17:41:14,352 - INFO - 字幕序号 #1762 场景匹配完成，共选择 5 个场景，总时长: 6.34秒
2025-08-07 17:41:14,352 - INFO - 方案 #2 生成成功，包含 5 个场景
2025-08-07 17:41:14,352 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:14,352 - INFO - ========== 当前模式：为字幕 #43 生成 1 套场景方案 ==========
2025-08-07 17:41:14,352 - INFO - 开始查找字幕序号 [1746, 1762] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:14,352 - INFO - 找到related_overlap场景: scene_id=1956, 字幕#1746
2025-08-07 17:41:14,352 - INFO - 找到related_overlap场景: scene_id=1974, 字幕#1762
2025-08-07 17:41:14,352 - INFO - 找到related_overlap场景: scene_id=1975, 字幕#1762
2025-08-07 17:41:14,353 - INFO - 字幕 #1746 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:14,353 - INFO - 字幕 #1762 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:14,353 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-08-07 17:41:14,353 - INFO - 开始生成方案 #1
2025-08-07 17:41:14,353 - INFO - 方案 #1: 为字幕#1746选择初始化overlap场景id=1956
2025-08-07 17:41:14,353 - INFO - 方案 #1: 为字幕#1762选择初始化overlap场景id=1974
2025-08-07 17:41:14,353 - INFO - 方案 #1: 初始选择后，当前总时长=5.28秒
2025-08-07 17:41:14,353 - INFO - 方案 #1: 额外添加overlap场景id=1975, 当前总时长=7.48秒
2025-08-07 17:41:14,353 - INFO - 方案 #1: 额外between选择后，当前总时长=7.48秒
2025-08-07 17:41:14,353 - INFO - 方案 #1: 场景总时长(7.48秒)大于音频时长(6.34秒)，需要裁剪
2025-08-07 17:41:14,353 - INFO - 调整前总时长: 7.48秒, 目标时长: 6.34秒
2025-08-07 17:41:14,353 - INFO - 需要裁剪 1.14秒
2025-08-07 17:41:14,353 - INFO - 裁剪最长场景ID=1956：从4.12秒裁剪至2.98秒
2025-08-07 17:41:14,353 - INFO - 调整后总时长: 6.34秒，与目标时长差异: 0.00秒
2025-08-07 17:41:14,353 - INFO - 方案 #1 调整/填充后最终总时长: 6.34秒
2025-08-07 17:41:14,353 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:14,353 - INFO - ========== 当前模式：字幕 #43 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:14,353 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:14,353 - INFO - ========== 新模式：字幕 #43 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:14,353 - INFO - 
----- 处理字幕 #43 的方案 #1 -----
2025-08-07 17:41:14,353 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\43_1.mp4
2025-08-07 17:41:14,354 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpi8qnjanv
2025-08-07 17:41:14,354 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1956.mp4 (确认存在: True)
2025-08-07 17:41:14,354 - INFO - 添加场景ID=1956，时长=4.12秒，累计时长=4.12秒
2025-08-07 17:41:14,354 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1957.mp4 (确认存在: True)
2025-08-07 17:41:14,354 - INFO - 添加场景ID=1957，时长=1.24秒，累计时长=5.36秒
2025-08-07 17:41:14,354 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1958.mp4 (确认存在: True)
2025-08-07 17:41:14,354 - INFO - 添加场景ID=1958，时长=2.08秒，累计时长=7.44秒
2025-08-07 17:41:14,354 - INFO - 准备合并 3 个场景文件，总时长约 7.44秒
2025-08-07 17:41:14,354 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1956.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1957.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1958.mp4'

2025-08-07 17:41:14,355 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpi8qnjanv\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpi8qnjanv\temp_combined.mp4
2025-08-07 17:41:14,501 - INFO - 合并后的视频时长: 7.51秒，目标音频时长: 6.34秒
2025-08-07 17:41:14,501 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpi8qnjanv\temp_combined.mp4 -ss 0 -to 6.34 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\43_1.mp4
2025-08-07 17:41:14,826 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:14,826 - INFO - 目标音频时长: 6.34秒
2025-08-07 17:41:14,826 - INFO - 实际视频时长: 6.38秒
2025-08-07 17:41:14,826 - INFO - 时长差异: 0.04秒 (0.68%)
2025-08-07 17:41:14,826 - INFO - ==========================================
2025-08-07 17:41:14,826 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:14,826 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\43_1.mp4
2025-08-07 17:41:14,827 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpi8qnjanv
2025-08-07 17:41:14,869 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:14,869 - INFO -   - 音频时长: 6.34秒
2025-08-07 17:41:14,869 - INFO -   - 视频时长: 6.38秒
2025-08-07 17:41:14,869 - INFO -   - 时长差异: 0.04秒 (0.68%)
2025-08-07 17:41:14,869 - INFO - 
----- 处理字幕 #43 的方案 #2 -----
2025-08-07 17:41:14,869 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\43_2.mp4
2025-08-07 17:41:14,870 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp53qql7gq
2025-08-07 17:41:14,871 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1974.mp4 (确认存在: True)
2025-08-07 17:41:14,871 - INFO - 添加场景ID=1974，时长=1.16秒，累计时长=1.16秒
2025-08-07 17:41:14,871 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1975.mp4 (确认存在: True)
2025-08-07 17:41:14,871 - INFO - 添加场景ID=1975，时长=2.20秒，累计时长=3.36秒
2025-08-07 17:41:14,871 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1976.mp4 (确认存在: True)
2025-08-07 17:41:14,871 - INFO - 添加场景ID=1976，时长=0.92秒，累计时长=4.28秒
2025-08-07 17:41:14,871 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1977.mp4 (确认存在: True)
2025-08-07 17:41:14,871 - INFO - 添加场景ID=1977，时长=1.28秒，累计时长=5.56秒
2025-08-07 17:41:14,871 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1978.mp4 (确认存在: True)
2025-08-07 17:41:14,871 - INFO - 添加场景ID=1978，时长=3.12秒，累计时长=8.68秒
2025-08-07 17:41:14,871 - INFO - 准备合并 5 个场景文件，总时长约 8.68秒
2025-08-07 17:41:14,871 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1974.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1975.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1976.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1977.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1978.mp4'

2025-08-07 17:41:14,871 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp53qql7gq\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp53qql7gq\temp_combined.mp4
2025-08-07 17:41:15,034 - INFO - 合并后的视频时长: 8.80秒，目标音频时长: 6.34秒
2025-08-07 17:41:15,034 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp53qql7gq\temp_combined.mp4 -ss 0 -to 6.34 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\43_2.mp4
2025-08-07 17:41:15,373 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:15,373 - INFO - 目标音频时长: 6.34秒
2025-08-07 17:41:15,373 - INFO - 实际视频时长: 6.38秒
2025-08-07 17:41:15,373 - INFO - 时长差异: 0.04秒 (0.68%)
2025-08-07 17:41:15,373 - INFO - ==========================================
2025-08-07 17:41:15,373 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:15,373 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\43_2.mp4
2025-08-07 17:41:15,374 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp53qql7gq
2025-08-07 17:41:15,417 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:15,417 - INFO -   - 音频时长: 6.34秒
2025-08-07 17:41:15,417 - INFO -   - 视频时长: 6.38秒
2025-08-07 17:41:15,417 - INFO -   - 时长差异: 0.04秒 (0.68%)
2025-08-07 17:41:15,417 - INFO - 
----- 处理字幕 #43 的方案 #3 -----
2025-08-07 17:41:15,417 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\43_3.mp4
2025-08-07 17:41:15,418 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1gbymad0
2025-08-07 17:41:15,418 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1956.mp4 (确认存在: True)
2025-08-07 17:41:15,418 - INFO - 添加场景ID=1956，时长=4.12秒，累计时长=4.12秒
2025-08-07 17:41:15,418 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1974.mp4 (确认存在: True)
2025-08-07 17:41:15,418 - INFO - 添加场景ID=1974，时长=1.16秒，累计时长=5.28秒
2025-08-07 17:41:15,418 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1975.mp4 (确认存在: True)
2025-08-07 17:41:15,418 - INFO - 添加场景ID=1975，时长=2.20秒，累计时长=7.48秒
2025-08-07 17:41:15,419 - INFO - 准备合并 3 个场景文件，总时长约 7.48秒
2025-08-07 17:41:15,419 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1956.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1974.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1975.mp4'

2025-08-07 17:41:15,419 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp1gbymad0\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp1gbymad0\temp_combined.mp4
2025-08-07 17:41:15,552 - INFO - 合并后的视频时长: 7.55秒，目标音频时长: 6.34秒
2025-08-07 17:41:15,552 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp1gbymad0\temp_combined.mp4 -ss 0 -to 6.34 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\43_3.mp4
2025-08-07 17:41:15,870 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:15,870 - INFO - 目标音频时长: 6.34秒
2025-08-07 17:41:15,871 - INFO - 实际视频时长: 6.38秒
2025-08-07 17:41:15,871 - INFO - 时长差异: 0.04秒 (0.68%)
2025-08-07 17:41:15,871 - INFO - ==========================================
2025-08-07 17:41:15,871 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:15,871 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\43_3.mp4
2025-08-07 17:41:15,871 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1gbymad0
2025-08-07 17:41:15,915 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:15,915 - INFO -   - 音频时长: 6.34秒
2025-08-07 17:41:15,915 - INFO -   - 视频时长: 6.38秒
2025-08-07 17:41:15,915 - INFO -   - 时长差异: 0.04秒 (0.68%)
2025-08-07 17:41:15,916 - INFO - 
字幕 #43 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:15,916 - INFO - 生成的视频文件:
2025-08-07 17:41:15,916 - INFO -   1. F:/github/aicut_auto/newcut_ai\43_1.mp4
2025-08-07 17:41:15,916 - INFO -   2. F:/github/aicut_auto/newcut_ai\43_2.mp4
2025-08-07 17:41:15,916 - INFO -   3. F:/github/aicut_auto/newcut_ai\43_3.mp4
2025-08-07 17:41:15,916 - INFO - ========== 字幕 #43 处理结束 ==========

