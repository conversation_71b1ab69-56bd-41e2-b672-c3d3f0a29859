2025-08-07 17:41:07,256 - INFO - ========== 字幕 #38 处理开始 ==========
2025-08-07 17:41:07,256 - INFO - 字幕内容: 女孩懒得跟她废话，直接一张符咒贴在她额头，让她当众说出了实话。
2025-08-07 17:41:07,256 - INFO - 字幕序号: [1370, 1376]
2025-08-07 17:41:07,256 - INFO - 音频文件详情:
2025-08-07 17:41:07,256 - INFO -   - 路径: output\38.wav
2025-08-07 17:41:07,256 - INFO -   - 时长: 4.05秒
2025-08-07 17:41:07,258 - INFO -   - 验证音频时长: 4.05秒
2025-08-07 17:41:07,258 - INFO - 字幕时间戳信息:
2025-08-07 17:41:07,258 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:07,258 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:07,258 - INFO -   - 根据生成的音频时长(4.05秒)已调整字幕时间戳
2025-08-07 17:41:07,258 - INFO - ========== 新模式：为字幕 #38 生成4套场景方案 ==========
2025-08-07 17:41:07,258 - INFO - 字幕序号列表: [1370, 1376]
2025-08-07 17:41:07,258 - INFO - 
--- 生成方案 #1：基于字幕序号 #1370 ---
2025-08-07 17:41:07,258 - INFO - 开始为单个字幕序号 #1370 匹配场景，目标时长: 4.05秒
2025-08-07 17:41:07,258 - INFO - 开始查找字幕序号 [1370] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:07,258 - INFO - 找到related_overlap场景: scene_id=1574, 字幕#1370
2025-08-07 17:41:07,258 - INFO - 找到related_overlap场景: scene_id=1575, 字幕#1370
2025-08-07 17:41:07,259 - INFO - 字幕 #1370 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:07,259 - INFO - 字幕序号 #1370 找到 2 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:07,259 - INFO - 选择第一个overlap场景作为起点: scene_id=1574
2025-08-07 17:41:07,259 - INFO - 添加起点场景: scene_id=1574, 时长=0.96秒, 累计时长=0.96秒
2025-08-07 17:41:07,259 - INFO - 起点场景时长不足，需要延伸填充 3.09秒
2025-08-07 17:41:07,259 - INFO - 起点场景在原始列表中的索引: 1573
2025-08-07 17:41:07,259 - INFO - 延伸添加场景: scene_id=1575 (完整时长 1.04秒)
2025-08-07 17:41:07,259 - INFO - 累计时长: 2.00秒
2025-08-07 17:41:07,259 - INFO - 延伸添加场景: scene_id=1576 (完整时长 0.92秒)
2025-08-07 17:41:07,259 - INFO - 累计时长: 2.92秒
2025-08-07 17:41:07,259 - INFO - 延伸添加场景: scene_id=1577 (裁剪至 1.13秒)
2025-08-07 17:41:07,259 - INFO - 累计时长: 4.05秒
2025-08-07 17:41:07,259 - INFO - 字幕序号 #1370 场景匹配完成，共选择 4 个场景，总时长: 4.05秒
2025-08-07 17:41:07,259 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-08-07 17:41:07,259 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-08-07 17:41:07,259 - INFO - 
--- 生成方案 #2：基于字幕序号 #1376 ---
2025-08-07 17:41:07,259 - INFO - 开始为单个字幕序号 #1376 匹配场景，目标时长: 4.05秒
2025-08-07 17:41:07,259 - INFO - 开始查找字幕序号 [1376] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:07,259 - INFO - 找到related_overlap场景: scene_id=1585, 字幕#1376
2025-08-07 17:41:07,260 - INFO - 字幕 #1376 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:07,260 - INFO - 字幕序号 #1376 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:07,260 - INFO - 选择第一个overlap场景作为起点: scene_id=1585
2025-08-07 17:41:07,260 - INFO - 添加起点场景: scene_id=1585, 时长=2.92秒, 累计时长=2.92秒
2025-08-07 17:41:07,260 - INFO - 起点场景时长不足，需要延伸填充 1.13秒
2025-08-07 17:41:07,260 - INFO - 起点场景在原始列表中的索引: 1584
2025-08-07 17:41:07,260 - INFO - 延伸添加场景: scene_id=1586 (裁剪至 1.13秒)
2025-08-07 17:41:07,260 - INFO - 累计时长: 4.05秒
2025-08-07 17:41:07,260 - INFO - 字幕序号 #1376 场景匹配完成，共选择 2 个场景，总时长: 4.05秒
2025-08-07 17:41:07,260 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-08-07 17:41:07,260 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:07,260 - INFO - ========== 当前模式：为字幕 #38 生成 1 套场景方案 ==========
2025-08-07 17:41:07,260 - INFO - 开始查找字幕序号 [1370, 1376] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:07,261 - INFO - 找到related_overlap场景: scene_id=1574, 字幕#1370
2025-08-07 17:41:07,261 - INFO - 找到related_overlap场景: scene_id=1575, 字幕#1370
2025-08-07 17:41:07,261 - INFO - 找到related_overlap场景: scene_id=1585, 字幕#1376
2025-08-07 17:41:07,261 - INFO - 字幕 #1370 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:07,261 - INFO - 字幕 #1376 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:07,261 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-08-07 17:41:07,261 - INFO - 开始生成方案 #1
2025-08-07 17:41:07,261 - INFO - 方案 #1: 为字幕#1370选择初始化overlap场景id=1574
2025-08-07 17:41:07,261 - INFO - 方案 #1: 为字幕#1376选择初始化overlap场景id=1585
2025-08-07 17:41:07,261 - INFO - 方案 #1: 初始选择后，当前总时长=3.88秒
2025-08-07 17:41:07,261 - INFO - 方案 #1: 额外添加overlap场景id=1575, 当前总时长=4.92秒
2025-08-07 17:41:07,261 - INFO - 方案 #1: 额外between选择后，当前总时长=4.92秒
2025-08-07 17:41:07,261 - INFO - 方案 #1: 场景总时长(4.92秒)大于音频时长(4.05秒)，需要裁剪
2025-08-07 17:41:07,262 - INFO - 调整前总时长: 4.92秒, 目标时长: 4.05秒
2025-08-07 17:41:07,262 - INFO - 需要裁剪 0.87秒
2025-08-07 17:41:07,262 - INFO - 裁剪最长场景ID=1585：从2.92秒裁剪至2.05秒
2025-08-07 17:41:07,262 - INFO - 调整后总时长: 4.05秒，与目标时长差异: 0.00秒
2025-08-07 17:41:07,262 - INFO - 方案 #1 调整/填充后最终总时长: 4.05秒
2025-08-07 17:41:07,262 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:07,262 - INFO - ========== 当前模式：字幕 #38 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:07,262 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:07,262 - INFO - ========== 新模式：字幕 #38 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:07,262 - INFO - 
----- 处理字幕 #38 的方案 #1 -----
2025-08-07 17:41:07,262 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\38_1.mp4
2025-08-07 17:41:07,262 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpaxydo7g3
2025-08-07 17:41:07,262 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1574.mp4 (确认存在: True)
2025-08-07 17:41:07,263 - INFO - 添加场景ID=1574，时长=0.96秒，累计时长=0.96秒
2025-08-07 17:41:07,263 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1575.mp4 (确认存在: True)
2025-08-07 17:41:07,263 - INFO - 添加场景ID=1575，时长=1.04秒，累计时长=2.00秒
2025-08-07 17:41:07,263 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1576.mp4 (确认存在: True)
2025-08-07 17:41:07,263 - INFO - 添加场景ID=1576，时长=0.92秒，累计时长=2.92秒
2025-08-07 17:41:07,263 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1577.mp4 (确认存在: True)
2025-08-07 17:41:07,263 - INFO - 添加场景ID=1577，时长=1.28秒，累计时长=4.20秒
2025-08-07 17:41:07,263 - INFO - 准备合并 4 个场景文件，总时长约 4.20秒
2025-08-07 17:41:07,263 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1574.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1575.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1576.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1577.mp4'

2025-08-07 17:41:07,263 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpaxydo7g3\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpaxydo7g3\temp_combined.mp4
2025-08-07 17:41:07,424 - INFO - 合并后的视频时长: 4.29秒，目标音频时长: 4.05秒
2025-08-07 17:41:07,424 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpaxydo7g3\temp_combined.mp4 -ss 0 -to 4.051 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\38_1.mp4
2025-08-07 17:41:07,705 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:07,705 - INFO - 目标音频时长: 4.05秒
2025-08-07 17:41:07,705 - INFO - 实际视频时长: 4.10秒
2025-08-07 17:41:07,705 - INFO - 时长差异: 0.05秒 (1.28%)
2025-08-07 17:41:07,705 - INFO - ==========================================
2025-08-07 17:41:07,705 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:07,705 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\38_1.mp4
2025-08-07 17:41:07,705 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpaxydo7g3
2025-08-07 17:41:07,749 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:07,749 - INFO -   - 音频时长: 4.05秒
2025-08-07 17:41:07,749 - INFO -   - 视频时长: 4.10秒
2025-08-07 17:41:07,749 - INFO -   - 时长差异: 0.05秒 (1.28%)
2025-08-07 17:41:07,749 - INFO - 
----- 处理字幕 #38 的方案 #2 -----
2025-08-07 17:41:07,749 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\38_2.mp4
2025-08-07 17:41:07,749 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsmydf2ps
2025-08-07 17:41:07,749 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1585.mp4 (确认存在: True)
2025-08-07 17:41:07,750 - INFO - 添加场景ID=1585，时长=2.92秒，累计时长=2.92秒
2025-08-07 17:41:07,750 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1586.mp4 (确认存在: True)
2025-08-07 17:41:07,750 - INFO - 添加场景ID=1586，时长=2.56秒，累计时长=5.48秒
2025-08-07 17:41:07,750 - INFO - 准备合并 2 个场景文件，总时长约 5.48秒
2025-08-07 17:41:07,750 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1585.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1586.mp4'

2025-08-07 17:41:07,750 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpsmydf2ps\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpsmydf2ps\temp_combined.mp4
2025-08-07 17:41:07,876 - INFO - 合并后的视频时长: 5.53秒，目标音频时长: 4.05秒
2025-08-07 17:41:07,876 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpsmydf2ps\temp_combined.mp4 -ss 0 -to 4.051 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\38_2.mp4
2025-08-07 17:41:08,136 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:08,136 - INFO - 目标音频时长: 4.05秒
2025-08-07 17:41:08,136 - INFO - 实际视频时长: 4.10秒
2025-08-07 17:41:08,136 - INFO - 时长差异: 0.05秒 (1.28%)
2025-08-07 17:41:08,136 - INFO - ==========================================
2025-08-07 17:41:08,136 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:08,136 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\38_2.mp4
2025-08-07 17:41:08,136 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsmydf2ps
2025-08-07 17:41:08,180 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:08,180 - INFO -   - 音频时长: 4.05秒
2025-08-07 17:41:08,180 - INFO -   - 视频时长: 4.10秒
2025-08-07 17:41:08,180 - INFO -   - 时长差异: 0.05秒 (1.28%)
2025-08-07 17:41:08,180 - INFO - 
----- 处理字幕 #38 的方案 #3 -----
2025-08-07 17:41:08,180 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\38_3.mp4
2025-08-07 17:41:08,180 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp7bfincs5
2025-08-07 17:41:08,180 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1574.mp4 (确认存在: True)
2025-08-07 17:41:08,180 - INFO - 添加场景ID=1574，时长=0.96秒，累计时长=0.96秒
2025-08-07 17:41:08,181 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1585.mp4 (确认存在: True)
2025-08-07 17:41:08,181 - INFO - 添加场景ID=1585，时长=2.92秒，累计时长=3.88秒
2025-08-07 17:41:08,181 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1575.mp4 (确认存在: True)
2025-08-07 17:41:08,181 - INFO - 添加场景ID=1575，时长=1.04秒，累计时长=4.92秒
2025-08-07 17:41:08,181 - INFO - 准备合并 3 个场景文件，总时长约 4.92秒
2025-08-07 17:41:08,181 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1574.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1585.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1575.mp4'

2025-08-07 17:41:08,181 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp7bfincs5\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp7bfincs5\temp_combined.mp4
2025-08-07 17:41:08,305 - INFO - 合并后的视频时长: 4.99秒，目标音频时长: 4.05秒
2025-08-07 17:41:08,305 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp7bfincs5\temp_combined.mp4 -ss 0 -to 4.051 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\38_3.mp4
2025-08-07 17:41:08,576 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:08,576 - INFO - 目标音频时长: 4.05秒
2025-08-07 17:41:08,576 - INFO - 实际视频时长: 4.10秒
2025-08-07 17:41:08,576 - INFO - 时长差异: 0.05秒 (1.28%)
2025-08-07 17:41:08,576 - INFO - ==========================================
2025-08-07 17:41:08,576 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:08,576 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\38_3.mp4
2025-08-07 17:41:08,576 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp7bfincs5
2025-08-07 17:41:08,620 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:08,620 - INFO -   - 音频时长: 4.05秒
2025-08-07 17:41:08,620 - INFO -   - 视频时长: 4.10秒
2025-08-07 17:41:08,621 - INFO -   - 时长差异: 0.05秒 (1.28%)
2025-08-07 17:41:08,621 - INFO - 
字幕 #38 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:08,621 - INFO - 生成的视频文件:
2025-08-07 17:41:08,621 - INFO -   1. F:/github/aicut_auto/newcut_ai\38_1.mp4
2025-08-07 17:41:08,621 - INFO -   2. F:/github/aicut_auto/newcut_ai\38_2.mp4
2025-08-07 17:41:08,621 - INFO -   3. F:/github/aicut_auto/newcut_ai\38_3.mp4
2025-08-07 17:41:08,621 - INFO - ========== 字幕 #38 处理结束 ==========

