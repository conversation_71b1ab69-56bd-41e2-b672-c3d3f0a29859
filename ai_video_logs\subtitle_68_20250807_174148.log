2025-08-07 17:41:48,919 - INFO - ========== 字幕 #68 处理开始 ==========
2025-08-07 17:41:48,919 - INFO - 字幕内容: 丧心病狂的他甚至承认，男人父母当年的死，也是他一手策划的。
2025-08-07 17:41:48,919 - INFO - 字幕序号: [3072, 3075]
2025-08-07 17:41:48,919 - INFO - 音频文件详情:
2025-08-07 17:41:48,920 - INFO -   - 路径: output\68.wav
2025-08-07 17:41:48,920 - INFO -   - 时长: 3.48秒
2025-08-07 17:41:48,920 - INFO -   - 验证音频时长: 3.48秒
2025-08-07 17:41:48,920 - INFO - 字幕时间戳信息:
2025-08-07 17:41:48,920 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:48,920 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:48,920 - INFO -   - 根据生成的音频时长(3.48秒)已调整字幕时间戳
2025-08-07 17:41:48,920 - INFO - ========== 新模式：为字幕 #68 生成4套场景方案 ==========
2025-08-07 17:41:48,920 - INFO - 字幕序号列表: [3072, 3075]
2025-08-07 17:41:48,920 - INFO - 
--- 生成方案 #1：基于字幕序号 #3072 ---
2025-08-07 17:41:48,920 - INFO - 开始为单个字幕序号 #3072 匹配场景，目标时长: 3.48秒
2025-08-07 17:41:48,920 - INFO - 开始查找字幕序号 [3072] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:48,921 - INFO - 找到related_overlap场景: scene_id=3401, 字幕#3072
2025-08-07 17:41:48,921 - INFO - 找到related_overlap场景: scene_id=3402, 字幕#3072
2025-08-07 17:41:48,922 - INFO - 字幕 #3072 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:48,922 - INFO - 字幕序号 #3072 找到 2 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:48,922 - INFO - 选择第一个overlap场景作为起点: scene_id=3401
2025-08-07 17:41:48,922 - INFO - 添加起点场景: scene_id=3401, 时长=1.24秒, 累计时长=1.24秒
2025-08-07 17:41:48,922 - INFO - 起点场景时长不足，需要延伸填充 2.24秒
2025-08-07 17:41:48,922 - INFO - 起点场景在原始列表中的索引: 3400
2025-08-07 17:41:48,922 - INFO - 延伸添加场景: scene_id=3402 (完整时长 1.00秒)
2025-08-07 17:41:48,922 - INFO - 累计时长: 2.24秒
2025-08-07 17:41:48,922 - INFO - 延伸添加场景: scene_id=3403 (完整时长 1.24秒)
2025-08-07 17:41:48,922 - INFO - 累计时长: 3.48秒
2025-08-07 17:41:48,922 - INFO - 延伸添加场景: scene_id=3404 (裁剪至 0.01秒)
2025-08-07 17:41:48,922 - INFO - 累计时长: 3.48秒
2025-08-07 17:41:48,922 - INFO - 字幕序号 #3072 场景匹配完成，共选择 4 个场景，总时长: 3.48秒
2025-08-07 17:41:48,922 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-08-07 17:41:48,922 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-08-07 17:41:48,922 - INFO - 
--- 生成方案 #2：基于字幕序号 #3075 ---
2025-08-07 17:41:48,922 - INFO - 开始为单个字幕序号 #3075 匹配场景，目标时长: 3.48秒
2025-08-07 17:41:48,922 - INFO - 开始查找字幕序号 [3075] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:48,923 - INFO - 找到related_overlap场景: scene_id=3407, 字幕#3075
2025-08-07 17:41:48,923 - INFO - 找到related_overlap场景: scene_id=3408, 字幕#3075
2025-08-07 17:41:48,923 - INFO - 字幕 #3075 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:48,923 - INFO - 字幕序号 #3075 找到 2 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:48,923 - INFO - 选择第一个overlap场景作为起点: scene_id=3407
2025-08-07 17:41:48,923 - INFO - 添加起点场景: scene_id=3407, 时长=1.80秒, 累计时长=1.80秒
2025-08-07 17:41:48,923 - INFO - 起点场景时长不足，需要延伸填充 1.68秒
2025-08-07 17:41:48,923 - INFO - 起点场景在原始列表中的索引: 3406
2025-08-07 17:41:48,923 - INFO - 延伸添加场景: scene_id=3408 (完整时长 1.40秒)
2025-08-07 17:41:48,923 - INFO - 累计时长: 3.20秒
2025-08-07 17:41:48,923 - INFO - 延伸添加场景: scene_id=3409 (裁剪至 0.28秒)
2025-08-07 17:41:48,923 - INFO - 累计时长: 3.48秒
2025-08-07 17:41:48,923 - INFO - 字幕序号 #3075 场景匹配完成，共选择 3 个场景，总时长: 3.48秒
2025-08-07 17:41:48,923 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-08-07 17:41:48,923 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:48,924 - INFO - ========== 当前模式：为字幕 #68 生成 1 套场景方案 ==========
2025-08-07 17:41:48,924 - INFO - 开始查找字幕序号 [3072, 3075] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:48,925 - INFO - 找到related_overlap场景: scene_id=3401, 字幕#3072
2025-08-07 17:41:48,925 - INFO - 找到related_overlap场景: scene_id=3402, 字幕#3072
2025-08-07 17:41:48,925 - INFO - 找到related_overlap场景: scene_id=3407, 字幕#3075
2025-08-07 17:41:48,925 - INFO - 找到related_overlap场景: scene_id=3408, 字幕#3075
2025-08-07 17:41:48,925 - INFO - 字幕 #3072 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:48,925 - INFO - 字幕 #3075 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:48,925 - INFO - 共收集 4 个未使用的overlap场景和 0 个未使用的between场景
2025-08-07 17:41:48,925 - INFO - 开始生成方案 #1
2025-08-07 17:41:48,925 - INFO - 方案 #1: 为字幕#3072选择初始化overlap场景id=3402
2025-08-07 17:41:48,925 - INFO - 方案 #1: 为字幕#3075选择初始化overlap场景id=3407
2025-08-07 17:41:48,925 - INFO - 方案 #1: 初始选择后，当前总时长=2.80秒
2025-08-07 17:41:48,925 - INFO - 方案 #1: 额外添加overlap场景id=3408, 当前总时长=4.20秒
2025-08-07 17:41:48,925 - INFO - 方案 #1: 额外between选择后，当前总时长=4.20秒
2025-08-07 17:41:48,925 - INFO - 方案 #1: 场景总时长(4.20秒)大于音频时长(3.48秒)，需要裁剪
2025-08-07 17:41:48,925 - INFO - 调整前总时长: 4.20秒, 目标时长: 3.48秒
2025-08-07 17:41:48,925 - INFO - 需要裁剪 0.72秒
2025-08-07 17:41:48,925 - INFO - 裁剪最长场景ID=3407：从1.80秒裁剪至1.08秒
2025-08-07 17:41:48,925 - INFO - 调整后总时长: 3.48秒，与目标时长差异: 0.00秒
2025-08-07 17:41:48,925 - INFO - 方案 #1 调整/填充后最终总时长: 3.48秒
2025-08-07 17:41:48,925 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:48,925 - INFO - ========== 当前模式：字幕 #68 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:48,925 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:48,925 - INFO - ========== 新模式：字幕 #68 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:48,926 - INFO - 
----- 处理字幕 #68 的方案 #1 -----
2025-08-07 17:41:48,926 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\68_1.mp4
2025-08-07 17:41:48,926 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpb_axyqs5
2025-08-07 17:41:48,926 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3401.mp4 (确认存在: True)
2025-08-07 17:41:48,926 - INFO - 添加场景ID=3401，时长=1.24秒，累计时长=1.24秒
2025-08-07 17:41:48,927 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3402.mp4 (确认存在: True)
2025-08-07 17:41:48,927 - INFO - 添加场景ID=3402，时长=1.00秒，累计时长=2.24秒
2025-08-07 17:41:48,927 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3403.mp4 (确认存在: True)
2025-08-07 17:41:48,927 - INFO - 添加场景ID=3403，时长=1.24秒，累计时长=3.48秒
2025-08-07 17:41:48,927 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3404.mp4 (确认存在: True)
2025-08-07 17:41:48,927 - INFO - 添加场景ID=3404，时长=1.08秒，累计时长=4.56秒
2025-08-07 17:41:48,927 - INFO - 准备合并 4 个场景文件，总时长约 4.56秒
2025-08-07 17:41:48,927 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3401.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3402.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3403.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3404.mp4'

2025-08-07 17:41:48,927 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpb_axyqs5\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpb_axyqs5\temp_combined.mp4
2025-08-07 17:41:49,081 - INFO - 合并后的视频时长: 4.65秒，目标音频时长: 3.48秒
2025-08-07 17:41:49,081 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpb_axyqs5\temp_combined.mp4 -ss 0 -to 3.483 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\68_1.mp4
2025-08-07 17:41:49,363 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:49,363 - INFO - 目标音频时长: 3.48秒
2025-08-07 17:41:49,363 - INFO - 实际视频时长: 3.54秒
2025-08-07 17:41:49,363 - INFO - 时长差异: 0.06秒 (1.72%)
2025-08-07 17:41:49,363 - INFO - ==========================================
2025-08-07 17:41:49,363 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:49,363 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\68_1.mp4
2025-08-07 17:41:49,365 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpb_axyqs5
2025-08-07 17:41:49,410 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:49,410 - INFO -   - 音频时长: 3.48秒
2025-08-07 17:41:49,410 - INFO -   - 视频时长: 3.54秒
2025-08-07 17:41:49,410 - INFO -   - 时长差异: 0.06秒 (1.72%)
2025-08-07 17:41:49,410 - INFO - 
----- 处理字幕 #68 的方案 #2 -----
2025-08-07 17:41:49,410 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\68_2.mp4
2025-08-07 17:41:49,410 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpijoq3p7q
2025-08-07 17:41:49,411 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3407.mp4 (确认存在: True)
2025-08-07 17:41:49,411 - INFO - 添加场景ID=3407，时长=1.80秒，累计时长=1.80秒
2025-08-07 17:41:49,411 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3408.mp4 (确认存在: True)
2025-08-07 17:41:49,411 - INFO - 添加场景ID=3408，时长=1.40秒，累计时长=3.20秒
2025-08-07 17:41:49,411 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3409.mp4 (确认存在: True)
2025-08-07 17:41:49,411 - INFO - 添加场景ID=3409，时长=0.68秒，累计时长=3.88秒
2025-08-07 17:41:49,411 - INFO - 准备合并 3 个场景文件，总时长约 3.88秒
2025-08-07 17:41:49,411 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3407.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3408.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3409.mp4'

2025-08-07 17:41:49,411 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpijoq3p7q\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpijoq3p7q\temp_combined.mp4
2025-08-07 17:41:49,557 - INFO - 合并后的视频时长: 3.95秒，目标音频时长: 3.48秒
2025-08-07 17:41:49,558 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpijoq3p7q\temp_combined.mp4 -ss 0 -to 3.483 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\68_2.mp4
2025-08-07 17:41:49,838 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:49,838 - INFO - 目标音频时长: 3.48秒
2025-08-07 17:41:49,838 - INFO - 实际视频时长: 3.54秒
2025-08-07 17:41:49,838 - INFO - 时长差异: 0.06秒 (1.72%)
2025-08-07 17:41:49,838 - INFO - ==========================================
2025-08-07 17:41:49,838 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:49,838 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\68_2.mp4
2025-08-07 17:41:49,839 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpijoq3p7q
2025-08-07 17:41:49,888 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:49,888 - INFO -   - 音频时长: 3.48秒
2025-08-07 17:41:49,888 - INFO -   - 视频时长: 3.54秒
2025-08-07 17:41:49,888 - INFO -   - 时长差异: 0.06秒 (1.72%)
2025-08-07 17:41:49,888 - INFO - 
----- 处理字幕 #68 的方案 #3 -----
2025-08-07 17:41:49,888 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\68_3.mp4
2025-08-07 17:41:49,888 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkrj4427t
2025-08-07 17:41:49,888 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3402.mp4 (确认存在: True)
2025-08-07 17:41:49,888 - INFO - 添加场景ID=3402，时长=1.00秒，累计时长=1.00秒
2025-08-07 17:41:49,888 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3407.mp4 (确认存在: True)
2025-08-07 17:41:49,888 - INFO - 添加场景ID=3407，时长=1.80秒，累计时长=2.80秒
2025-08-07 17:41:49,888 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3408.mp4 (确认存在: True)
2025-08-07 17:41:49,888 - INFO - 添加场景ID=3408，时长=1.40秒，累计时长=4.20秒
2025-08-07 17:41:49,888 - INFO - 准备合并 3 个场景文件，总时长约 4.20秒
2025-08-07 17:41:49,890 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3402.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3407.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3408.mp4'

2025-08-07 17:41:49,890 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpkrj4427t\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpkrj4427t\temp_combined.mp4
2025-08-07 17:41:50,047 - INFO - 合并后的视频时长: 4.27秒，目标音频时长: 3.48秒
2025-08-07 17:41:50,047 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpkrj4427t\temp_combined.mp4 -ss 0 -to 3.483 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\68_3.mp4
2025-08-07 17:41:50,318 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:50,318 - INFO - 目标音频时长: 3.48秒
2025-08-07 17:41:50,319 - INFO - 实际视频时长: 3.54秒
2025-08-07 17:41:50,319 - INFO - 时长差异: 0.06秒 (1.72%)
2025-08-07 17:41:50,319 - INFO - ==========================================
2025-08-07 17:41:50,319 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:50,319 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\68_3.mp4
2025-08-07 17:41:50,320 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkrj4427t
2025-08-07 17:41:50,366 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:50,366 - INFO -   - 音频时长: 3.48秒
2025-08-07 17:41:50,366 - INFO -   - 视频时长: 3.54秒
2025-08-07 17:41:50,366 - INFO -   - 时长差异: 0.06秒 (1.72%)
2025-08-07 17:41:50,367 - INFO - 
字幕 #68 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:50,367 - INFO - 生成的视频文件:
2025-08-07 17:41:50,367 - INFO -   1. F:/github/aicut_auto/newcut_ai\68_1.mp4
2025-08-07 17:41:50,367 - INFO -   2. F:/github/aicut_auto/newcut_ai\68_2.mp4
2025-08-07 17:41:50,367 - INFO -   3. F:/github/aicut_auto/newcut_ai\68_3.mp4
2025-08-07 17:41:50,367 - INFO - ========== 字幕 #68 处理结束 ==========

