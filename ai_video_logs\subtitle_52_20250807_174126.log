2025-08-07 17:41:26,863 - INFO - ========== 字幕 #52 处理开始 ==========
2025-08-07 17:41:26,863 - INFO - 字幕内容: 就在众人以为会长必死无疑时，喝下灵芝水的老人竟奇迹般地苏醒了，而且精神矍铄。
2025-08-07 17:41:26,863 - INFO - 字幕序号: [2356, 2357]
2025-08-07 17:41:26,863 - INFO - 音频文件详情:
2025-08-07 17:41:26,863 - INFO -   - 路径: output\52.wav
2025-08-07 17:41:26,863 - INFO -   - 时长: 5.96秒
2025-08-07 17:41:26,863 - INFO -   - 验证音频时长: 5.96秒
2025-08-07 17:41:26,863 - INFO - 字幕时间戳信息:
2025-08-07 17:41:26,872 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:26,872 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:26,872 - INFO -   - 根据生成的音频时长(5.96秒)已调整字幕时间戳
2025-08-07 17:41:26,872 - INFO - ========== 新模式：为字幕 #52 生成4套场景方案 ==========
2025-08-07 17:41:26,872 - INFO - 字幕序号列表: [2356, 2357]
2025-08-07 17:41:26,873 - INFO - 
--- 生成方案 #1：基于字幕序号 #2356 ---
2025-08-07 17:41:26,873 - INFO - 开始为单个字幕序号 #2356 匹配场景，目标时长: 5.96秒
2025-08-07 17:41:26,873 - INFO - 开始查找字幕序号 [2356] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:26,873 - INFO - 找到related_overlap场景: scene_id=2621, 字幕#2356
2025-08-07 17:41:26,874 - INFO - 找到related_between场景: scene_id=2620, 字幕#2356
2025-08-07 17:41:26,874 - INFO - 字幕 #2356 找到 1 个overlap场景, 1 个between场景
2025-08-07 17:41:26,874 - INFO - 字幕序号 #2356 找到 1 个可用overlap场景, 1 个可用between场景
2025-08-07 17:41:26,874 - INFO - 选择第一个overlap场景作为起点: scene_id=2621
2025-08-07 17:41:26,874 - INFO - 添加起点场景: scene_id=2621, 时长=0.88秒, 累计时长=0.88秒
2025-08-07 17:41:26,874 - INFO - 起点场景时长不足，需要延伸填充 5.08秒
2025-08-07 17:41:26,874 - INFO - 起点场景在原始列表中的索引: 2620
2025-08-07 17:41:26,874 - INFO - 延伸添加场景: scene_id=2622 (完整时长 1.44秒)
2025-08-07 17:41:26,874 - INFO - 累计时长: 2.32秒
2025-08-07 17:41:26,874 - INFO - 延伸添加场景: scene_id=2623 (完整时长 1.36秒)
2025-08-07 17:41:26,874 - INFO - 累计时长: 3.68秒
2025-08-07 17:41:26,874 - INFO - 延伸添加场景: scene_id=2624 (完整时长 0.64秒)
2025-08-07 17:41:26,874 - INFO - 累计时长: 4.32秒
2025-08-07 17:41:26,874 - INFO - 延伸添加场景: scene_id=2625 (完整时长 1.64秒)
2025-08-07 17:41:26,874 - INFO - 累计时长: 5.96秒
2025-08-07 17:41:26,874 - INFO - 延伸添加场景: scene_id=2626 (裁剪至 0.00秒)
2025-08-07 17:41:26,874 - INFO - 累计时长: 5.96秒
2025-08-07 17:41:26,874 - INFO - 字幕序号 #2356 场景匹配完成，共选择 6 个场景，总时长: 5.96秒
2025-08-07 17:41:26,874 - INFO - 方案 #1 生成成功，包含 6 个场景
2025-08-07 17:41:26,874 - INFO - 新模式：第1套方案的 6 个场景已加入全局已使用集合
2025-08-07 17:41:26,874 - INFO - 
--- 生成方案 #2：基于字幕序号 #2357 ---
2025-08-07 17:41:26,874 - INFO - 开始为单个字幕序号 #2357 匹配场景，目标时长: 5.96秒
2025-08-07 17:41:26,874 - INFO - 开始查找字幕序号 [2357] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:26,875 - INFO - 找到related_overlap场景: scene_id=2622, 字幕#2357
2025-08-07 17:41:26,876 - INFO - 字幕 #2357 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:26,876 - INFO - 字幕序号 #2357 找到 0 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:26,876 - ERROR - 字幕序号 #2357 没有找到任何可用的匹配场景
2025-08-07 17:41:26,876 - WARNING - 方案 #2 生成失败，未找到合适的场景
2025-08-07 17:41:26,876 - INFO - 
--- 生成方案 #2：使用传统模式 ---
2025-08-07 17:41:26,876 - INFO - ========== 当前模式：为字幕 #52 生成 1 套场景方案 ==========
2025-08-07 17:41:26,876 - INFO - 开始查找字幕序号 [2356, 2357] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:26,876 - INFO - 找到related_overlap场景: scene_id=2621, 字幕#2356
2025-08-07 17:41:26,876 - INFO - 找到related_overlap场景: scene_id=2622, 字幕#2357
2025-08-07 17:41:26,877 - INFO - 找到related_between场景: scene_id=2620, 字幕#2356
2025-08-07 17:41:26,877 - INFO - 字幕 #2356 找到 1 个overlap场景, 1 个between场景
2025-08-07 17:41:26,877 - INFO - 字幕 #2357 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:26,877 - INFO - 共收集 2 个未使用的overlap场景和 1 个未使用的between场景
2025-08-07 17:41:26,877 - INFO - 开始生成方案 #1
2025-08-07 17:41:26,877 - INFO - 方案 #1: 为字幕#2356选择初始化overlap场景id=2621
2025-08-07 17:41:26,877 - INFO - 方案 #1: 为字幕#2357选择初始化overlap场景id=2622
2025-08-07 17:41:26,877 - INFO - 方案 #1: 初始选择后，当前总时长=2.32秒
2025-08-07 17:41:26,877 - INFO - 方案 #1: 额外between选择后，当前总时长=2.32秒
2025-08-07 17:41:26,877 - INFO - 方案 #1: 额外添加between场景id=2620, 当前总时长=4.40秒
2025-08-07 17:41:26,877 - INFO - 方案 #1: 场景总时长(4.40秒)小于音频时长(5.96秒)，需要延伸填充
2025-08-07 17:41:26,877 - INFO - 方案 #1: 最后一个场景ID: 2620
2025-08-07 17:41:26,877 - INFO - 方案 #1: 找到最后一个场景在原始列表中的索引: 2619
2025-08-07 17:41:26,877 - INFO - 方案 #1: 需要填充时长: 1.56秒
2025-08-07 17:41:26,877 - INFO - 方案 #1: 跳过已使用的场景: scene_id=2621
2025-08-07 17:41:26,877 - INFO - 方案 #1: 跳过已使用的场景: scene_id=2622
2025-08-07 17:41:26,877 - INFO - 方案 #1: 追加场景 scene_id=2623 (完整时长 1.36秒)
2025-08-07 17:41:26,877 - INFO - 方案 #1: 追加场景 scene_id=2624 (裁剪至 0.20秒)
2025-08-07 17:41:26,877 - INFO - 方案 #1: 成功填充至目标时长
2025-08-07 17:41:26,877 - INFO - 方案 #1 调整/填充后最终总时长: 5.96秒
2025-08-07 17:41:26,877 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:26,877 - INFO - ========== 当前模式：字幕 #52 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:26,877 - INFO - 方案 #2 (传统模式) 生成成功
2025-08-07 17:41:26,877 - INFO - ========== 新模式：字幕 #52 共生成 2 套有效场景方案 ==========
2025-08-07 17:41:26,878 - INFO - 
----- 处理字幕 #52 的方案 #1 -----
2025-08-07 17:41:26,878 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\52_1.mp4
2025-08-07 17:41:26,878 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1oyj_q3p
2025-08-07 17:41:26,878 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2621.mp4 (确认存在: True)
2025-08-07 17:41:26,878 - INFO - 添加场景ID=2621，时长=0.88秒，累计时长=0.88秒
2025-08-07 17:41:26,879 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2622.mp4 (确认存在: True)
2025-08-07 17:41:26,879 - INFO - 添加场景ID=2622，时长=1.44秒，累计时长=2.32秒
2025-08-07 17:41:26,879 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2623.mp4 (确认存在: True)
2025-08-07 17:41:26,879 - INFO - 添加场景ID=2623，时长=1.36秒，累计时长=3.68秒
2025-08-07 17:41:26,879 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2624.mp4 (确认存在: True)
2025-08-07 17:41:26,879 - INFO - 添加场景ID=2624，时长=0.64秒，累计时长=4.32秒
2025-08-07 17:41:26,879 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2625.mp4 (确认存在: True)
2025-08-07 17:41:26,879 - INFO - 添加场景ID=2625，时长=1.64秒，累计时长=5.96秒
2025-08-07 17:41:26,879 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2626.mp4 (确认存在: True)
2025-08-07 17:41:26,879 - INFO - 添加场景ID=2626，时长=1.12秒，累计时长=7.08秒
2025-08-07 17:41:26,879 - INFO - 准备合并 6 个场景文件，总时长约 7.08秒
2025-08-07 17:41:26,879 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2621.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2622.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2623.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2624.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2625.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2626.mp4'

2025-08-07 17:41:26,879 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp1oyj_q3p\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp1oyj_q3p\temp_combined.mp4
2025-08-07 17:41:27,066 - INFO - 合并后的视频时长: 7.22秒，目标音频时长: 5.96秒
2025-08-07 17:41:27,066 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp1oyj_q3p\temp_combined.mp4 -ss 0 -to 5.96 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\52_1.mp4
2025-08-07 17:41:27,396 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:27,396 - INFO - 目标音频时长: 5.96秒
2025-08-07 17:41:27,396 - INFO - 实际视频时长: 5.98秒
2025-08-07 17:41:27,396 - INFO - 时长差异: 0.02秒 (0.39%)
2025-08-07 17:41:27,396 - INFO - ==========================================
2025-08-07 17:41:27,396 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:27,396 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\52_1.mp4
2025-08-07 17:41:27,397 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp1oyj_q3p
2025-08-07 17:41:27,444 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:27,444 - INFO -   - 音频时长: 5.96秒
2025-08-07 17:41:27,444 - INFO -   - 视频时长: 5.98秒
2025-08-07 17:41:27,444 - INFO -   - 时长差异: 0.02秒 (0.39%)
2025-08-07 17:41:27,444 - INFO - 
----- 处理字幕 #52 的方案 #2 -----
2025-08-07 17:41:27,444 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\52_2.mp4
2025-08-07 17:41:27,444 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpitgqh35o
2025-08-07 17:41:27,444 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2621.mp4 (确认存在: True)
2025-08-07 17:41:27,444 - INFO - 添加场景ID=2621，时长=0.88秒，累计时长=0.88秒
2025-08-07 17:41:27,444 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2622.mp4 (确认存在: True)
2025-08-07 17:41:27,444 - INFO - 添加场景ID=2622，时长=1.44秒，累计时长=2.32秒
2025-08-07 17:41:27,444 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2620.mp4 (确认存在: True)
2025-08-07 17:41:27,444 - INFO - 添加场景ID=2620，时长=2.08秒，累计时长=4.40秒
2025-08-07 17:41:27,444 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2623.mp4 (确认存在: True)
2025-08-07 17:41:27,444 - INFO - 添加场景ID=2623，时长=1.36秒，累计时长=5.76秒
2025-08-07 17:41:27,444 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2624.mp4 (确认存在: True)
2025-08-07 17:41:27,444 - INFO - 添加场景ID=2624，时长=0.64秒，累计时长=6.40秒
2025-08-07 17:41:27,444 - INFO - 准备合并 5 个场景文件，总时长约 6.40秒
2025-08-07 17:41:27,444 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2621.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2622.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2620.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2623.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2624.mp4'

2025-08-07 17:41:27,445 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpitgqh35o\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpitgqh35o\temp_combined.mp4
2025-08-07 17:41:27,630 - INFO - 合并后的视频时长: 6.52秒，目标音频时长: 5.96秒
2025-08-07 17:41:27,630 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpitgqh35o\temp_combined.mp4 -ss 0 -to 5.96 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\52_2.mp4
2025-08-07 17:41:27,950 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:27,950 - INFO - 目标音频时长: 5.96秒
2025-08-07 17:41:27,950 - INFO - 实际视频时长: 5.98秒
2025-08-07 17:41:27,950 - INFO - 时长差异: 0.02秒 (0.39%)
2025-08-07 17:41:27,950 - INFO - ==========================================
2025-08-07 17:41:27,950 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:27,950 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\52_2.mp4
2025-08-07 17:41:27,951 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpitgqh35o
2025-08-07 17:41:27,996 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:27,996 - INFO -   - 音频时长: 5.96秒
2025-08-07 17:41:27,996 - INFO -   - 视频时长: 5.98秒
2025-08-07 17:41:27,996 - INFO -   - 时长差异: 0.02秒 (0.39%)
2025-08-07 17:41:27,996 - INFO - 
字幕 #52 处理完成，成功生成 2/2 套方案
2025-08-07 17:41:27,996 - INFO - 生成的视频文件:
2025-08-07 17:41:27,996 - INFO -   1. F:/github/aicut_auto/newcut_ai\52_1.mp4
2025-08-07 17:41:27,996 - INFO -   2. F:/github/aicut_auto/newcut_ai\52_2.mp4
2025-08-07 17:41:27,996 - INFO - ========== 字幕 #52 处理结束 ==========

