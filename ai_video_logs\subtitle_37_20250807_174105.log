2025-08-07 17:41:05,968 - INFO - ========== 字幕 #37 处理开始 ==========
2025-08-07 17:41:05,968 - INFO - 字幕内容: 被抓个正着的她故技重施，反咬一口，污蔑是男人的助理对自己图谋不轨。
2025-08-07 17:41:05,968 - INFO - 字幕序号: [1346, 1356]
2025-08-07 17:41:05,968 - INFO - 音频文件详情:
2025-08-07 17:41:05,968 - INFO -   - 路径: output\37.wav
2025-08-07 17:41:05,968 - INFO -   - 时长: 3.52秒
2025-08-07 17:41:05,968 - INFO -   - 验证音频时长: 3.52秒
2025-08-07 17:41:05,968 - INFO - 字幕时间戳信息:
2025-08-07 17:41:05,968 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:05,968 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:05,968 - INFO -   - 根据生成的音频时长(3.52秒)已调整字幕时间戳
2025-08-07 17:41:05,968 - INFO - ========== 新模式：为字幕 #37 生成4套场景方案 ==========
2025-08-07 17:41:05,968 - INFO - 字幕序号列表: [1346, 1356]
2025-08-07 17:41:05,968 - INFO - 
--- 生成方案 #1：基于字幕序号 #1346 ---
2025-08-07 17:41:05,968 - INFO - 开始为单个字幕序号 #1346 匹配场景，目标时长: 3.52秒
2025-08-07 17:41:05,968 - INFO - 开始查找字幕序号 [1346] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:05,969 - INFO - 找到related_overlap场景: scene_id=1550, 字幕#1346
2025-08-07 17:41:05,969 - INFO - 找到related_overlap场景: scene_id=1551, 字幕#1346
2025-08-07 17:41:05,970 - INFO - 字幕 #1346 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:05,970 - INFO - 字幕序号 #1346 找到 2 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:05,970 - INFO - 选择第一个overlap场景作为起点: scene_id=1550
2025-08-07 17:41:05,970 - INFO - 添加起点场景: scene_id=1550, 时长=0.84秒, 累计时长=0.84秒
2025-08-07 17:41:05,970 - INFO - 起点场景时长不足，需要延伸填充 2.68秒
2025-08-07 17:41:05,970 - INFO - 起点场景在原始列表中的索引: 1549
2025-08-07 17:41:05,970 - INFO - 延伸添加场景: scene_id=1551 (完整时长 1.12秒)
2025-08-07 17:41:05,970 - INFO - 累计时长: 1.96秒
2025-08-07 17:41:05,970 - INFO - 延伸添加场景: scene_id=1552 (完整时长 1.28秒)
2025-08-07 17:41:05,970 - INFO - 累计时长: 3.24秒
2025-08-07 17:41:05,970 - INFO - 延伸添加场景: scene_id=1553 (裁剪至 0.28秒)
2025-08-07 17:41:05,970 - INFO - 累计时长: 3.52秒
2025-08-07 17:41:05,970 - INFO - 字幕序号 #1346 场景匹配完成，共选择 4 个场景，总时长: 3.52秒
2025-08-07 17:41:05,970 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-08-07 17:41:05,970 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-08-07 17:41:05,970 - INFO - 
--- 生成方案 #2：基于字幕序号 #1356 ---
2025-08-07 17:41:05,970 - INFO - 开始为单个字幕序号 #1356 匹配场景，目标时长: 3.52秒
2025-08-07 17:41:05,970 - INFO - 开始查找字幕序号 [1356] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:05,970 - INFO - 找到related_overlap场景: scene_id=1559, 字幕#1356
2025-08-07 17:41:05,971 - INFO - 字幕 #1356 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:05,971 - INFO - 字幕序号 #1356 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:05,971 - INFO - 选择第一个overlap场景作为起点: scene_id=1559
2025-08-07 17:41:05,971 - INFO - 添加起点场景: scene_id=1559, 时长=3.04秒, 累计时长=3.04秒
2025-08-07 17:41:05,971 - INFO - 起点场景时长不足，需要延伸填充 0.48秒
2025-08-07 17:41:05,971 - INFO - 起点场景在原始列表中的索引: 1558
2025-08-07 17:41:05,971 - INFO - 延伸添加场景: scene_id=1560 (裁剪至 0.48秒)
2025-08-07 17:41:05,971 - INFO - 累计时长: 3.52秒
2025-08-07 17:41:05,971 - INFO - 字幕序号 #1356 场景匹配完成，共选择 2 个场景，总时长: 3.52秒
2025-08-07 17:41:05,971 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-08-07 17:41:05,971 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:05,971 - INFO - ========== 当前模式：为字幕 #37 生成 1 套场景方案 ==========
2025-08-07 17:41:05,971 - INFO - 开始查找字幕序号 [1346, 1356] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:05,971 - INFO - 找到related_overlap场景: scene_id=1550, 字幕#1346
2025-08-07 17:41:05,971 - INFO - 找到related_overlap场景: scene_id=1551, 字幕#1346
2025-08-07 17:41:05,971 - INFO - 找到related_overlap场景: scene_id=1559, 字幕#1356
2025-08-07 17:41:05,972 - INFO - 字幕 #1346 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:05,972 - INFO - 字幕 #1356 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:05,972 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-08-07 17:41:05,972 - INFO - 开始生成方案 #1
2025-08-07 17:41:05,972 - INFO - 方案 #1: 为字幕#1346选择初始化overlap场景id=1550
2025-08-07 17:41:05,972 - INFO - 方案 #1: 为字幕#1356选择初始化overlap场景id=1559
2025-08-07 17:41:05,972 - INFO - 方案 #1: 初始选择后，当前总时长=3.88秒
2025-08-07 17:41:05,972 - INFO - 方案 #1: 额外between选择后，当前总时长=3.88秒
2025-08-07 17:41:05,972 - INFO - 方案 #1: 场景总时长(3.88秒)大于音频时长(3.52秒)，需要裁剪
2025-08-07 17:41:05,972 - INFO - 调整前总时长: 3.88秒, 目标时长: 3.52秒
2025-08-07 17:41:05,972 - INFO - 需要裁剪 0.36秒
2025-08-07 17:41:05,972 - INFO - 裁剪最长场景ID=1559：从3.04秒裁剪至2.68秒
2025-08-07 17:41:05,972 - INFO - 调整后总时长: 3.52秒，与目标时长差异: 0.00秒
2025-08-07 17:41:05,972 - INFO - 方案 #1 调整/填充后最终总时长: 3.52秒
2025-08-07 17:41:05,972 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:05,972 - INFO - ========== 当前模式：字幕 #37 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:05,972 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:05,972 - INFO - ========== 新模式：字幕 #37 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:05,972 - INFO - 
----- 处理字幕 #37 的方案 #1 -----
2025-08-07 17:41:05,972 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\37_1.mp4
2025-08-07 17:41:05,973 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmps2w3nd4u
2025-08-07 17:41:05,973 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1550.mp4 (确认存在: True)
2025-08-07 17:41:05,973 - INFO - 添加场景ID=1550，时长=0.84秒，累计时长=0.84秒
2025-08-07 17:41:05,973 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1551.mp4 (确认存在: True)
2025-08-07 17:41:05,973 - INFO - 添加场景ID=1551，时长=1.12秒，累计时长=1.96秒
2025-08-07 17:41:05,973 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1552.mp4 (确认存在: True)
2025-08-07 17:41:05,973 - INFO - 添加场景ID=1552，时长=1.28秒，累计时长=3.24秒
2025-08-07 17:41:05,973 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1553.mp4 (确认存在: True)
2025-08-07 17:41:05,973 - INFO - 添加场景ID=1553，时长=1.48秒，累计时长=4.72秒
2025-08-07 17:41:05,973 - INFO - 准备合并 4 个场景文件，总时长约 4.72秒
2025-08-07 17:41:05,973 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1550.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1551.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1552.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1553.mp4'

2025-08-07 17:41:05,974 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmps2w3nd4u\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmps2w3nd4u\temp_combined.mp4
2025-08-07 17:41:06,137 - INFO - 合并后的视频时长: 4.81秒，目标音频时长: 3.52秒
2025-08-07 17:41:06,137 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmps2w3nd4u\temp_combined.mp4 -ss 0 -to 3.52 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\37_1.mp4
2025-08-07 17:41:06,407 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:06,407 - INFO - 目标音频时长: 3.52秒
2025-08-07 17:41:06,407 - INFO - 实际视频时长: 3.54秒
2025-08-07 17:41:06,407 - INFO - 时长差异: 0.02秒 (0.66%)
2025-08-07 17:41:06,407 - INFO - ==========================================
2025-08-07 17:41:06,407 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:06,407 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\37_1.mp4
2025-08-07 17:41:06,407 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmps2w3nd4u
2025-08-07 17:41:06,450 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:06,450 - INFO -   - 音频时长: 3.52秒
2025-08-07 17:41:06,450 - INFO -   - 视频时长: 3.54秒
2025-08-07 17:41:06,450 - INFO -   - 时长差异: 0.02秒 (0.66%)
2025-08-07 17:41:06,450 - INFO - 
----- 处理字幕 #37 的方案 #2 -----
2025-08-07 17:41:06,450 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\37_2.mp4
2025-08-07 17:41:06,450 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp837rjv38
2025-08-07 17:41:06,450 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1559.mp4 (确认存在: True)
2025-08-07 17:41:06,451 - INFO - 添加场景ID=1559，时长=3.04秒，累计时长=3.04秒
2025-08-07 17:41:06,451 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1560.mp4 (确认存在: True)
2025-08-07 17:41:06,451 - INFO - 添加场景ID=1560，时长=1.56秒，累计时长=4.60秒
2025-08-07 17:41:06,451 - INFO - 准备合并 2 个场景文件，总时长约 4.60秒
2025-08-07 17:41:06,451 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1559.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1560.mp4'

2025-08-07 17:41:06,451 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp837rjv38\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp837rjv38\temp_combined.mp4
2025-08-07 17:41:06,574 - INFO - 合并后的视频时长: 4.65秒，目标音频时长: 3.52秒
2025-08-07 17:41:06,574 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp837rjv38\temp_combined.mp4 -ss 0 -to 3.52 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\37_2.mp4
2025-08-07 17:41:06,806 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:06,806 - INFO - 目标音频时长: 3.52秒
2025-08-07 17:41:06,806 - INFO - 实际视频时长: 3.54秒
2025-08-07 17:41:06,806 - INFO - 时长差异: 0.02秒 (0.66%)
2025-08-07 17:41:06,806 - INFO - ==========================================
2025-08-07 17:41:06,806 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:06,806 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\37_2.mp4
2025-08-07 17:41:06,806 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp837rjv38
2025-08-07 17:41:06,848 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:06,848 - INFO -   - 音频时长: 3.52秒
2025-08-07 17:41:06,848 - INFO -   - 视频时长: 3.54秒
2025-08-07 17:41:06,848 - INFO -   - 时长差异: 0.02秒 (0.66%)
2025-08-07 17:41:06,848 - INFO - 
----- 处理字幕 #37 的方案 #3 -----
2025-08-07 17:41:06,849 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\37_3.mp4
2025-08-07 17:41:06,849 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcckn4bqb
2025-08-07 17:41:06,849 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1550.mp4 (确认存在: True)
2025-08-07 17:41:06,849 - INFO - 添加场景ID=1550，时长=0.84秒，累计时长=0.84秒
2025-08-07 17:41:06,849 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1559.mp4 (确认存在: True)
2025-08-07 17:41:06,849 - INFO - 添加场景ID=1559，时长=3.04秒，累计时长=3.88秒
2025-08-07 17:41:06,850 - INFO - 准备合并 2 个场景文件，总时长约 3.88秒
2025-08-07 17:41:06,850 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1550.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1559.mp4'

2025-08-07 17:41:06,850 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpcckn4bqb\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpcckn4bqb\temp_combined.mp4
2025-08-07 17:41:06,958 - INFO - 合并后的视频时长: 3.93秒，目标音频时长: 3.52秒
2025-08-07 17:41:06,958 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpcckn4bqb\temp_combined.mp4 -ss 0 -to 3.52 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\37_3.mp4
2025-08-07 17:41:07,200 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:07,200 - INFO - 目标音频时长: 3.52秒
2025-08-07 17:41:07,200 - INFO - 实际视频时长: 3.54秒
2025-08-07 17:41:07,200 - INFO - 时长差异: 0.02秒 (0.66%)
2025-08-07 17:41:07,200 - INFO - ==========================================
2025-08-07 17:41:07,200 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:07,200 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\37_3.mp4
2025-08-07 17:41:07,201 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpcckn4bqb
2025-08-07 17:41:07,256 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:07,256 - INFO -   - 音频时长: 3.52秒
2025-08-07 17:41:07,256 - INFO -   - 视频时长: 3.54秒
2025-08-07 17:41:07,256 - INFO -   - 时长差异: 0.02秒 (0.66%)
2025-08-07 17:41:07,256 - INFO - 
字幕 #37 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:07,256 - INFO - 生成的视频文件:
2025-08-07 17:41:07,256 - INFO -   1. F:/github/aicut_auto/newcut_ai\37_1.mp4
2025-08-07 17:41:07,256 - INFO -   2. F:/github/aicut_auto/newcut_ai\37_2.mp4
2025-08-07 17:41:07,256 - INFO -   3. F:/github/aicut_auto/newcut_ai\37_3.mp4
2025-08-07 17:41:07,256 - INFO - ========== 字幕 #37 处理结束 ==========

