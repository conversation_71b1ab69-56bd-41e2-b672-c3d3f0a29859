2025-08-07 17:40:44,563 - INFO - ========== 字幕 #22 处理开始 ==========
2025-08-07 17:40:44,563 - INFO - 字幕内容: 就在众人以为这又是一场闹剧时，男人缓缓站了起来，一脚踹开身边的二弟，十几年未曾站立的双腿，终于恢复如初。
2025-08-07 17:40:44,563 - INFO - 字幕序号: [635, 640]
2025-08-07 17:40:44,563 - INFO - 音频文件详情:
2025-08-07 17:40:44,563 - INFO -   - 路径: output\22.wav
2025-08-07 17:40:44,563 - INFO -   - 时长: 5.91秒
2025-08-07 17:40:44,565 - INFO -   - 验证音频时长: 5.91秒
2025-08-07 17:40:44,565 - INFO - 字幕时间戳信息:
2025-08-07 17:40:44,565 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:44,565 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:44,565 - INFO -   - 根据生成的音频时长(5.91秒)已调整字幕时间戳
2025-08-07 17:40:44,565 - INFO - ========== 新模式：为字幕 #22 生成4套场景方案 ==========
2025-08-07 17:40:44,565 - INFO - 字幕序号列表: [635, 640]
2025-08-07 17:40:44,565 - INFO - 
--- 生成方案 #1：基于字幕序号 #635 ---
2025-08-07 17:40:44,565 - INFO - 开始为单个字幕序号 #635 匹配场景，目标时长: 5.91秒
2025-08-07 17:40:44,565 - INFO - 开始查找字幕序号 [635] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:44,565 - INFO - 找到related_overlap场景: scene_id=756, 字幕#635
2025-08-07 17:40:44,566 - INFO - 字幕 #635 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:44,566 - INFO - 字幕序号 #635 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:44,566 - INFO - 选择第一个overlap场景作为起点: scene_id=756
2025-08-07 17:40:44,566 - INFO - 添加起点场景: scene_id=756, 时长=1.48秒, 累计时长=1.48秒
2025-08-07 17:40:44,566 - INFO - 起点场景时长不足，需要延伸填充 4.43秒
2025-08-07 17:40:44,566 - INFO - 起点场景在原始列表中的索引: 755
2025-08-07 17:40:44,566 - INFO - 延伸添加场景: scene_id=757 (完整时长 3.20秒)
2025-08-07 17:40:44,566 - INFO - 累计时长: 4.68秒
2025-08-07 17:40:44,566 - INFO - 延伸添加场景: scene_id=758 (裁剪至 1.23秒)
2025-08-07 17:40:44,566 - INFO - 累计时长: 5.91秒
2025-08-07 17:40:44,566 - INFO - 字幕序号 #635 场景匹配完成，共选择 3 个场景，总时长: 5.91秒
2025-08-07 17:40:44,566 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-08-07 17:40:44,566 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-08-07 17:40:44,566 - INFO - 
--- 生成方案 #2：基于字幕序号 #640 ---
2025-08-07 17:40:44,566 - INFO - 开始为单个字幕序号 #640 匹配场景，目标时长: 5.91秒
2025-08-07 17:40:44,566 - INFO - 开始查找字幕序号 [640] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:44,566 - INFO - 找到related_overlap场景: scene_id=775, 字幕#640
2025-08-07 17:40:44,566 - INFO - 找到related_overlap场景: scene_id=776, 字幕#640
2025-08-07 17:40:44,567 - INFO - 找到related_between场景: scene_id=777, 字幕#640
2025-08-07 17:40:44,567 - INFO - 找到related_between场景: scene_id=778, 字幕#640
2025-08-07 17:40:44,567 - INFO - 字幕 #640 找到 2 个overlap场景, 2 个between场景
2025-08-07 17:40:44,567 - INFO - 字幕序号 #640 找到 2 个可用overlap场景, 2 个可用between场景
2025-08-07 17:40:44,567 - INFO - 选择第一个overlap场景作为起点: scene_id=775
2025-08-07 17:40:44,567 - INFO - 添加起点场景: scene_id=775, 时长=1.52秒, 累计时长=1.52秒
2025-08-07 17:40:44,567 - INFO - 起点场景时长不足，需要延伸填充 4.39秒
2025-08-07 17:40:44,567 - INFO - 起点场景在原始列表中的索引: 774
2025-08-07 17:40:44,567 - INFO - 延伸添加场景: scene_id=776 (完整时长 1.00秒)
2025-08-07 17:40:44,567 - INFO - 累计时长: 2.52秒
2025-08-07 17:40:44,567 - INFO - 延伸添加场景: scene_id=777 (完整时长 0.88秒)
2025-08-07 17:40:44,567 - INFO - 累计时长: 3.40秒
2025-08-07 17:40:44,567 - INFO - 延伸添加场景: scene_id=778 (完整时长 1.24秒)
2025-08-07 17:40:44,567 - INFO - 累计时长: 4.64秒
2025-08-07 17:40:44,567 - INFO - 延伸添加场景: scene_id=779 (完整时长 0.80秒)
2025-08-07 17:40:44,567 - INFO - 累计时长: 5.44秒
2025-08-07 17:40:44,567 - INFO - 延伸添加场景: scene_id=780 (裁剪至 0.47秒)
2025-08-07 17:40:44,567 - INFO - 累计时长: 5.91秒
2025-08-07 17:40:44,567 - INFO - 字幕序号 #640 场景匹配完成，共选择 6 个场景，总时长: 5.91秒
2025-08-07 17:40:44,567 - INFO - 方案 #2 生成成功，包含 6 个场景
2025-08-07 17:40:44,567 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:44,567 - INFO - ========== 当前模式：为字幕 #22 生成 1 套场景方案 ==========
2025-08-07 17:40:44,567 - INFO - 开始查找字幕序号 [635, 640] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:44,568 - INFO - 找到related_overlap场景: scene_id=756, 字幕#635
2025-08-07 17:40:44,568 - INFO - 找到related_overlap场景: scene_id=775, 字幕#640
2025-08-07 17:40:44,568 - INFO - 找到related_overlap场景: scene_id=776, 字幕#640
2025-08-07 17:40:44,568 - INFO - 找到related_between场景: scene_id=777, 字幕#640
2025-08-07 17:40:44,568 - INFO - 找到related_between场景: scene_id=778, 字幕#640
2025-08-07 17:40:44,569 - INFO - 字幕 #635 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:44,569 - INFO - 字幕 #640 找到 2 个overlap场景, 2 个between场景
2025-08-07 17:40:44,569 - INFO - 共收集 3 个未使用的overlap场景和 2 个未使用的between场景
2025-08-07 17:40:44,569 - INFO - 开始生成方案 #1
2025-08-07 17:40:44,569 - INFO - 方案 #1: 为字幕#635选择初始化overlap场景id=756
2025-08-07 17:40:44,569 - INFO - 方案 #1: 为字幕#640选择初始化overlap场景id=775
2025-08-07 17:40:44,569 - INFO - 方案 #1: 初始选择后，当前总时长=3.00秒
2025-08-07 17:40:44,569 - INFO - 方案 #1: 额外添加overlap场景id=776, 当前总时长=4.00秒
2025-08-07 17:40:44,569 - INFO - 方案 #1: 额外between选择后，当前总时长=4.00秒
2025-08-07 17:40:44,569 - INFO - 方案 #1: 额外添加between场景id=777, 当前总时长=4.88秒
2025-08-07 17:40:44,569 - INFO - 方案 #1: 额外添加between场景id=778, 当前总时长=6.12秒
2025-08-07 17:40:44,569 - INFO - 方案 #1: 场景总时长(6.12秒)大于音频时长(5.91秒)，需要裁剪
2025-08-07 17:40:44,569 - INFO - 调整前总时长: 6.12秒, 目标时长: 5.91秒
2025-08-07 17:40:44,569 - INFO - 需要裁剪 0.21秒
2025-08-07 17:40:44,569 - INFO - 裁剪最长场景ID=775：从1.52秒裁剪至1.31秒
2025-08-07 17:40:44,569 - INFO - 调整后总时长: 5.91秒，与目标时长差异: 0.00秒
2025-08-07 17:40:44,569 - INFO - 方案 #1 调整/填充后最终总时长: 5.91秒
2025-08-07 17:40:44,569 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:44,569 - INFO - ========== 当前模式：字幕 #22 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:44,569 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:44,569 - INFO - ========== 新模式：字幕 #22 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:44,569 - INFO - 
----- 处理字幕 #22 的方案 #1 -----
2025-08-07 17:40:44,569 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\22_1.mp4
2025-08-07 17:40:44,569 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpshdl65u8
2025-08-07 17:40:44,570 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\756.mp4 (确认存在: True)
2025-08-07 17:40:44,570 - INFO - 添加场景ID=756，时长=1.48秒，累计时长=1.48秒
2025-08-07 17:40:44,570 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\757.mp4 (确认存在: True)
2025-08-07 17:40:44,570 - INFO - 添加场景ID=757，时长=3.20秒，累计时长=4.68秒
2025-08-07 17:40:44,570 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\758.mp4 (确认存在: True)
2025-08-07 17:40:44,570 - INFO - 添加场景ID=758，时长=1.88秒，累计时长=6.56秒
2025-08-07 17:40:44,570 - INFO - 准备合并 3 个场景文件，总时长约 6.56秒
2025-08-07 17:40:44,570 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/756.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/757.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/758.mp4'

2025-08-07 17:40:44,571 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpshdl65u8\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpshdl65u8\temp_combined.mp4
2025-08-07 17:40:44,707 - INFO - 合并后的视频时长: 6.63秒，目标音频时长: 5.91秒
2025-08-07 17:40:44,707 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpshdl65u8\temp_combined.mp4 -ss 0 -to 5.906 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\22_1.mp4
2025-08-07 17:40:45,021 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:45,021 - INFO - 目标音频时长: 5.91秒
2025-08-07 17:40:45,021 - INFO - 实际视频时长: 5.94秒
2025-08-07 17:40:45,021 - INFO - 时长差异: 0.04秒 (0.63%)
2025-08-07 17:40:45,021 - INFO - ==========================================
2025-08-07 17:40:45,021 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:45,021 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\22_1.mp4
2025-08-07 17:40:45,022 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpshdl65u8
2025-08-07 17:40:45,067 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:45,067 - INFO -   - 音频时长: 5.91秒
2025-08-07 17:40:45,067 - INFO -   - 视频时长: 5.94秒
2025-08-07 17:40:45,067 - INFO -   - 时长差异: 0.04秒 (0.63%)
2025-08-07 17:40:45,067 - INFO - 
----- 处理字幕 #22 的方案 #2 -----
2025-08-07 17:40:45,067 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\22_2.mp4
2025-08-07 17:40:45,067 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkcclb4co
2025-08-07 17:40:45,068 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\775.mp4 (确认存在: True)
2025-08-07 17:40:45,068 - INFO - 添加场景ID=775，时长=1.52秒，累计时长=1.52秒
2025-08-07 17:40:45,068 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\776.mp4 (确认存在: True)
2025-08-07 17:40:45,068 - INFO - 添加场景ID=776，时长=1.00秒，累计时长=2.52秒
2025-08-07 17:40:45,068 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\777.mp4 (确认存在: True)
2025-08-07 17:40:45,068 - INFO - 添加场景ID=777，时长=0.88秒，累计时长=3.40秒
2025-08-07 17:40:45,068 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\778.mp4 (确认存在: True)
2025-08-07 17:40:45,068 - INFO - 添加场景ID=778，时长=1.24秒，累计时长=4.64秒
2025-08-07 17:40:45,068 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\779.mp4 (确认存在: True)
2025-08-07 17:40:45,068 - INFO - 添加场景ID=779，时长=0.80秒，累计时长=5.44秒
2025-08-07 17:40:45,068 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\780.mp4 (确认存在: True)
2025-08-07 17:40:45,068 - INFO - 添加场景ID=780，时长=1.00秒，累计时长=6.44秒
2025-08-07 17:40:45,068 - INFO - 准备合并 6 个场景文件，总时长约 6.44秒
2025-08-07 17:40:45,068 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/775.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/776.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/777.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/778.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/779.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/780.mp4'

2025-08-07 17:40:45,068 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpkcclb4co\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpkcclb4co\temp_combined.mp4
2025-08-07 17:40:45,267 - INFO - 合并后的视频时长: 6.58秒，目标音频时长: 5.91秒
2025-08-07 17:40:45,267 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpkcclb4co\temp_combined.mp4 -ss 0 -to 5.906 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\22_2.mp4
2025-08-07 17:40:45,614 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:45,614 - INFO - 目标音频时长: 5.91秒
2025-08-07 17:40:45,614 - INFO - 实际视频时长: 5.94秒
2025-08-07 17:40:45,614 - INFO - 时长差异: 0.04秒 (0.63%)
2025-08-07 17:40:45,614 - INFO - ==========================================
2025-08-07 17:40:45,614 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:45,614 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\22_2.mp4
2025-08-07 17:40:45,615 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpkcclb4co
2025-08-07 17:40:45,658 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:45,658 - INFO -   - 音频时长: 5.91秒
2025-08-07 17:40:45,658 - INFO -   - 视频时长: 5.94秒
2025-08-07 17:40:45,658 - INFO -   - 时长差异: 0.04秒 (0.63%)
2025-08-07 17:40:45,658 - INFO - 
----- 处理字幕 #22 的方案 #3 -----
2025-08-07 17:40:45,658 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\22_3.mp4
2025-08-07 17:40:45,659 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpigxl_su7
2025-08-07 17:40:45,659 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\756.mp4 (确认存在: True)
2025-08-07 17:40:45,659 - INFO - 添加场景ID=756，时长=1.48秒，累计时长=1.48秒
2025-08-07 17:40:45,659 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\775.mp4 (确认存在: True)
2025-08-07 17:40:45,659 - INFO - 添加场景ID=775，时长=1.52秒，累计时长=3.00秒
2025-08-07 17:40:45,659 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\776.mp4 (确认存在: True)
2025-08-07 17:40:45,659 - INFO - 添加场景ID=776，时长=1.00秒，累计时长=4.00秒
2025-08-07 17:40:45,659 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\777.mp4 (确认存在: True)
2025-08-07 17:40:45,659 - INFO - 添加场景ID=777，时长=0.88秒，累计时长=4.88秒
2025-08-07 17:40:45,659 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\778.mp4 (确认存在: True)
2025-08-07 17:40:45,659 - INFO - 添加场景ID=778，时长=1.24秒，累计时长=6.12秒
2025-08-07 17:40:45,660 - INFO - 准备合并 5 个场景文件，总时长约 6.12秒
2025-08-07 17:40:45,660 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/756.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/775.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/776.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/777.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/778.mp4'

2025-08-07 17:40:45,660 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpigxl_su7\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpigxl_su7\temp_combined.mp4
2025-08-07 17:40:45,837 - INFO - 合并后的视频时长: 6.24秒，目标音频时长: 5.91秒
2025-08-07 17:40:45,837 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpigxl_su7\temp_combined.mp4 -ss 0 -to 5.906 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\22_3.mp4
2025-08-07 17:40:46,183 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:46,183 - INFO - 目标音频时长: 5.91秒
2025-08-07 17:40:46,183 - INFO - 实际视频时长: 5.94秒
2025-08-07 17:40:46,183 - INFO - 时长差异: 0.04秒 (0.63%)
2025-08-07 17:40:46,183 - INFO - ==========================================
2025-08-07 17:40:46,183 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:46,183 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\22_3.mp4
2025-08-07 17:40:46,184 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpigxl_su7
2025-08-07 17:40:46,230 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:46,230 - INFO -   - 音频时长: 5.91秒
2025-08-07 17:40:46,230 - INFO -   - 视频时长: 5.94秒
2025-08-07 17:40:46,230 - INFO -   - 时长差异: 0.04秒 (0.63%)
2025-08-07 17:40:46,230 - INFO - 
字幕 #22 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:46,230 - INFO - 生成的视频文件:
2025-08-07 17:40:46,230 - INFO -   1. F:/github/aicut_auto/newcut_ai\22_1.mp4
2025-08-07 17:40:46,230 - INFO -   2. F:/github/aicut_auto/newcut_ai\22_2.mp4
2025-08-07 17:40:46,230 - INFO -   3. F:/github/aicut_auto/newcut_ai\22_3.mp4
2025-08-07 17:40:46,230 - INFO - ========== 字幕 #22 处理结束 ==========

