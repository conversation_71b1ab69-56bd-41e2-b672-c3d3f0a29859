2025-08-07 17:41:01,919 - INFO - ========== 字幕 #34 处理开始 ==========
2025-08-07 17:41:01,919 - INFO - 字幕内容: 一计不成，二弟又生一计，他逼迫自己的未婚妻，去勾引大哥的助理，骗取一份至关重要的标书。
2025-08-07 17:41:01,919 - INFO - 字幕序号: [1203, 1212]
2025-08-07 17:41:01,919 - INFO - 音频文件详情:
2025-08-07 17:41:01,919 - INFO -   - 路径: output\34.wav
2025-08-07 17:41:01,919 - INFO -   - 时长: 5.22秒
2025-08-07 17:41:01,919 - INFO -   - 验证音频时长: 5.22秒
2025-08-07 17:41:01,919 - INFO - 字幕时间戳信息:
2025-08-07 17:41:01,919 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:01,919 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:01,919 - INFO -   - 根据生成的音频时长(5.22秒)已调整字幕时间戳
2025-08-07 17:41:01,919 - INFO - ========== 新模式：为字幕 #34 生成4套场景方案 ==========
2025-08-07 17:41:01,920 - INFO - 字幕序号列表: [1203, 1212]
2025-08-07 17:41:01,920 - INFO - 
--- 生成方案 #1：基于字幕序号 #1203 ---
2025-08-07 17:41:01,920 - INFO - 开始为单个字幕序号 #1203 匹配场景，目标时长: 5.22秒
2025-08-07 17:41:01,920 - INFO - 开始查找字幕序号 [1203] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:01,920 - INFO - 找到related_overlap场景: scene_id=1404, 字幕#1203
2025-08-07 17:41:01,921 - INFO - 字幕 #1203 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:01,921 - INFO - 字幕序号 #1203 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:01,921 - INFO - 选择第一个overlap场景作为起点: scene_id=1404
2025-08-07 17:41:01,921 - INFO - 添加起点场景: scene_id=1404, 时长=4.00秒, 累计时长=4.00秒
2025-08-07 17:41:01,921 - INFO - 起点场景时长不足，需要延伸填充 1.22秒
2025-08-07 17:41:01,921 - INFO - 起点场景在原始列表中的索引: 1403
2025-08-07 17:41:01,921 - INFO - 延伸添加场景: scene_id=1405 (裁剪至 1.22秒)
2025-08-07 17:41:01,921 - INFO - 累计时长: 5.22秒
2025-08-07 17:41:01,921 - INFO - 字幕序号 #1203 场景匹配完成，共选择 2 个场景，总时长: 5.22秒
2025-08-07 17:41:01,921 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-08-07 17:41:01,921 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-08-07 17:41:01,921 - INFO - 
--- 生成方案 #2：基于字幕序号 #1212 ---
2025-08-07 17:41:01,921 - INFO - 开始为单个字幕序号 #1212 匹配场景，目标时长: 5.22秒
2025-08-07 17:41:01,921 - INFO - 开始查找字幕序号 [1212] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:01,921 - INFO - 找到related_overlap场景: scene_id=1409, 字幕#1212
2025-08-07 17:41:01,922 - INFO - 字幕 #1212 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:01,922 - INFO - 字幕序号 #1212 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:01,922 - INFO - 选择第一个overlap场景作为起点: scene_id=1409
2025-08-07 17:41:01,922 - INFO - 添加起点场景: scene_id=1409, 时长=1.80秒, 累计时长=1.80秒
2025-08-07 17:41:01,922 - INFO - 起点场景时长不足，需要延伸填充 3.42秒
2025-08-07 17:41:01,922 - INFO - 起点场景在原始列表中的索引: 1408
2025-08-07 17:41:01,922 - INFO - 延伸添加场景: scene_id=1410 (完整时长 1.80秒)
2025-08-07 17:41:01,922 - INFO - 累计时长: 3.60秒
2025-08-07 17:41:01,922 - INFO - 延伸添加场景: scene_id=1411 (完整时长 1.40秒)
2025-08-07 17:41:01,922 - INFO - 累计时长: 5.00秒
2025-08-07 17:41:01,922 - INFO - 延伸添加场景: scene_id=1412 (裁剪至 0.22秒)
2025-08-07 17:41:01,922 - INFO - 累计时长: 5.22秒
2025-08-07 17:41:01,922 - INFO - 字幕序号 #1212 场景匹配完成，共选择 4 个场景，总时长: 5.22秒
2025-08-07 17:41:01,922 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-08-07 17:41:01,922 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:01,922 - INFO - ========== 当前模式：为字幕 #34 生成 1 套场景方案 ==========
2025-08-07 17:41:01,922 - INFO - 开始查找字幕序号 [1203, 1212] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:01,922 - INFO - 找到related_overlap场景: scene_id=1404, 字幕#1203
2025-08-07 17:41:01,922 - INFO - 找到related_overlap场景: scene_id=1409, 字幕#1212
2025-08-07 17:41:01,923 - INFO - 字幕 #1203 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:01,923 - INFO - 字幕 #1212 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:01,923 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-08-07 17:41:01,923 - INFO - 开始生成方案 #1
2025-08-07 17:41:01,923 - INFO - 方案 #1: 为字幕#1203选择初始化overlap场景id=1404
2025-08-07 17:41:01,923 - INFO - 方案 #1: 为字幕#1212选择初始化overlap场景id=1409
2025-08-07 17:41:01,923 - INFO - 方案 #1: 初始选择后，当前总时长=5.80秒
2025-08-07 17:41:01,923 - INFO - 方案 #1: 额外between选择后，当前总时长=5.80秒
2025-08-07 17:41:01,923 - INFO - 方案 #1: 场景总时长(5.80秒)大于音频时长(5.22秒)，需要裁剪
2025-08-07 17:41:01,923 - INFO - 调整前总时长: 5.80秒, 目标时长: 5.22秒
2025-08-07 17:41:01,924 - INFO - 需要裁剪 0.58秒
2025-08-07 17:41:01,924 - INFO - 裁剪最长场景ID=1404：从4.00秒裁剪至3.42秒
2025-08-07 17:41:01,924 - INFO - 调整后总时长: 5.22秒，与目标时长差异: 0.00秒
2025-08-07 17:41:01,924 - INFO - 方案 #1 调整/填充后最终总时长: 5.22秒
2025-08-07 17:41:01,924 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:01,924 - INFO - ========== 当前模式：字幕 #34 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:01,924 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:01,924 - INFO - ========== 新模式：字幕 #34 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:01,924 - INFO - 
----- 处理字幕 #34 的方案 #1 -----
2025-08-07 17:41:01,924 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\34_1.mp4
2025-08-07 17:41:01,924 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5t9vmspz
2025-08-07 17:41:01,925 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1404.mp4 (确认存在: True)
2025-08-07 17:41:01,925 - INFO - 添加场景ID=1404，时长=4.00秒，累计时长=4.00秒
2025-08-07 17:41:01,925 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1405.mp4 (确认存在: True)
2025-08-07 17:41:01,925 - INFO - 添加场景ID=1405，时长=1.56秒，累计时长=5.56秒
2025-08-07 17:41:01,925 - INFO - 准备合并 2 个场景文件，总时长约 5.56秒
2025-08-07 17:41:01,925 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1404.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1405.mp4'

2025-08-07 17:41:01,925 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp5t9vmspz\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp5t9vmspz\temp_combined.mp4
2025-08-07 17:41:02,044 - INFO - 合并后的视频时长: 5.61秒，目标音频时长: 5.22秒
2025-08-07 17:41:02,044 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp5t9vmspz\temp_combined.mp4 -ss 0 -to 5.217 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\34_1.mp4
2025-08-07 17:41:02,353 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:02,353 - INFO - 目标音频时长: 5.22秒
2025-08-07 17:41:02,353 - INFO - 实际视频时长: 5.26秒
2025-08-07 17:41:02,353 - INFO - 时长差异: 0.05秒 (0.88%)
2025-08-07 17:41:02,353 - INFO - ==========================================
2025-08-07 17:41:02,353 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:02,353 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\34_1.mp4
2025-08-07 17:41:02,354 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5t9vmspz
2025-08-07 17:41:02,397 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:02,397 - INFO -   - 音频时长: 5.22秒
2025-08-07 17:41:02,397 - INFO -   - 视频时长: 5.26秒
2025-08-07 17:41:02,397 - INFO -   - 时长差异: 0.05秒 (0.88%)
2025-08-07 17:41:02,397 - INFO - 
----- 处理字幕 #34 的方案 #2 -----
2025-08-07 17:41:02,397 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\34_2.mp4
2025-08-07 17:41:02,398 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5szh47rx
2025-08-07 17:41:02,398 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1409.mp4 (确认存在: True)
2025-08-07 17:41:02,398 - INFO - 添加场景ID=1409，时长=1.80秒，累计时长=1.80秒
2025-08-07 17:41:02,398 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1410.mp4 (确认存在: True)
2025-08-07 17:41:02,398 - INFO - 添加场景ID=1410，时长=1.80秒，累计时长=3.60秒
2025-08-07 17:41:02,398 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1411.mp4 (确认存在: True)
2025-08-07 17:41:02,398 - INFO - 添加场景ID=1411，时长=1.40秒，累计时长=5.00秒
2025-08-07 17:41:02,399 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1412.mp4 (确认存在: True)
2025-08-07 17:41:02,399 - INFO - 添加场景ID=1412，时长=2.56秒，累计时长=7.56秒
2025-08-07 17:41:02,399 - INFO - 准备合并 4 个场景文件，总时长约 7.56秒
2025-08-07 17:41:02,399 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1409.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1410.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1411.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1412.mp4'

2025-08-07 17:41:02,399 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp5szh47rx\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp5szh47rx\temp_combined.mp4
2025-08-07 17:41:02,553 - INFO - 合并后的视频时长: 7.65秒，目标音频时长: 5.22秒
2025-08-07 17:41:02,553 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp5szh47rx\temp_combined.mp4 -ss 0 -to 5.217 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\34_2.mp4
2025-08-07 17:41:02,854 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:02,854 - INFO - 目标音频时长: 5.22秒
2025-08-07 17:41:02,854 - INFO - 实际视频时长: 5.26秒
2025-08-07 17:41:02,854 - INFO - 时长差异: 0.05秒 (0.88%)
2025-08-07 17:41:02,854 - INFO - ==========================================
2025-08-07 17:41:02,854 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:02,854 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\34_2.mp4
2025-08-07 17:41:02,856 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5szh47rx
2025-08-07 17:41:02,898 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:02,898 - INFO -   - 音频时长: 5.22秒
2025-08-07 17:41:02,898 - INFO -   - 视频时长: 5.26秒
2025-08-07 17:41:02,898 - INFO -   - 时长差异: 0.05秒 (0.88%)
2025-08-07 17:41:02,899 - INFO - 
----- 处理字幕 #34 的方案 #3 -----
2025-08-07 17:41:02,899 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\34_3.mp4
2025-08-07 17:41:02,899 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0_c3ceqp
2025-08-07 17:41:02,899 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1404.mp4 (确认存在: True)
2025-08-07 17:41:02,899 - INFO - 添加场景ID=1404，时长=4.00秒，累计时长=4.00秒
2025-08-07 17:41:02,900 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1409.mp4 (确认存在: True)
2025-08-07 17:41:02,900 - INFO - 添加场景ID=1409，时长=1.80秒，累计时长=5.80秒
2025-08-07 17:41:02,900 - INFO - 准备合并 2 个场景文件，总时长约 5.80秒
2025-08-07 17:41:02,900 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1404.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1409.mp4'

2025-08-07 17:41:02,900 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp0_c3ceqp\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp0_c3ceqp\temp_combined.mp4
2025-08-07 17:41:03,020 - INFO - 合并后的视频时长: 5.85秒，目标音频时长: 5.22秒
2025-08-07 17:41:03,020 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp0_c3ceqp\temp_combined.mp4 -ss 0 -to 5.217 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\34_3.mp4
2025-08-07 17:41:03,328 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:03,328 - INFO - 目标音频时长: 5.22秒
2025-08-07 17:41:03,328 - INFO - 实际视频时长: 5.26秒
2025-08-07 17:41:03,328 - INFO - 时长差异: 0.05秒 (0.88%)
2025-08-07 17:41:03,328 - INFO - ==========================================
2025-08-07 17:41:03,328 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:03,328 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\34_3.mp4
2025-08-07 17:41:03,329 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp0_c3ceqp
2025-08-07 17:41:03,371 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:03,372 - INFO -   - 音频时长: 5.22秒
2025-08-07 17:41:03,372 - INFO -   - 视频时长: 5.26秒
2025-08-07 17:41:03,372 - INFO -   - 时长差异: 0.05秒 (0.88%)
2025-08-07 17:41:03,372 - INFO - 
字幕 #34 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:03,372 - INFO - 生成的视频文件:
2025-08-07 17:41:03,372 - INFO -   1. F:/github/aicut_auto/newcut_ai\34_1.mp4
2025-08-07 17:41:03,372 - INFO -   2. F:/github/aicut_auto/newcut_ai\34_2.mp4
2025-08-07 17:41:03,372 - INFO -   3. F:/github/aicut_auto/newcut_ai\34_3.mp4
2025-08-07 17:41:03,372 - INFO - ========== 字幕 #34 处理结束 ==========

