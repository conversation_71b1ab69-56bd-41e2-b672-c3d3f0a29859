2025-08-07 17:41:16,812 - INFO - ========== 字幕 #45 处理开始 ==========
2025-08-07 17:41:16,812 - INFO - 字幕内容: 不久后，跨国商会竞标会举行，这关系到楚家未来的命运，男人带着女孩一同前往。
2025-08-07 17:41:16,812 - INFO - 字幕序号: [1993, 2006]
2025-08-07 17:41:16,812 - INFO - 音频文件详情:
2025-08-07 17:41:16,812 - INFO -   - 路径: output\45.wav
2025-08-07 17:41:16,812 - INFO -   - 时长: 4.72秒
2025-08-07 17:41:16,813 - INFO -   - 验证音频时长: 4.72秒
2025-08-07 17:41:16,813 - INFO - 字幕时间戳信息:
2025-08-07 17:41:16,813 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:16,813 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:16,813 - INFO -   - 根据生成的音频时长(4.72秒)已调整字幕时间戳
2025-08-07 17:41:16,813 - INFO - ========== 新模式：为字幕 #45 生成4套场景方案 ==========
2025-08-07 17:41:16,813 - INFO - 字幕序号列表: [1993, 2006]
2025-08-07 17:41:16,813 - INFO - 
--- 生成方案 #1：基于字幕序号 #1993 ---
2025-08-07 17:41:16,813 - INFO - 开始为单个字幕序号 #1993 匹配场景，目标时长: 4.72秒
2025-08-07 17:41:16,813 - INFO - 开始查找字幕序号 [1993] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:16,814 - INFO - 找到related_overlap场景: scene_id=2266, 字幕#1993
2025-08-07 17:41:16,814 - INFO - 找到related_between场景: scene_id=2259, 字幕#1993
2025-08-07 17:41:16,814 - INFO - 找到related_between场景: scene_id=2260, 字幕#1993
2025-08-07 17:41:16,814 - INFO - 找到related_between场景: scene_id=2261, 字幕#1993
2025-08-07 17:41:16,814 - INFO - 找到related_between场景: scene_id=2262, 字幕#1993
2025-08-07 17:41:16,814 - INFO - 找到related_between场景: scene_id=2263, 字幕#1993
2025-08-07 17:41:16,814 - INFO - 找到related_between场景: scene_id=2264, 字幕#1993
2025-08-07 17:41:16,814 - INFO - 找到related_between场景: scene_id=2265, 字幕#1993
2025-08-07 17:41:16,814 - INFO - 字幕 #1993 找到 1 个overlap场景, 7 个between场景
2025-08-07 17:41:16,814 - INFO - 字幕序号 #1993 找到 1 个可用overlap场景, 7 个可用between场景
2025-08-07 17:41:16,814 - INFO - 选择第一个overlap场景作为起点: scene_id=2266
2025-08-07 17:41:16,814 - INFO - 添加起点场景: scene_id=2266, 时长=1.72秒, 累计时长=1.72秒
2025-08-07 17:41:16,814 - INFO - 起点场景时长不足，需要延伸填充 3.00秒
2025-08-07 17:41:16,814 - INFO - 起点场景在原始列表中的索引: 2265
2025-08-07 17:41:16,814 - INFO - 延伸添加场景: scene_id=2267 (完整时长 1.64秒)
2025-08-07 17:41:16,814 - INFO - 累计时长: 3.36秒
2025-08-07 17:41:16,814 - INFO - 延伸添加场景: scene_id=2268 (裁剪至 1.36秒)
2025-08-07 17:41:16,814 - INFO - 累计时长: 4.72秒
2025-08-07 17:41:16,814 - INFO - 字幕序号 #1993 场景匹配完成，共选择 3 个场景，总时长: 4.72秒
2025-08-07 17:41:16,814 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-08-07 17:41:16,814 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-08-07 17:41:16,814 - INFO - 
--- 生成方案 #2：基于字幕序号 #2006 ---
2025-08-07 17:41:16,814 - INFO - 开始为单个字幕序号 #2006 匹配场景，目标时长: 4.72秒
2025-08-07 17:41:16,814 - INFO - 开始查找字幕序号 [2006] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:16,815 - INFO - 找到related_overlap场景: scene_id=2274, 字幕#2006
2025-08-07 17:41:16,815 - INFO - 找到related_between场景: scene_id=2275, 字幕#2006
2025-08-07 17:41:16,815 - INFO - 找到related_between场景: scene_id=2276, 字幕#2006
2025-08-07 17:41:16,815 - INFO - 找到related_between场景: scene_id=2277, 字幕#2006
2025-08-07 17:41:16,816 - INFO - 字幕 #2006 找到 1 个overlap场景, 3 个between场景
2025-08-07 17:41:16,816 - INFO - 字幕序号 #2006 找到 1 个可用overlap场景, 3 个可用between场景
2025-08-07 17:41:16,816 - INFO - 选择第一个overlap场景作为起点: scene_id=2274
2025-08-07 17:41:16,816 - INFO - 添加起点场景: scene_id=2274, 时长=4.32秒, 累计时长=4.32秒
2025-08-07 17:41:16,816 - INFO - 起点场景时长不足，需要延伸填充 0.40秒
2025-08-07 17:41:16,816 - INFO - 起点场景在原始列表中的索引: 2273
2025-08-07 17:41:16,816 - INFO - 延伸添加场景: scene_id=2275 (裁剪至 0.40秒)
2025-08-07 17:41:16,816 - INFO - 累计时长: 4.72秒
2025-08-07 17:41:16,816 - INFO - 字幕序号 #2006 场景匹配完成，共选择 2 个场景，总时长: 4.72秒
2025-08-07 17:41:16,816 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-08-07 17:41:16,816 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:16,816 - INFO - ========== 当前模式：为字幕 #45 生成 1 套场景方案 ==========
2025-08-07 17:41:16,816 - INFO - 开始查找字幕序号 [1993, 2006] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:16,816 - INFO - 找到related_overlap场景: scene_id=2266, 字幕#1993
2025-08-07 17:41:16,816 - INFO - 找到related_overlap场景: scene_id=2274, 字幕#2006
2025-08-07 17:41:16,817 - INFO - 找到related_between场景: scene_id=2259, 字幕#1993
2025-08-07 17:41:16,817 - INFO - 找到related_between场景: scene_id=2260, 字幕#1993
2025-08-07 17:41:16,817 - INFO - 找到related_between场景: scene_id=2261, 字幕#1993
2025-08-07 17:41:16,817 - INFO - 找到related_between场景: scene_id=2262, 字幕#1993
2025-08-07 17:41:16,817 - INFO - 找到related_between场景: scene_id=2263, 字幕#1993
2025-08-07 17:41:16,817 - INFO - 找到related_between场景: scene_id=2264, 字幕#1993
2025-08-07 17:41:16,817 - INFO - 找到related_between场景: scene_id=2265, 字幕#1993
2025-08-07 17:41:16,817 - INFO - 找到related_between场景: scene_id=2275, 字幕#2006
2025-08-07 17:41:16,817 - INFO - 找到related_between场景: scene_id=2276, 字幕#2006
2025-08-07 17:41:16,817 - INFO - 找到related_between场景: scene_id=2277, 字幕#2006
2025-08-07 17:41:16,817 - INFO - 字幕 #1993 找到 1 个overlap场景, 7 个between场景
2025-08-07 17:41:16,817 - INFO - 字幕 #2006 找到 1 个overlap场景, 3 个between场景
2025-08-07 17:41:16,817 - INFO - 共收集 2 个未使用的overlap场景和 10 个未使用的between场景
2025-08-07 17:41:16,817 - INFO - 开始生成方案 #1
2025-08-07 17:41:16,817 - INFO - 方案 #1: 为字幕#1993选择初始化overlap场景id=2266
2025-08-07 17:41:16,817 - INFO - 方案 #1: 为字幕#2006选择初始化overlap场景id=2274
2025-08-07 17:41:16,817 - INFO - 方案 #1: 初始选择后，当前总时长=6.04秒
2025-08-07 17:41:16,817 - INFO - 方案 #1: 额外between选择后，当前总时长=6.04秒
2025-08-07 17:41:16,817 - INFO - 方案 #1: 场景总时长(6.04秒)大于音频时长(4.72秒)，需要裁剪
2025-08-07 17:41:16,817 - INFO - 调整前总时长: 6.04秒, 目标时长: 4.72秒
2025-08-07 17:41:16,817 - INFO - 需要裁剪 1.32秒
2025-08-07 17:41:16,817 - INFO - 裁剪最长场景ID=2274：从4.32秒裁剪至3.00秒
2025-08-07 17:41:16,817 - INFO - 调整后总时长: 4.72秒，与目标时长差异: 0.00秒
2025-08-07 17:41:16,817 - INFO - 方案 #1 调整/填充后最终总时长: 4.72秒
2025-08-07 17:41:16,817 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:16,817 - INFO - ========== 当前模式：字幕 #45 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:16,817 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:16,817 - INFO - ========== 新模式：字幕 #45 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:16,817 - INFO - 
----- 处理字幕 #45 的方案 #1 -----
2025-08-07 17:41:16,817 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\45_1.mp4
2025-08-07 17:41:16,818 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqsibjsdu
2025-08-07 17:41:16,818 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2266.mp4 (确认存在: True)
2025-08-07 17:41:16,818 - INFO - 添加场景ID=2266，时长=1.72秒，累计时长=1.72秒
2025-08-07 17:41:16,818 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2267.mp4 (确认存在: True)
2025-08-07 17:41:16,818 - INFO - 添加场景ID=2267，时长=1.64秒，累计时长=3.36秒
2025-08-07 17:41:16,818 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2268.mp4 (确认存在: True)
2025-08-07 17:41:16,818 - INFO - 添加场景ID=2268，时长=1.60秒，累计时长=4.96秒
2025-08-07 17:41:16,818 - INFO - 准备合并 3 个场景文件，总时长约 4.96秒
2025-08-07 17:41:16,819 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2266.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2267.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2268.mp4'

2025-08-07 17:41:16,819 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpqsibjsdu\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpqsibjsdu\temp_combined.mp4
2025-08-07 17:41:16,965 - INFO - 合并后的视频时长: 5.03秒，目标音频时长: 4.72秒
2025-08-07 17:41:16,965 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpqsibjsdu\temp_combined.mp4 -ss 0 -to 4.716 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\45_1.mp4
2025-08-07 17:41:17,256 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:17,256 - INFO - 目标音频时长: 4.72秒
2025-08-07 17:41:17,256 - INFO - 实际视频时长: 4.74秒
2025-08-07 17:41:17,256 - INFO - 时长差异: 0.03秒 (0.57%)
2025-08-07 17:41:17,256 - INFO - ==========================================
2025-08-07 17:41:17,256 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:17,256 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\45_1.mp4
2025-08-07 17:41:17,257 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqsibjsdu
2025-08-07 17:41:17,300 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:17,300 - INFO -   - 音频时长: 4.72秒
2025-08-07 17:41:17,300 - INFO -   - 视频时长: 4.74秒
2025-08-07 17:41:17,300 - INFO -   - 时长差异: 0.03秒 (0.57%)
2025-08-07 17:41:17,300 - INFO - 
----- 处理字幕 #45 的方案 #2 -----
2025-08-07 17:41:17,300 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\45_2.mp4
2025-08-07 17:41:17,300 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpewwxjqeq
2025-08-07 17:41:17,301 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2274.mp4 (确认存在: True)
2025-08-07 17:41:17,301 - INFO - 添加场景ID=2274，时长=4.32秒，累计时长=4.32秒
2025-08-07 17:41:17,301 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2275.mp4 (确认存在: True)
2025-08-07 17:41:17,301 - INFO - 添加场景ID=2275，时长=2.24秒，累计时长=6.56秒
2025-08-07 17:41:17,301 - INFO - 准备合并 2 个场景文件，总时长约 6.56秒
2025-08-07 17:41:17,301 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2274.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2275.mp4'

2025-08-07 17:41:17,301 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpewwxjqeq\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpewwxjqeq\temp_combined.mp4
2025-08-07 17:41:17,429 - INFO - 合并后的视频时长: 6.61秒，目标音频时长: 4.72秒
2025-08-07 17:41:17,429 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpewwxjqeq\temp_combined.mp4 -ss 0 -to 4.716 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\45_2.mp4
2025-08-07 17:41:17,705 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:17,705 - INFO - 目标音频时长: 4.72秒
2025-08-07 17:41:17,705 - INFO - 实际视频时长: 4.74秒
2025-08-07 17:41:17,705 - INFO - 时长差异: 0.03秒 (0.57%)
2025-08-07 17:41:17,705 - INFO - ==========================================
2025-08-07 17:41:17,705 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:17,705 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\45_2.mp4
2025-08-07 17:41:17,706 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpewwxjqeq
2025-08-07 17:41:17,750 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:17,750 - INFO -   - 音频时长: 4.72秒
2025-08-07 17:41:17,750 - INFO -   - 视频时长: 4.74秒
2025-08-07 17:41:17,750 - INFO -   - 时长差异: 0.03秒 (0.57%)
2025-08-07 17:41:17,750 - INFO - 
----- 处理字幕 #45 的方案 #3 -----
2025-08-07 17:41:17,751 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\45_3.mp4
2025-08-07 17:41:17,751 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbgddackp
2025-08-07 17:41:17,751 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2266.mp4 (确认存在: True)
2025-08-07 17:41:17,751 - INFO - 添加场景ID=2266，时长=1.72秒，累计时长=1.72秒
2025-08-07 17:41:17,751 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2274.mp4 (确认存在: True)
2025-08-07 17:41:17,751 - INFO - 添加场景ID=2274，时长=4.32秒，累计时长=6.04秒
2025-08-07 17:41:17,751 - INFO - 准备合并 2 个场景文件，总时长约 6.04秒
2025-08-07 17:41:17,751 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2266.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2274.mp4'

2025-08-07 17:41:17,751 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpbgddackp\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpbgddackp\temp_combined.mp4
2025-08-07 17:41:17,872 - INFO - 合并后的视频时长: 6.09秒，目标音频时长: 4.72秒
2025-08-07 17:41:17,872 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpbgddackp\temp_combined.mp4 -ss 0 -to 4.716 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\45_3.mp4
2025-08-07 17:41:18,163 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:18,163 - INFO - 目标音频时长: 4.72秒
2025-08-07 17:41:18,163 - INFO - 实际视频时长: 4.74秒
2025-08-07 17:41:18,163 - INFO - 时长差异: 0.03秒 (0.57%)
2025-08-07 17:41:18,163 - INFO - ==========================================
2025-08-07 17:41:18,163 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:18,163 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\45_3.mp4
2025-08-07 17:41:18,163 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpbgddackp
2025-08-07 17:41:18,206 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:18,206 - INFO -   - 音频时长: 4.72秒
2025-08-07 17:41:18,206 - INFO -   - 视频时长: 4.74秒
2025-08-07 17:41:18,206 - INFO -   - 时长差异: 0.03秒 (0.57%)
2025-08-07 17:41:18,206 - INFO - 
字幕 #45 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:18,206 - INFO - 生成的视频文件:
2025-08-07 17:41:18,206 - INFO -   1. F:/github/aicut_auto/newcut_ai\45_1.mp4
2025-08-07 17:41:18,206 - INFO -   2. F:/github/aicut_auto/newcut_ai\45_2.mp4
2025-08-07 17:41:18,206 - INFO -   3. F:/github/aicut_auto/newcut_ai\45_3.mp4
2025-08-07 17:41:18,206 - INFO - ========== 字幕 #45 处理结束 ==========

