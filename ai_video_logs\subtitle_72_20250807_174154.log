2025-08-07 17:41:54,797 - INFO - ========== 字幕 #72 处理开始 ==========
2025-08-07 17:41:54,797 - INFO - 字幕内容: 女孩傲娇地表示，念在他“欺负”自己多次的份上，罚他以后要定期给自己送零食上山。
2025-08-07 17:41:54,797 - INFO - 字幕序号: [3112, 3121]
2025-08-07 17:41:54,797 - INFO - 音频文件详情:
2025-08-07 17:41:54,797 - INFO -   - 路径: output\72.wav
2025-08-07 17:41:54,797 - INFO -   - 时长: 5.18秒
2025-08-07 17:41:54,798 - INFO -   - 验证音频时长: 5.18秒
2025-08-07 17:41:54,798 - INFO - 字幕时间戳信息:
2025-08-07 17:41:54,798 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:54,798 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:54,798 - INFO -   - 根据生成的音频时长(5.18秒)已调整字幕时间戳
2025-08-07 17:41:54,798 - INFO - ========== 新模式：为字幕 #72 生成4套场景方案 ==========
2025-08-07 17:41:54,798 - INFO - 字幕序号列表: [3112, 3121]
2025-08-07 17:41:54,798 - INFO - 
--- 生成方案 #1：基于字幕序号 #3112 ---
2025-08-07 17:41:54,798 - INFO - 开始为单个字幕序号 #3112 匹配场景，目标时长: 5.18秒
2025-08-07 17:41:54,798 - INFO - 开始查找字幕序号 [3112] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:54,799 - INFO - 找到related_overlap场景: scene_id=3461, 字幕#3112
2025-08-07 17:41:54,799 - INFO - 找到related_overlap场景: scene_id=3462, 字幕#3112
2025-08-07 17:41:54,800 - INFO - 找到related_between场景: scene_id=3460, 字幕#3112
2025-08-07 17:41:54,800 - INFO - 字幕 #3112 找到 2 个overlap场景, 1 个between场景
2025-08-07 17:41:54,800 - INFO - 字幕序号 #3112 找到 2 个可用overlap场景, 1 个可用between场景
2025-08-07 17:41:54,800 - INFO - 选择第一个overlap场景作为起点: scene_id=3461
2025-08-07 17:41:54,800 - INFO - 添加起点场景: scene_id=3461, 时长=1.00秒, 累计时长=1.00秒
2025-08-07 17:41:54,800 - INFO - 起点场景时长不足，需要延伸填充 4.18秒
2025-08-07 17:41:54,800 - INFO - 起点场景在原始列表中的索引: 3460
2025-08-07 17:41:54,801 - INFO - 延伸添加场景: scene_id=3462 (完整时长 2.12秒)
2025-08-07 17:41:54,801 - INFO - 累计时长: 3.12秒
2025-08-07 17:41:54,801 - INFO - 延伸添加场景: scene_id=3463 (裁剪至 2.06秒)
2025-08-07 17:41:54,801 - INFO - 累计时长: 5.18秒
2025-08-07 17:41:54,801 - INFO - 字幕序号 #3112 场景匹配完成，共选择 3 个场景，总时长: 5.18秒
2025-08-07 17:41:54,801 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-08-07 17:41:54,801 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-08-07 17:41:54,801 - INFO - 
--- 生成方案 #2：基于字幕序号 #3121 ---
2025-08-07 17:41:54,801 - INFO - 开始为单个字幕序号 #3121 匹配场景，目标时长: 5.18秒
2025-08-07 17:41:54,801 - INFO - 开始查找字幕序号 [3121] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:54,802 - INFO - 找到related_overlap场景: scene_id=3473, 字幕#3121
2025-08-07 17:41:54,802 - INFO - 找到related_overlap场景: scene_id=3474, 字幕#3121
2025-08-07 17:41:54,803 - INFO - 找到related_between场景: scene_id=3475, 字幕#3121
2025-08-07 17:41:54,803 - INFO - 字幕 #3121 找到 2 个overlap场景, 1 个between场景
2025-08-07 17:41:54,803 - INFO - 字幕序号 #3121 找到 2 个可用overlap场景, 1 个可用between场景
2025-08-07 17:41:54,803 - INFO - 选择第一个overlap场景作为起点: scene_id=3473
2025-08-07 17:41:54,803 - INFO - 添加起点场景: scene_id=3473, 时长=0.80秒, 累计时长=0.80秒
2025-08-07 17:41:54,803 - INFO - 起点场景时长不足，需要延伸填充 4.38秒
2025-08-07 17:41:54,803 - INFO - 起点场景在原始列表中的索引: 3472
2025-08-07 17:41:54,803 - INFO - 延伸添加场景: scene_id=3474 (完整时长 1.20秒)
2025-08-07 17:41:54,803 - INFO - 累计时长: 2.00秒
2025-08-07 17:41:54,803 - INFO - 延伸添加场景: scene_id=3475 (完整时长 1.08秒)
2025-08-07 17:41:54,803 - INFO - 累计时长: 3.08秒
2025-08-07 17:41:54,803 - INFO - 延伸添加场景: scene_id=3476 (裁剪至 2.10秒)
2025-08-07 17:41:54,803 - INFO - 累计时长: 5.18秒
2025-08-07 17:41:54,803 - INFO - 字幕序号 #3121 场景匹配完成，共选择 4 个场景，总时长: 5.18秒
2025-08-07 17:41:54,803 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-08-07 17:41:54,803 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:54,803 - INFO - ========== 当前模式：为字幕 #72 生成 1 套场景方案 ==========
2025-08-07 17:41:54,803 - INFO - 开始查找字幕序号 [3112, 3121] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:54,804 - INFO - 找到related_overlap场景: scene_id=3461, 字幕#3112
2025-08-07 17:41:54,804 - INFO - 找到related_overlap场景: scene_id=3462, 字幕#3112
2025-08-07 17:41:54,804 - INFO - 找到related_overlap场景: scene_id=3473, 字幕#3121
2025-08-07 17:41:54,804 - INFO - 找到related_overlap场景: scene_id=3474, 字幕#3121
2025-08-07 17:41:54,805 - INFO - 找到related_between场景: scene_id=3460, 字幕#3112
2025-08-07 17:41:54,805 - INFO - 找到related_between场景: scene_id=3475, 字幕#3121
2025-08-07 17:41:54,805 - INFO - 字幕 #3112 找到 2 个overlap场景, 1 个between场景
2025-08-07 17:41:54,805 - INFO - 字幕 #3121 找到 2 个overlap场景, 1 个between场景
2025-08-07 17:41:54,805 - INFO - 共收集 4 个未使用的overlap场景和 2 个未使用的between场景
2025-08-07 17:41:54,805 - INFO - 开始生成方案 #1
2025-08-07 17:41:54,805 - INFO - 方案 #1: 为字幕#3112选择初始化overlap场景id=3462
2025-08-07 17:41:54,805 - INFO - 方案 #1: 为字幕#3121选择初始化overlap场景id=3474
2025-08-07 17:41:54,805 - INFO - 方案 #1: 初始选择后，当前总时长=3.32秒
2025-08-07 17:41:54,805 - INFO - 方案 #1: 额外添加overlap场景id=3473, 当前总时长=4.12秒
2025-08-07 17:41:54,805 - INFO - 方案 #1: 额外添加overlap场景id=3461, 当前总时长=5.12秒
2025-08-07 17:41:54,805 - INFO - 方案 #1: 额外between选择后，当前总时长=5.12秒
2025-08-07 17:41:54,805 - INFO - 方案 #1: 额外添加between场景id=3460, 当前总时长=6.04秒
2025-08-07 17:41:54,805 - INFO - 方案 #1: 场景总时长(6.04秒)大于音频时长(5.18秒)，需要裁剪
2025-08-07 17:41:54,805 - INFO - 调整前总时长: 6.04秒, 目标时长: 5.18秒
2025-08-07 17:41:54,805 - INFO - 需要裁剪 0.86秒
2025-08-07 17:41:54,805 - INFO - 裁剪最长场景ID=3462：从2.12秒裁剪至1.26秒
2025-08-07 17:41:54,805 - INFO - 调整后总时长: 5.18秒，与目标时长差异: 0.00秒
2025-08-07 17:41:54,805 - INFO - 方案 #1 调整/填充后最终总时长: 5.18秒
2025-08-07 17:41:54,805 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:54,805 - INFO - ========== 当前模式：字幕 #72 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:54,805 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:54,805 - INFO - ========== 新模式：字幕 #72 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:54,805 - INFO - 
----- 处理字幕 #72 的方案 #1 -----
2025-08-07 17:41:54,805 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\72_1.mp4
2025-08-07 17:41:54,806 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmphhf5sywa
2025-08-07 17:41:54,806 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3461.mp4 (确认存在: True)
2025-08-07 17:41:54,806 - INFO - 添加场景ID=3461，时长=1.00秒，累计时长=1.00秒
2025-08-07 17:41:54,806 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3462.mp4 (确认存在: True)
2025-08-07 17:41:54,806 - INFO - 添加场景ID=3462，时长=2.12秒，累计时长=3.12秒
2025-08-07 17:41:54,807 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3463.mp4 (确认存在: True)
2025-08-07 17:41:54,807 - INFO - 添加场景ID=3463，时长=2.76秒，累计时长=5.88秒
2025-08-07 17:41:54,807 - INFO - 准备合并 3 个场景文件，总时长约 5.88秒
2025-08-07 17:41:54,807 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3461.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3462.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3463.mp4'

2025-08-07 17:41:54,807 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmphhf5sywa\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmphhf5sywa\temp_combined.mp4
2025-08-07 17:41:54,955 - INFO - 合并后的视频时长: 5.95秒，目标音频时长: 5.18秒
2025-08-07 17:41:54,955 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmphhf5sywa\temp_combined.mp4 -ss 0 -to 5.182 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\72_1.mp4
2025-08-07 17:41:55,290 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:55,290 - INFO - 目标音频时长: 5.18秒
2025-08-07 17:41:55,290 - INFO - 实际视频时长: 5.22秒
2025-08-07 17:41:55,290 - INFO - 时长差异: 0.04秒 (0.79%)
2025-08-07 17:41:55,290 - INFO - ==========================================
2025-08-07 17:41:55,290 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:55,290 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\72_1.mp4
2025-08-07 17:41:55,291 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmphhf5sywa
2025-08-07 17:41:55,335 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:55,335 - INFO -   - 音频时长: 5.18秒
2025-08-07 17:41:55,335 - INFO -   - 视频时长: 5.22秒
2025-08-07 17:41:55,335 - INFO -   - 时长差异: 0.04秒 (0.79%)
2025-08-07 17:41:55,335 - INFO - 
----- 处理字幕 #72 的方案 #2 -----
2025-08-07 17:41:55,335 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\72_2.mp4
2025-08-07 17:41:55,337 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxo7r7oyf
2025-08-07 17:41:55,337 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3473.mp4 (确认存在: True)
2025-08-07 17:41:55,337 - INFO - 添加场景ID=3473，时长=0.80秒，累计时长=0.80秒
2025-08-07 17:41:55,338 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3474.mp4 (确认存在: True)
2025-08-07 17:41:55,338 - INFO - 添加场景ID=3474，时长=1.20秒，累计时长=2.00秒
2025-08-07 17:41:55,338 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3475.mp4 (确认存在: True)
2025-08-07 17:41:55,338 - INFO - 添加场景ID=3475，时长=1.08秒，累计时长=3.08秒
2025-08-07 17:41:55,338 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3476.mp4 (确认存在: True)
2025-08-07 17:41:55,338 - INFO - 添加场景ID=3476，时长=2.92秒，累计时长=6.00秒
2025-08-07 17:41:55,338 - INFO - 准备合并 4 个场景文件，总时长约 6.00秒
2025-08-07 17:41:55,338 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3473.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3474.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3475.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3476.mp4'

2025-08-07 17:41:55,338 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpxo7r7oyf\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpxo7r7oyf\temp_combined.mp4
2025-08-07 17:41:55,507 - INFO - 合并后的视频时长: 6.09秒，目标音频时长: 5.18秒
2025-08-07 17:41:55,508 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpxo7r7oyf\temp_combined.mp4 -ss 0 -to 5.182 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\72_2.mp4
2025-08-07 17:41:55,830 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:55,830 - INFO - 目标音频时长: 5.18秒
2025-08-07 17:41:55,830 - INFO - 实际视频时长: 5.22秒
2025-08-07 17:41:55,830 - INFO - 时长差异: 0.04秒 (0.79%)
2025-08-07 17:41:55,830 - INFO - ==========================================
2025-08-07 17:41:55,830 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:55,830 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\72_2.mp4
2025-08-07 17:41:55,831 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxo7r7oyf
2025-08-07 17:41:55,873 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:55,873 - INFO -   - 音频时长: 5.18秒
2025-08-07 17:41:55,873 - INFO -   - 视频时长: 5.22秒
2025-08-07 17:41:55,873 - INFO -   - 时长差异: 0.04秒 (0.79%)
2025-08-07 17:41:55,873 - INFO - 
----- 处理字幕 #72 的方案 #3 -----
2025-08-07 17:41:55,873 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\72_3.mp4
2025-08-07 17:41:55,874 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpzmodrwi8
2025-08-07 17:41:55,874 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3462.mp4 (确认存在: True)
2025-08-07 17:41:55,874 - INFO - 添加场景ID=3462，时长=2.12秒，累计时长=2.12秒
2025-08-07 17:41:55,874 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3474.mp4 (确认存在: True)
2025-08-07 17:41:55,874 - INFO - 添加场景ID=3474，时长=1.20秒，累计时长=3.32秒
2025-08-07 17:41:55,874 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3473.mp4 (确认存在: True)
2025-08-07 17:41:55,874 - INFO - 添加场景ID=3473，时长=0.80秒，累计时长=4.12秒
2025-08-07 17:41:55,874 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3461.mp4 (确认存在: True)
2025-08-07 17:41:55,874 - INFO - 添加场景ID=3461，时长=1.00秒，累计时长=5.12秒
2025-08-07 17:41:55,874 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3460.mp4 (确认存在: True)
2025-08-07 17:41:55,874 - INFO - 添加场景ID=3460，时长=0.92秒，累计时长=6.04秒
2025-08-07 17:41:55,874 - INFO - 准备合并 5 个场景文件，总时长约 6.04秒
2025-08-07 17:41:55,874 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3462.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3474.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3473.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3461.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3460.mp4'

2025-08-07 17:41:55,874 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpzmodrwi8\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpzmodrwi8\temp_combined.mp4
2025-08-07 17:41:56,037 - INFO - 合并后的视频时长: 6.16秒，目标音频时长: 5.18秒
2025-08-07 17:41:56,037 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpzmodrwi8\temp_combined.mp4 -ss 0 -to 5.182 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\72_3.mp4
2025-08-07 17:41:56,375 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:56,375 - INFO - 目标音频时长: 5.18秒
2025-08-07 17:41:56,375 - INFO - 实际视频时长: 5.22秒
2025-08-07 17:41:56,375 - INFO - 时长差异: 0.04秒 (0.79%)
2025-08-07 17:41:56,375 - INFO - ==========================================
2025-08-07 17:41:56,375 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:56,375 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\72_3.mp4
2025-08-07 17:41:56,376 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpzmodrwi8
2025-08-07 17:41:56,429 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:56,429 - INFO -   - 音频时长: 5.18秒
2025-08-07 17:41:56,429 - INFO -   - 视频时长: 5.22秒
2025-08-07 17:41:56,429 - INFO -   - 时长差异: 0.04秒 (0.79%)
2025-08-07 17:41:56,429 - INFO - 
字幕 #72 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:56,429 - INFO - 生成的视频文件:
2025-08-07 17:41:56,429 - INFO -   1. F:/github/aicut_auto/newcut_ai\72_1.mp4
2025-08-07 17:41:56,429 - INFO -   2. F:/github/aicut_auto/newcut_ai\72_2.mp4
2025-08-07 17:41:56,429 - INFO -   3. F:/github/aicut_auto/newcut_ai\72_3.mp4
2025-08-07 17:41:56,429 - INFO - ========== 字幕 #72 处理结束 ==========

