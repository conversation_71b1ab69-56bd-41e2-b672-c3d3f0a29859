2025-08-07 17:41:37,502 - INFO - ========== 字幕 #60 处理开始 ==========
2025-08-07 17:41:37,502 - INFO - 字幕内容: 签约仪式上，二弟的眼神充满了杀意，他已经布下了最后的陷阱，要让男人死无葬身之地。
2025-08-07 17:41:37,502 - INFO - 字幕序号: [2886, 2889]
2025-08-07 17:41:37,502 - INFO - 音频文件详情:
2025-08-07 17:41:37,502 - INFO -   - 路径: output\60.wav
2025-08-07 17:41:37,502 - INFO -   - 时长: 5.15秒
2025-08-07 17:41:37,502 - INFO -   - 验证音频时长: 5.15秒
2025-08-07 17:41:37,502 - INFO - 字幕时间戳信息:
2025-08-07 17:41:37,503 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:37,503 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:37,503 - INFO -   - 根据生成的音频时长(5.15秒)已调整字幕时间戳
2025-08-07 17:41:37,503 - INFO - ========== 新模式：为字幕 #60 生成4套场景方案 ==========
2025-08-07 17:41:37,503 - INFO - 字幕序号列表: [2886, 2889]
2025-08-07 17:41:37,503 - INFO - 
--- 生成方案 #1：基于字幕序号 #2886 ---
2025-08-07 17:41:37,503 - INFO - 开始为单个字幕序号 #2886 匹配场景，目标时长: 5.15秒
2025-08-07 17:41:37,503 - INFO - 开始查找字幕序号 [2886] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:37,504 - INFO - 找到related_overlap场景: scene_id=3101, 字幕#2886
2025-08-07 17:41:37,504 - INFO - 字幕 #2886 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:37,504 - INFO - 字幕序号 #2886 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:37,504 - INFO - 选择第一个overlap场景作为起点: scene_id=3101
2025-08-07 17:41:37,504 - INFO - 添加起点场景: scene_id=3101, 时长=4.44秒, 累计时长=4.44秒
2025-08-07 17:41:37,504 - INFO - 起点场景时长不足，需要延伸填充 0.71秒
2025-08-07 17:41:37,505 - INFO - 起点场景在原始列表中的索引: 3100
2025-08-07 17:41:37,505 - INFO - 延伸添加场景: scene_id=3102 (裁剪至 0.71秒)
2025-08-07 17:41:37,505 - INFO - 累计时长: 5.15秒
2025-08-07 17:41:37,505 - INFO - 字幕序号 #2886 场景匹配完成，共选择 2 个场景，总时长: 5.15秒
2025-08-07 17:41:37,505 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-08-07 17:41:37,505 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-08-07 17:41:37,505 - INFO - 
--- 生成方案 #2：基于字幕序号 #2889 ---
2025-08-07 17:41:37,505 - INFO - 开始为单个字幕序号 #2889 匹配场景，目标时长: 5.15秒
2025-08-07 17:41:37,505 - INFO - 开始查找字幕序号 [2889] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:37,505 - INFO - 找到related_overlap场景: scene_id=3104, 字幕#2889
2025-08-07 17:41:37,505 - INFO - 找到related_between场景: scene_id=3102, 字幕#2889
2025-08-07 17:41:37,505 - INFO - 找到related_between场景: scene_id=3103, 字幕#2889
2025-08-07 17:41:37,505 - INFO - 找到related_between场景: scene_id=3105, 字幕#2889
2025-08-07 17:41:37,505 - INFO - 找到related_between场景: scene_id=3106, 字幕#2889
2025-08-07 17:41:37,505 - INFO - 找到related_between场景: scene_id=3107, 字幕#2889
2025-08-07 17:41:37,505 - INFO - 找到related_between场景: scene_id=3108, 字幕#2889
2025-08-07 17:41:37,505 - INFO - 字幕 #2889 找到 1 个overlap场景, 6 个between场景
2025-08-07 17:41:37,505 - INFO - 字幕序号 #2889 找到 1 个可用overlap场景, 5 个可用between场景
2025-08-07 17:41:37,505 - INFO - 选择第一个overlap场景作为起点: scene_id=3104
2025-08-07 17:41:37,505 - INFO - 添加起点场景: scene_id=3104, 时长=2.08秒, 累计时长=2.08秒
2025-08-07 17:41:37,505 - INFO - 起点场景时长不足，需要延伸填充 3.07秒
2025-08-07 17:41:37,505 - INFO - 起点场景在原始列表中的索引: 3103
2025-08-07 17:41:37,505 - INFO - 延伸添加场景: scene_id=3105 (完整时长 1.08秒)
2025-08-07 17:41:37,505 - INFO - 累计时长: 3.16秒
2025-08-07 17:41:37,506 - INFO - 延伸添加场景: scene_id=3106 (完整时长 1.16秒)
2025-08-07 17:41:37,506 - INFO - 累计时长: 4.32秒
2025-08-07 17:41:37,506 - INFO - 延伸添加场景: scene_id=3107 (裁剪至 0.84秒)
2025-08-07 17:41:37,506 - INFO - 累计时长: 5.15秒
2025-08-07 17:41:37,506 - INFO - 字幕序号 #2889 场景匹配完成，共选择 4 个场景，总时长: 5.15秒
2025-08-07 17:41:37,506 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-08-07 17:41:37,506 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:37,506 - INFO - ========== 当前模式：为字幕 #60 生成 1 套场景方案 ==========
2025-08-07 17:41:37,506 - INFO - 开始查找字幕序号 [2886, 2889] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:37,506 - INFO - 找到related_overlap场景: scene_id=3101, 字幕#2886
2025-08-07 17:41:37,506 - INFO - 找到related_overlap场景: scene_id=3104, 字幕#2889
2025-08-07 17:41:37,507 - INFO - 找到related_between场景: scene_id=3102, 字幕#2889
2025-08-07 17:41:37,507 - INFO - 找到related_between场景: scene_id=3103, 字幕#2889
2025-08-07 17:41:37,507 - INFO - 找到related_between场景: scene_id=3105, 字幕#2889
2025-08-07 17:41:37,507 - INFO - 找到related_between场景: scene_id=3106, 字幕#2889
2025-08-07 17:41:37,507 - INFO - 找到related_between场景: scene_id=3107, 字幕#2889
2025-08-07 17:41:37,507 - INFO - 找到related_between场景: scene_id=3108, 字幕#2889
2025-08-07 17:41:37,507 - INFO - 字幕 #2886 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:37,507 - INFO - 字幕 #2889 找到 1 个overlap场景, 6 个between场景
2025-08-07 17:41:37,507 - INFO - 共收集 2 个未使用的overlap场景和 6 个未使用的between场景
2025-08-07 17:41:37,507 - INFO - 开始生成方案 #1
2025-08-07 17:41:37,507 - INFO - 方案 #1: 为字幕#2886选择初始化overlap场景id=3101
2025-08-07 17:41:37,507 - INFO - 方案 #1: 为字幕#2889选择初始化overlap场景id=3104
2025-08-07 17:41:37,507 - INFO - 方案 #1: 初始选择后，当前总时长=6.52秒
2025-08-07 17:41:37,507 - INFO - 方案 #1: 额外between选择后，当前总时长=6.52秒
2025-08-07 17:41:37,507 - INFO - 方案 #1: 场景总时长(6.52秒)大于音频时长(5.15秒)，需要裁剪
2025-08-07 17:41:37,507 - INFO - 调整前总时长: 6.52秒, 目标时长: 5.15秒
2025-08-07 17:41:37,507 - INFO - 需要裁剪 1.37秒
2025-08-07 17:41:37,507 - INFO - 裁剪最长场景ID=3101：从4.44秒裁剪至3.07秒
2025-08-07 17:41:37,507 - INFO - 调整后总时长: 5.15秒，与目标时长差异: 0.00秒
2025-08-07 17:41:37,507 - INFO - 方案 #1 调整/填充后最终总时长: 5.15秒
2025-08-07 17:41:37,507 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:37,507 - INFO - ========== 当前模式：字幕 #60 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:37,507 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:37,507 - INFO - ========== 新模式：字幕 #60 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:37,507 - INFO - 
----- 处理字幕 #60 的方案 #1 -----
2025-08-07 17:41:37,507 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\60_1.mp4
2025-08-07 17:41:37,508 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxxnwqzvv
2025-08-07 17:41:37,508 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3101.mp4 (确认存在: True)
2025-08-07 17:41:37,508 - INFO - 添加场景ID=3101，时长=4.44秒，累计时长=4.44秒
2025-08-07 17:41:37,508 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3102.mp4 (确认存在: True)
2025-08-07 17:41:37,508 - INFO - 添加场景ID=3102，时长=4.08秒，累计时长=8.52秒
2025-08-07 17:41:37,508 - INFO - 场景总时长(8.52秒)已达到音频时长(5.15秒)的1.5倍，停止添加场景
2025-08-07 17:41:37,508 - INFO - 准备合并 2 个场景文件，总时长约 8.52秒
2025-08-07 17:41:37,508 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3101.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3102.mp4'

2025-08-07 17:41:37,508 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpxxnwqzvv\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpxxnwqzvv\temp_combined.mp4
2025-08-07 17:41:37,635 - INFO - 合并后的视频时长: 8.57秒，目标音频时长: 5.15秒
2025-08-07 17:41:37,635 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpxxnwqzvv\temp_combined.mp4 -ss 0 -to 5.153 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\60_1.mp4
2025-08-07 17:41:37,963 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:37,963 - INFO - 目标音频时长: 5.15秒
2025-08-07 17:41:37,963 - INFO - 实际视频时长: 5.18秒
2025-08-07 17:41:37,963 - INFO - 时长差异: 0.03秒 (0.58%)
2025-08-07 17:41:37,963 - INFO - ==========================================
2025-08-07 17:41:37,963 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:37,963 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\60_1.mp4
2025-08-07 17:41:37,963 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpxxnwqzvv
2025-08-07 17:41:38,011 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:38,011 - INFO -   - 音频时长: 5.15秒
2025-08-07 17:41:38,011 - INFO -   - 视频时长: 5.18秒
2025-08-07 17:41:38,011 - INFO -   - 时长差异: 0.03秒 (0.58%)
2025-08-07 17:41:38,011 - INFO - 
----- 处理字幕 #60 的方案 #2 -----
2025-08-07 17:41:38,011 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\60_2.mp4
2025-08-07 17:41:38,012 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp63k7z31c
2025-08-07 17:41:38,012 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3104.mp4 (确认存在: True)
2025-08-07 17:41:38,012 - INFO - 添加场景ID=3104，时长=2.08秒，累计时长=2.08秒
2025-08-07 17:41:38,012 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3105.mp4 (确认存在: True)
2025-08-07 17:41:38,012 - INFO - 添加场景ID=3105，时长=1.08秒，累计时长=3.16秒
2025-08-07 17:41:38,012 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3106.mp4 (确认存在: True)
2025-08-07 17:41:38,012 - INFO - 添加场景ID=3106，时长=1.16秒，累计时长=4.32秒
2025-08-07 17:41:38,012 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3107.mp4 (确认存在: True)
2025-08-07 17:41:38,012 - INFO - 添加场景ID=3107，时长=6.56秒，累计时长=10.88秒
2025-08-07 17:41:38,012 - INFO - 场景总时长(10.88秒)已达到音频时长(5.15秒)的1.5倍，停止添加场景
2025-08-07 17:41:38,012 - INFO - 准备合并 4 个场景文件，总时长约 10.88秒
2025-08-07 17:41:38,012 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3104.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3105.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3106.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3107.mp4'

2025-08-07 17:41:38,013 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp63k7z31c\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp63k7z31c\temp_combined.mp4
2025-08-07 17:41:38,158 - INFO - 合并后的视频时长: 10.97秒，目标音频时长: 5.15秒
2025-08-07 17:41:38,158 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp63k7z31c\temp_combined.mp4 -ss 0 -to 5.153 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\60_2.mp4
2025-08-07 17:41:38,485 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:38,485 - INFO - 目标音频时长: 5.15秒
2025-08-07 17:41:38,486 - INFO - 实际视频时长: 5.18秒
2025-08-07 17:41:38,486 - INFO - 时长差异: 0.03秒 (0.58%)
2025-08-07 17:41:38,486 - INFO - ==========================================
2025-08-07 17:41:38,486 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:38,486 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\60_2.mp4
2025-08-07 17:41:38,486 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp63k7z31c
2025-08-07 17:41:38,530 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:38,530 - INFO -   - 音频时长: 5.15秒
2025-08-07 17:41:38,530 - INFO -   - 视频时长: 5.18秒
2025-08-07 17:41:38,530 - INFO -   - 时长差异: 0.03秒 (0.58%)
2025-08-07 17:41:38,530 - INFO - 
----- 处理字幕 #60 的方案 #3 -----
2025-08-07 17:41:38,530 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\60_3.mp4
2025-08-07 17:41:38,530 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpu5qy6od4
2025-08-07 17:41:38,531 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3101.mp4 (确认存在: True)
2025-08-07 17:41:38,531 - INFO - 添加场景ID=3101，时长=4.44秒，累计时长=4.44秒
2025-08-07 17:41:38,531 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\3104.mp4 (确认存在: True)
2025-08-07 17:41:38,531 - INFO - 添加场景ID=3104，时长=2.08秒，累计时长=6.52秒
2025-08-07 17:41:38,531 - INFO - 准备合并 2 个场景文件，总时长约 6.52秒
2025-08-07 17:41:38,531 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/3101.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/3104.mp4'

2025-08-07 17:41:38,531 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpu5qy6od4\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpu5qy6od4\temp_combined.mp4
2025-08-07 17:41:38,641 - INFO - 合并后的视频时长: 6.57秒，目标音频时长: 5.15秒
2025-08-07 17:41:38,641 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpu5qy6od4\temp_combined.mp4 -ss 0 -to 5.153 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\60_3.mp4
2025-08-07 17:41:38,951 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:38,951 - INFO - 目标音频时长: 5.15秒
2025-08-07 17:41:38,951 - INFO - 实际视频时长: 5.18秒
2025-08-07 17:41:38,951 - INFO - 时长差异: 0.03秒 (0.58%)
2025-08-07 17:41:38,951 - INFO - ==========================================
2025-08-07 17:41:38,951 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:38,951 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\60_3.mp4
2025-08-07 17:41:38,952 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpu5qy6od4
2025-08-07 17:41:38,996 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:38,996 - INFO -   - 音频时长: 5.15秒
2025-08-07 17:41:38,996 - INFO -   - 视频时长: 5.18秒
2025-08-07 17:41:38,996 - INFO -   - 时长差异: 0.03秒 (0.58%)
2025-08-07 17:41:38,996 - INFO - 
字幕 #60 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:38,997 - INFO - 生成的视频文件:
2025-08-07 17:41:38,997 - INFO -   1. F:/github/aicut_auto/newcut_ai\60_1.mp4
2025-08-07 17:41:38,997 - INFO -   2. F:/github/aicut_auto/newcut_ai\60_2.mp4
2025-08-07 17:41:38,997 - INFO -   3. F:/github/aicut_auto/newcut_ai\60_3.mp4
2025-08-07 17:41:38,997 - INFO - ========== 字幕 #60 处理结束 ==========

