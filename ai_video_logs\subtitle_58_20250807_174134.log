2025-08-07 17:41:34,574 - INFO - ========== 字幕 #58 处理开始 ==========
2025-08-07 17:41:34,574 - INFO - 字幕内容: 男人再也无法容忍，他走上前，狠狠地给了二弟三个耳光，打他狂妄自大，丧尽天良。
2025-08-07 17:41:34,574 - INFO - 字幕序号: [2762, 2771]
2025-08-07 17:41:34,575 - INFO - 音频文件详情:
2025-08-07 17:41:34,575 - INFO -   - 路径: output\58.wav
2025-08-07 17:41:34,575 - INFO -   - 时长: 5.11秒
2025-08-07 17:41:34,575 - INFO -   - 验证音频时长: 5.11秒
2025-08-07 17:41:34,575 - INFO - 字幕时间戳信息:
2025-08-07 17:41:34,576 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:34,576 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:34,576 - INFO -   - 根据生成的音频时长(5.11秒)已调整字幕时间戳
2025-08-07 17:41:34,576 - INFO - ========== 新模式：为字幕 #58 生成4套场景方案 ==========
2025-08-07 17:41:34,576 - INFO - 字幕序号列表: [2762, 2771]
2025-08-07 17:41:34,576 - INFO - 
--- 生成方案 #1：基于字幕序号 #2762 ---
2025-08-07 17:41:34,576 - INFO - 开始为单个字幕序号 #2762 匹配场景，目标时长: 5.11秒
2025-08-07 17:41:34,576 - INFO - 开始查找字幕序号 [2762] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:34,576 - INFO - 找到related_overlap场景: scene_id=2986, 字幕#2762
2025-08-07 17:41:34,576 - INFO - 找到related_overlap场景: scene_id=2989, 字幕#2762
2025-08-07 17:41:34,577 - INFO - 字幕 #2762 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:34,577 - INFO - 字幕序号 #2762 找到 2 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:34,577 - INFO - 选择第一个overlap场景作为起点: scene_id=2986
2025-08-07 17:41:34,577 - INFO - 添加起点场景: scene_id=2986, 时长=2.44秒, 累计时长=2.44秒
2025-08-07 17:41:34,577 - INFO - 起点场景时长不足，需要延伸填充 2.67秒
2025-08-07 17:41:34,577 - INFO - 起点场景在原始列表中的索引: 2985
2025-08-07 17:41:34,577 - INFO - 延伸添加场景: scene_id=2987 (完整时长 0.76秒)
2025-08-07 17:41:34,577 - INFO - 累计时长: 3.20秒
2025-08-07 17:41:34,577 - INFO - 延伸添加场景: scene_id=2988 (完整时长 0.92秒)
2025-08-07 17:41:34,577 - INFO - 累计时长: 4.12秒
2025-08-07 17:41:34,578 - INFO - 延伸添加场景: scene_id=2989 (裁剪至 0.99秒)
2025-08-07 17:41:34,578 - INFO - 累计时长: 5.11秒
2025-08-07 17:41:34,578 - INFO - 字幕序号 #2762 场景匹配完成，共选择 4 个场景，总时长: 5.11秒
2025-08-07 17:41:34,578 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-08-07 17:41:34,578 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-08-07 17:41:34,578 - INFO - 
--- 生成方案 #2：基于字幕序号 #2771 ---
2025-08-07 17:41:34,578 - INFO - 开始为单个字幕序号 #2771 匹配场景，目标时长: 5.11秒
2025-08-07 17:41:34,578 - INFO - 开始查找字幕序号 [2771] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:34,578 - INFO - 找到related_overlap场景: scene_id=2995, 字幕#2771
2025-08-07 17:41:34,579 - INFO - 字幕 #2771 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:34,579 - INFO - 字幕序号 #2771 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:34,579 - INFO - 选择第一个overlap场景作为起点: scene_id=2995
2025-08-07 17:41:34,579 - INFO - 添加起点场景: scene_id=2995, 时长=1.56秒, 累计时长=1.56秒
2025-08-07 17:41:34,579 - INFO - 起点场景时长不足，需要延伸填充 3.55秒
2025-08-07 17:41:34,579 - INFO - 起点场景在原始列表中的索引: 2994
2025-08-07 17:41:34,579 - INFO - 延伸添加场景: scene_id=2996 (裁剪至 3.55秒)
2025-08-07 17:41:34,579 - INFO - 累计时长: 5.11秒
2025-08-07 17:41:34,579 - INFO - 字幕序号 #2771 场景匹配完成，共选择 2 个场景，总时长: 5.11秒
2025-08-07 17:41:34,579 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-08-07 17:41:34,579 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:34,579 - INFO - ========== 当前模式：为字幕 #58 生成 1 套场景方案 ==========
2025-08-07 17:41:34,579 - INFO - 开始查找字幕序号 [2762, 2771] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:34,580 - INFO - 找到related_overlap场景: scene_id=2986, 字幕#2762
2025-08-07 17:41:34,580 - INFO - 找到related_overlap场景: scene_id=2989, 字幕#2762
2025-08-07 17:41:34,580 - INFO - 找到related_overlap场景: scene_id=2995, 字幕#2771
2025-08-07 17:41:34,580 - INFO - 字幕 #2762 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:41:34,580 - INFO - 字幕 #2771 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:34,580 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-08-07 17:41:34,580 - INFO - 开始生成方案 #1
2025-08-07 17:41:34,580 - INFO - 方案 #1: 为字幕#2762选择初始化overlap场景id=2989
2025-08-07 17:41:34,580 - INFO - 方案 #1: 为字幕#2771选择初始化overlap场景id=2995
2025-08-07 17:41:34,580 - INFO - 方案 #1: 初始选择后，当前总时长=2.80秒
2025-08-07 17:41:34,580 - INFO - 方案 #1: 额外添加overlap场景id=2986, 当前总时长=5.24秒
2025-08-07 17:41:34,580 - INFO - 方案 #1: 额外between选择后，当前总时长=5.24秒
2025-08-07 17:41:34,580 - INFO - 方案 #1: 场景总时长(5.24秒)大于音频时长(5.11秒)，需要裁剪
2025-08-07 17:41:34,580 - INFO - 调整前总时长: 5.24秒, 目标时长: 5.11秒
2025-08-07 17:41:34,580 - INFO - 需要裁剪 0.13秒
2025-08-07 17:41:34,580 - INFO - 裁剪最长场景ID=2986：从2.44秒裁剪至2.31秒
2025-08-07 17:41:34,580 - INFO - 调整后总时长: 5.11秒，与目标时长差异: 0.00秒
2025-08-07 17:41:34,580 - INFO - 方案 #1 调整/填充后最终总时长: 5.11秒
2025-08-07 17:41:34,580 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:34,580 - INFO - ========== 当前模式：字幕 #58 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:34,580 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:34,580 - INFO - ========== 新模式：字幕 #58 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:34,580 - INFO - 
----- 处理字幕 #58 的方案 #1 -----
2025-08-07 17:41:34,580 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\58_1.mp4
2025-08-07 17:41:34,581 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpl8ns50yx
2025-08-07 17:41:34,581 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2986.mp4 (确认存在: True)
2025-08-07 17:41:34,581 - INFO - 添加场景ID=2986，时长=2.44秒，累计时长=2.44秒
2025-08-07 17:41:34,581 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2987.mp4 (确认存在: True)
2025-08-07 17:41:34,581 - INFO - 添加场景ID=2987，时长=0.76秒，累计时长=3.20秒
2025-08-07 17:41:34,582 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2988.mp4 (确认存在: True)
2025-08-07 17:41:34,582 - INFO - 添加场景ID=2988，时长=0.92秒，累计时长=4.12秒
2025-08-07 17:41:34,582 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2989.mp4 (确认存在: True)
2025-08-07 17:41:34,582 - INFO - 添加场景ID=2989，时长=1.24秒，累计时长=5.36秒
2025-08-07 17:41:34,582 - INFO - 准备合并 4 个场景文件，总时长约 5.36秒
2025-08-07 17:41:34,582 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2986.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2987.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2988.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2989.mp4'

2025-08-07 17:41:34,582 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpl8ns50yx\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpl8ns50yx\temp_combined.mp4
2025-08-07 17:41:34,750 - INFO - 合并后的视频时长: 5.45秒，目标音频时长: 5.11秒
2025-08-07 17:41:34,750 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpl8ns50yx\temp_combined.mp4 -ss 0 -to 5.112 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\58_1.mp4
2025-08-07 17:41:35,087 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:35,087 - INFO - 目标音频时长: 5.11秒
2025-08-07 17:41:35,087 - INFO - 实际视频时长: 5.14秒
2025-08-07 17:41:35,087 - INFO - 时长差异: 0.03秒 (0.61%)
2025-08-07 17:41:35,087 - INFO - ==========================================
2025-08-07 17:41:35,087 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:35,087 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\58_1.mp4
2025-08-07 17:41:35,088 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpl8ns50yx
2025-08-07 17:41:35,130 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:35,130 - INFO -   - 音频时长: 5.11秒
2025-08-07 17:41:35,130 - INFO -   - 视频时长: 5.14秒
2025-08-07 17:41:35,130 - INFO -   - 时长差异: 0.03秒 (0.61%)
2025-08-07 17:41:35,130 - INFO - 
----- 处理字幕 #58 的方案 #2 -----
2025-08-07 17:41:35,130 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\58_2.mp4
2025-08-07 17:41:35,131 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp81qcx71r
2025-08-07 17:41:35,131 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2995.mp4 (确认存在: True)
2025-08-07 17:41:35,131 - INFO - 添加场景ID=2995，时长=1.56秒，累计时长=1.56秒
2025-08-07 17:41:35,131 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2996.mp4 (确认存在: True)
2025-08-07 17:41:35,131 - INFO - 添加场景ID=2996，时长=6.56秒，累计时长=8.12秒
2025-08-07 17:41:35,131 - INFO - 场景总时长(8.12秒)已达到音频时长(5.11秒)的1.5倍，停止添加场景
2025-08-07 17:41:35,131 - INFO - 准备合并 2 个场景文件，总时长约 8.12秒
2025-08-07 17:41:35,131 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2995.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2996.mp4'

2025-08-07 17:41:35,132 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp81qcx71r\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp81qcx71r\temp_combined.mp4
2025-08-07 17:41:35,257 - INFO - 合并后的视频时长: 8.17秒，目标音频时长: 5.11秒
2025-08-07 17:41:35,257 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp81qcx71r\temp_combined.mp4 -ss 0 -to 5.112 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\58_2.mp4
2025-08-07 17:41:35,552 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:35,552 - INFO - 目标音频时长: 5.11秒
2025-08-07 17:41:35,552 - INFO - 实际视频时长: 5.14秒
2025-08-07 17:41:35,552 - INFO - 时长差异: 0.03秒 (0.61%)
2025-08-07 17:41:35,553 - INFO - ==========================================
2025-08-07 17:41:35,553 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:35,553 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\58_2.mp4
2025-08-07 17:41:35,553 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp81qcx71r
2025-08-07 17:41:35,597 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:35,597 - INFO -   - 音频时长: 5.11秒
2025-08-07 17:41:35,597 - INFO -   - 视频时长: 5.14秒
2025-08-07 17:41:35,597 - INFO -   - 时长差异: 0.03秒 (0.61%)
2025-08-07 17:41:35,598 - INFO - 
----- 处理字幕 #58 的方案 #3 -----
2025-08-07 17:41:35,598 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\58_3.mp4
2025-08-07 17:41:35,598 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpr4byf_x5
2025-08-07 17:41:35,598 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2989.mp4 (确认存在: True)
2025-08-07 17:41:35,598 - INFO - 添加场景ID=2989，时长=1.24秒，累计时长=1.24秒
2025-08-07 17:41:35,598 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2995.mp4 (确认存在: True)
2025-08-07 17:41:35,598 - INFO - 添加场景ID=2995，时长=1.56秒，累计时长=2.80秒
2025-08-07 17:41:35,599 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2986.mp4 (确认存在: True)
2025-08-07 17:41:35,599 - INFO - 添加场景ID=2986，时长=2.44秒，累计时长=5.24秒
2025-08-07 17:41:35,599 - INFO - 准备合并 3 个场景文件，总时长约 5.24秒
2025-08-07 17:41:35,599 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2989.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2995.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2986.mp4'

2025-08-07 17:41:35,599 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpr4byf_x5\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpr4byf_x5\temp_combined.mp4
2025-08-07 17:41:35,751 - INFO - 合并后的视频时长: 5.31秒，目标音频时长: 5.11秒
2025-08-07 17:41:35,751 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpr4byf_x5\temp_combined.mp4 -ss 0 -to 5.112 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\58_3.mp4
2025-08-07 17:41:36,071 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:36,071 - INFO - 目标音频时长: 5.11秒
2025-08-07 17:41:36,071 - INFO - 实际视频时长: 5.14秒
2025-08-07 17:41:36,071 - INFO - 时长差异: 0.03秒 (0.61%)
2025-08-07 17:41:36,071 - INFO - ==========================================
2025-08-07 17:41:36,071 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:36,071 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\58_3.mp4
2025-08-07 17:41:36,072 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpr4byf_x5
2025-08-07 17:41:36,113 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:36,113 - INFO -   - 音频时长: 5.11秒
2025-08-07 17:41:36,113 - INFO -   - 视频时长: 5.14秒
2025-08-07 17:41:36,113 - INFO -   - 时长差异: 0.03秒 (0.61%)
2025-08-07 17:41:36,115 - INFO - 
字幕 #58 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:36,115 - INFO - 生成的视频文件:
2025-08-07 17:41:36,115 - INFO -   1. F:/github/aicut_auto/newcut_ai\58_1.mp4
2025-08-07 17:41:36,115 - INFO -   2. F:/github/aicut_auto/newcut_ai\58_2.mp4
2025-08-07 17:41:36,115 - INFO -   3. F:/github/aicut_auto/newcut_ai\58_3.mp4
2025-08-07 17:41:36,115 - INFO - ========== 字幕 #58 处理结束 ==========

