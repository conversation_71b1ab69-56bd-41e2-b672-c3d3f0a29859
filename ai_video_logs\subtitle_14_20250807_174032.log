2025-08-07 17:40:32,531 - INFO - ========== 字幕 #14 处理开始 ==========
2025-08-07 17:40:32,531 - INFO - 字幕内容: 被小瞧的女孩当即施展法力，指尖燃起火焰，瞬间让男人的双腿恢复了知觉，让他震惊不已。
2025-08-07 17:40:32,531 - INFO - 字幕序号: [290, 293]
2025-08-07 17:40:32,531 - INFO - 音频文件详情:
2025-08-07 17:40:32,531 - INFO -   - 路径: output\14.wav
2025-08-07 17:40:32,531 - INFO -   - 时长: 6.02秒
2025-08-07 17:40:32,532 - INFO -   - 验证音频时长: 6.02秒
2025-08-07 17:40:32,532 - INFO - 字幕时间戳信息:
2025-08-07 17:40:32,532 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:32,532 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:32,532 - INFO -   - 根据生成的音频时长(6.02秒)已调整字幕时间戳
2025-08-07 17:40:32,532 - INFO - ========== 新模式：为字幕 #14 生成4套场景方案 ==========
2025-08-07 17:40:32,532 - INFO - 字幕序号列表: [290, 293]
2025-08-07 17:40:32,532 - INFO - 
--- 生成方案 #1：基于字幕序号 #290 ---
2025-08-07 17:40:32,532 - INFO - 开始为单个字幕序号 #290 匹配场景，目标时长: 6.02秒
2025-08-07 17:40:32,532 - INFO - 开始查找字幕序号 [290] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:32,532 - INFO - 找到related_overlap场景: scene_id=423, 字幕#290
2025-08-07 17:40:32,532 - INFO - 找到related_overlap场景: scene_id=424, 字幕#290
2025-08-07 17:40:32,533 - INFO - 找到related_between场景: scene_id=425, 字幕#290
2025-08-07 17:40:32,533 - INFO - 找到related_between场景: scene_id=426, 字幕#290
2025-08-07 17:40:32,534 - INFO - 字幕 #290 找到 2 个overlap场景, 2 个between场景
2025-08-07 17:40:32,534 - INFO - 字幕序号 #290 找到 2 个可用overlap场景, 2 个可用between场景
2025-08-07 17:40:32,534 - INFO - 选择第一个overlap场景作为起点: scene_id=423
2025-08-07 17:40:32,534 - INFO - 添加起点场景: scene_id=423, 时长=1.72秒, 累计时长=1.72秒
2025-08-07 17:40:32,534 - INFO - 起点场景时长不足，需要延伸填充 4.30秒
2025-08-07 17:40:32,534 - INFO - 起点场景在原始列表中的索引: 422
2025-08-07 17:40:32,534 - INFO - 延伸添加场景: scene_id=424 (完整时长 1.24秒)
2025-08-07 17:40:32,534 - INFO - 累计时长: 2.96秒
2025-08-07 17:40:32,534 - INFO - 延伸添加场景: scene_id=425 (完整时长 1.56秒)
2025-08-07 17:40:32,534 - INFO - 累计时长: 4.52秒
2025-08-07 17:40:32,534 - INFO - 延伸添加场景: scene_id=426 (完整时长 1.24秒)
2025-08-07 17:40:32,534 - INFO - 累计时长: 5.76秒
2025-08-07 17:40:32,534 - INFO - 延伸添加场景: scene_id=427 (裁剪至 0.26秒)
2025-08-07 17:40:32,534 - INFO - 累计时长: 6.02秒
2025-08-07 17:40:32,534 - INFO - 字幕序号 #290 场景匹配完成，共选择 5 个场景，总时长: 6.02秒
2025-08-07 17:40:32,534 - INFO - 方案 #1 生成成功，包含 5 个场景
2025-08-07 17:40:32,534 - INFO - 新模式：第1套方案的 5 个场景已加入全局已使用集合
2025-08-07 17:40:32,534 - INFO - 
--- 生成方案 #2：基于字幕序号 #293 ---
2025-08-07 17:40:32,534 - INFO - 开始为单个字幕序号 #293 匹配场景，目标时长: 6.02秒
2025-08-07 17:40:32,534 - INFO - 开始查找字幕序号 [293] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:32,534 - INFO - 找到related_overlap场景: scene_id=432, 字幕#293
2025-08-07 17:40:32,535 - INFO - 找到related_between场景: scene_id=431, 字幕#293
2025-08-07 17:40:32,535 - INFO - 找到related_between场景: scene_id=433, 字幕#293
2025-08-07 17:40:32,535 - INFO - 字幕 #293 找到 1 个overlap场景, 2 个between场景
2025-08-07 17:40:32,535 - INFO - 字幕序号 #293 找到 1 个可用overlap场景, 2 个可用between场景
2025-08-07 17:40:32,535 - INFO - 选择第一个overlap场景作为起点: scene_id=432
2025-08-07 17:40:32,535 - INFO - 添加起点场景: scene_id=432, 时长=1.24秒, 累计时长=1.24秒
2025-08-07 17:40:32,535 - INFO - 起点场景时长不足，需要延伸填充 4.78秒
2025-08-07 17:40:32,535 - INFO - 起点场景在原始列表中的索引: 431
2025-08-07 17:40:32,535 - INFO - 延伸添加场景: scene_id=433 (完整时长 1.44秒)
2025-08-07 17:40:32,535 - INFO - 累计时长: 2.68秒
2025-08-07 17:40:32,535 - INFO - 延伸添加场景: scene_id=434 (完整时长 1.68秒)
2025-08-07 17:40:32,535 - INFO - 累计时长: 4.36秒
2025-08-07 17:40:32,535 - INFO - 延伸添加场景: scene_id=435 (完整时长 1.20秒)
2025-08-07 17:40:32,535 - INFO - 累计时长: 5.56秒
2025-08-07 17:40:32,535 - INFO - 延伸添加场景: scene_id=436 (裁剪至 0.47秒)
2025-08-07 17:40:32,535 - INFO - 累计时长: 6.02秒
2025-08-07 17:40:32,535 - INFO - 字幕序号 #293 场景匹配完成，共选择 5 个场景，总时长: 6.02秒
2025-08-07 17:40:32,535 - INFO - 方案 #2 生成成功，包含 5 个场景
2025-08-07 17:40:32,535 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:32,535 - INFO - ========== 当前模式：为字幕 #14 生成 1 套场景方案 ==========
2025-08-07 17:40:32,535 - INFO - 开始查找字幕序号 [290, 293] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:32,535 - INFO - 找到related_overlap场景: scene_id=423, 字幕#290
2025-08-07 17:40:32,535 - INFO - 找到related_overlap场景: scene_id=424, 字幕#290
2025-08-07 17:40:32,535 - INFO - 找到related_overlap场景: scene_id=432, 字幕#293
2025-08-07 17:40:32,536 - INFO - 找到related_between场景: scene_id=425, 字幕#290
2025-08-07 17:40:32,536 - INFO - 找到related_between场景: scene_id=426, 字幕#290
2025-08-07 17:40:32,536 - INFO - 找到related_between场景: scene_id=431, 字幕#293
2025-08-07 17:40:32,536 - INFO - 找到related_between场景: scene_id=433, 字幕#293
2025-08-07 17:40:32,536 - INFO - 字幕 #290 找到 2 个overlap场景, 2 个between场景
2025-08-07 17:40:32,537 - INFO - 字幕 #293 找到 1 个overlap场景, 2 个between场景
2025-08-07 17:40:32,537 - INFO - 共收集 3 个未使用的overlap场景和 4 个未使用的between场景
2025-08-07 17:40:32,537 - INFO - 开始生成方案 #1
2025-08-07 17:40:32,537 - INFO - 方案 #1: 为字幕#290选择初始化overlap场景id=424
2025-08-07 17:40:32,537 - INFO - 方案 #1: 为字幕#293选择初始化overlap场景id=432
2025-08-07 17:40:32,537 - INFO - 方案 #1: 初始选择后，当前总时长=2.48秒
2025-08-07 17:40:32,537 - INFO - 方案 #1: 额外添加overlap场景id=423, 当前总时长=4.20秒
2025-08-07 17:40:32,537 - INFO - 方案 #1: 额外between选择后，当前总时长=4.20秒
2025-08-07 17:40:32,537 - INFO - 方案 #1: 额外添加between场景id=431, 当前总时长=5.44秒
2025-08-07 17:40:32,537 - INFO - 方案 #1: 额外添加between场景id=425, 当前总时长=7.00秒
2025-08-07 17:40:32,537 - INFO - 方案 #1: 场景总时长(7.00秒)大于音频时长(6.02秒)，需要裁剪
2025-08-07 17:40:32,537 - INFO - 调整前总时长: 7.00秒, 目标时长: 6.02秒
2025-08-07 17:40:32,537 - INFO - 需要裁剪 0.98秒
2025-08-07 17:40:32,537 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-08-07 17:40:32,537 - INFO - 裁剪场景ID=423：从1.72秒裁剪至1.00秒
2025-08-07 17:40:32,537 - INFO - 裁剪场景ID=425：从1.56秒裁剪至1.30秒
2025-08-07 17:40:32,537 - INFO - 调整后总时长: 6.02秒，与目标时长差异: 0.00秒
2025-08-07 17:40:32,537 - INFO - 方案 #1 调整/填充后最终总时长: 6.02秒
2025-08-07 17:40:32,537 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:32,537 - INFO - ========== 当前模式：字幕 #14 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:32,537 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:32,537 - INFO - ========== 新模式：字幕 #14 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:32,537 - INFO - 
----- 处理字幕 #14 的方案 #1 -----
2025-08-07 17:40:32,537 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\14_1.mp4
2025-08-07 17:40:32,537 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmphduie4gt
2025-08-07 17:40:32,537 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\423.mp4 (确认存在: True)
2025-08-07 17:40:32,537 - INFO - 添加场景ID=423，时长=1.72秒，累计时长=1.72秒
2025-08-07 17:40:32,537 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\424.mp4 (确认存在: True)
2025-08-07 17:40:32,537 - INFO - 添加场景ID=424，时长=1.24秒，累计时长=2.96秒
2025-08-07 17:40:32,537 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\425.mp4 (确认存在: True)
2025-08-07 17:40:32,537 - INFO - 添加场景ID=425，时长=1.56秒，累计时长=4.52秒
2025-08-07 17:40:32,538 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\426.mp4 (确认存在: True)
2025-08-07 17:40:32,538 - INFO - 添加场景ID=426，时长=1.24秒，累计时长=5.76秒
2025-08-07 17:40:32,538 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\427.mp4 (确认存在: True)
2025-08-07 17:40:32,538 - INFO - 添加场景ID=427，时长=1.56秒，累计时长=7.32秒
2025-08-07 17:40:32,538 - INFO - 准备合并 5 个场景文件，总时长约 7.32秒
2025-08-07 17:40:32,538 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/423.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/424.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/425.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/426.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/427.mp4'

2025-08-07 17:40:32,538 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmphduie4gt\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmphduie4gt\temp_combined.mp4
2025-08-07 17:40:32,718 - INFO - 合并后的视频时长: 7.44秒，目标音频时长: 6.02秒
2025-08-07 17:40:32,718 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmphduie4gt\temp_combined.mp4 -ss 0 -to 6.024 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\14_1.mp4
2025-08-07 17:40:33,046 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:33,046 - INFO - 目标音频时长: 6.02秒
2025-08-07 17:40:33,046 - INFO - 实际视频时长: 6.06秒
2025-08-07 17:40:33,047 - INFO - 时长差异: 0.04秒 (0.65%)
2025-08-07 17:40:33,047 - INFO - ==========================================
2025-08-07 17:40:33,047 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:33,047 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\14_1.mp4
2025-08-07 17:40:33,047 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmphduie4gt
2025-08-07 17:40:33,091 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:33,091 - INFO -   - 音频时长: 6.02秒
2025-08-07 17:40:33,091 - INFO -   - 视频时长: 6.06秒
2025-08-07 17:40:33,091 - INFO -   - 时长差异: 0.04秒 (0.65%)
2025-08-07 17:40:33,091 - INFO - 
----- 处理字幕 #14 的方案 #2 -----
2025-08-07 17:40:33,091 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\14_2.mp4
2025-08-07 17:40:33,096 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpe5fen46v
2025-08-07 17:40:33,096 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\432.mp4 (确认存在: True)
2025-08-07 17:40:33,096 - INFO - 添加场景ID=432，时长=1.24秒，累计时长=1.24秒
2025-08-07 17:40:33,096 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\433.mp4 (确认存在: True)
2025-08-07 17:40:33,096 - INFO - 添加场景ID=433，时长=1.44秒，累计时长=2.68秒
2025-08-07 17:40:33,096 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\434.mp4 (确认存在: True)
2025-08-07 17:40:33,097 - INFO - 添加场景ID=434，时长=1.68秒，累计时长=4.36秒
2025-08-07 17:40:33,097 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\435.mp4 (确认存在: True)
2025-08-07 17:40:33,097 - INFO - 添加场景ID=435，时长=1.20秒，累计时长=5.56秒
2025-08-07 17:40:33,097 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\436.mp4 (确认存在: True)
2025-08-07 17:40:33,097 - INFO - 添加场景ID=436，时长=1.28秒，累计时长=6.84秒
2025-08-07 17:40:33,097 - INFO - 准备合并 5 个场景文件，总时长约 6.84秒
2025-08-07 17:40:33,097 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/432.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/433.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/434.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/435.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/436.mp4'

2025-08-07 17:40:33,097 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpe5fen46v\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpe5fen46v\temp_combined.mp4
2025-08-07 17:40:33,277 - INFO - 合并后的视频时长: 6.96秒，目标音频时长: 6.02秒
2025-08-07 17:40:33,277 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpe5fen46v\temp_combined.mp4 -ss 0 -to 6.024 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\14_2.mp4
2025-08-07 17:40:33,628 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:33,628 - INFO - 目标音频时长: 6.02秒
2025-08-07 17:40:33,628 - INFO - 实际视频时长: 6.06秒
2025-08-07 17:40:33,628 - INFO - 时长差异: 0.04秒 (0.65%)
2025-08-07 17:40:33,628 - INFO - ==========================================
2025-08-07 17:40:33,628 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:33,628 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\14_2.mp4
2025-08-07 17:40:33,629 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpe5fen46v
2025-08-07 17:40:33,673 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:33,673 - INFO -   - 音频时长: 6.02秒
2025-08-07 17:40:33,673 - INFO -   - 视频时长: 6.06秒
2025-08-07 17:40:33,673 - INFO -   - 时长差异: 0.04秒 (0.65%)
2025-08-07 17:40:33,673 - INFO - 
----- 处理字幕 #14 的方案 #3 -----
2025-08-07 17:40:33,673 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\14_3.mp4
2025-08-07 17:40:33,674 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqozh6wm3
2025-08-07 17:40:33,674 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\424.mp4 (确认存在: True)
2025-08-07 17:40:33,674 - INFO - 添加场景ID=424，时长=1.24秒，累计时长=1.24秒
2025-08-07 17:40:33,674 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\432.mp4 (确认存在: True)
2025-08-07 17:40:33,674 - INFO - 添加场景ID=432，时长=1.24秒，累计时长=2.48秒
2025-08-07 17:40:33,674 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\423.mp4 (确认存在: True)
2025-08-07 17:40:33,674 - INFO - 添加场景ID=423，时长=1.72秒，累计时长=4.20秒
2025-08-07 17:40:33,674 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\431.mp4 (确认存在: True)
2025-08-07 17:40:33,674 - INFO - 添加场景ID=431，时长=1.24秒，累计时长=5.44秒
2025-08-07 17:40:33,674 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\425.mp4 (确认存在: True)
2025-08-07 17:40:33,674 - INFO - 添加场景ID=425，时长=1.56秒，累计时长=7.00秒
2025-08-07 17:40:33,674 - INFO - 准备合并 5 个场景文件，总时长约 7.00秒
2025-08-07 17:40:33,675 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/424.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/432.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/423.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/431.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/425.mp4'

2025-08-07 17:40:33,675 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpqozh6wm3\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpqozh6wm3\temp_combined.mp4
2025-08-07 17:40:33,851 - INFO - 合并后的视频时长: 7.12秒，目标音频时长: 6.02秒
2025-08-07 17:40:33,851 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpqozh6wm3\temp_combined.mp4 -ss 0 -to 6.024 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\14_3.mp4
2025-08-07 17:40:34,199 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:34,199 - INFO - 目标音频时长: 6.02秒
2025-08-07 17:40:34,199 - INFO - 实际视频时长: 6.06秒
2025-08-07 17:40:34,199 - INFO - 时长差异: 0.04秒 (0.65%)
2025-08-07 17:40:34,199 - INFO - ==========================================
2025-08-07 17:40:34,199 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:34,199 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\14_3.mp4
2025-08-07 17:40:34,200 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpqozh6wm3
2025-08-07 17:40:34,242 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:34,242 - INFO -   - 音频时长: 6.02秒
2025-08-07 17:40:34,242 - INFO -   - 视频时长: 6.06秒
2025-08-07 17:40:34,242 - INFO -   - 时长差异: 0.04秒 (0.65%)
2025-08-07 17:40:34,242 - INFO - 
字幕 #14 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:34,242 - INFO - 生成的视频文件:
2025-08-07 17:40:34,242 - INFO -   1. F:/github/aicut_auto/newcut_ai\14_1.mp4
2025-08-07 17:40:34,242 - INFO -   2. F:/github/aicut_auto/newcut_ai\14_2.mp4
2025-08-07 17:40:34,242 - INFO -   3. F:/github/aicut_auto/newcut_ai\14_3.mp4
2025-08-07 17:40:34,242 - INFO - ========== 字幕 #14 处理结束 ==========

