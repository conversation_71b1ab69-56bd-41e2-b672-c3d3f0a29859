2025-08-07 17:41:30,747 - INFO - ========== 字幕 #55 处理开始 ==========
2025-08-07 17:41:30,747 - INFO - 字幕内容: 会长服下丹药后，立刻口吐白沫，浑身抽搐，医生诊断竟是中了老鼠药。
2025-08-07 17:41:30,747 - INFO - 字幕序号: [2513, 2520]
2025-08-07 17:41:30,748 - INFO - 音频文件详情:
2025-08-07 17:41:30,748 - INFO -   - 路径: output\55.wav
2025-08-07 17:41:30,748 - INFO -   - 时长: 3.92秒
2025-08-07 17:41:30,748 - INFO -   - 验证音频时长: 3.92秒
2025-08-07 17:41:30,748 - INFO - 字幕时间戳信息:
2025-08-07 17:41:30,748 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:30,748 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:30,748 - INFO -   - 根据生成的音频时长(3.92秒)已调整字幕时间戳
2025-08-07 17:41:30,748 - INFO - ========== 新模式：为字幕 #55 生成4套场景方案 ==========
2025-08-07 17:41:30,748 - INFO - 字幕序号列表: [2513, 2520]
2025-08-07 17:41:30,748 - INFO - 
--- 生成方案 #1：基于字幕序号 #2513 ---
2025-08-07 17:41:30,748 - INFO - 开始为单个字幕序号 #2513 匹配场景，目标时长: 3.92秒
2025-08-07 17:41:30,748 - INFO - 开始查找字幕序号 [2513] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:30,749 - INFO - 找到related_overlap场景: scene_id=2760, 字幕#2513
2025-08-07 17:41:30,749 - INFO - 字幕 #2513 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:30,750 - INFO - 字幕序号 #2513 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:30,750 - INFO - 选择第一个overlap场景作为起点: scene_id=2760
2025-08-07 17:41:30,750 - INFO - 添加起点场景: scene_id=2760, 时长=0.96秒, 累计时长=0.96秒
2025-08-07 17:41:30,750 - INFO - 起点场景时长不足，需要延伸填充 2.96秒
2025-08-07 17:41:30,750 - INFO - 起点场景在原始列表中的索引: 2759
2025-08-07 17:41:30,750 - INFO - 延伸添加场景: scene_id=2761 (完整时长 1.56秒)
2025-08-07 17:41:30,750 - INFO - 累计时长: 2.52秒
2025-08-07 17:41:30,750 - INFO - 延伸添加场景: scene_id=2762 (完整时长 0.96秒)
2025-08-07 17:41:30,750 - INFO - 累计时长: 3.48秒
2025-08-07 17:41:30,750 - INFO - 延伸添加场景: scene_id=2763 (裁剪至 0.44秒)
2025-08-07 17:41:30,750 - INFO - 累计时长: 3.92秒
2025-08-07 17:41:30,750 - INFO - 字幕序号 #2513 场景匹配完成，共选择 4 个场景，总时长: 3.92秒
2025-08-07 17:41:30,750 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-08-07 17:41:30,750 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-08-07 17:41:30,750 - INFO - 
--- 生成方案 #2：基于字幕序号 #2520 ---
2025-08-07 17:41:30,750 - INFO - 开始为单个字幕序号 #2520 匹配场景，目标时长: 3.92秒
2025-08-07 17:41:30,750 - INFO - 开始查找字幕序号 [2520] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:30,750 - INFO - 找到related_overlap场景: scene_id=2765, 字幕#2520
2025-08-07 17:41:30,750 - INFO - 找到related_overlap场景: scene_id=2766, 字幕#2520
2025-08-07 17:41:30,751 - INFO - 找到related_between场景: scene_id=2767, 字幕#2520
2025-08-07 17:41:30,751 - INFO - 找到related_between场景: scene_id=2768, 字幕#2520
2025-08-07 17:41:30,751 - INFO - 找到related_between场景: scene_id=2769, 字幕#2520
2025-08-07 17:41:30,751 - INFO - 字幕 #2520 找到 2 个overlap场景, 3 个between场景
2025-08-07 17:41:30,751 - INFO - 字幕序号 #2520 找到 2 个可用overlap场景, 3 个可用between场景
2025-08-07 17:41:30,752 - INFO - 选择第一个overlap场景作为起点: scene_id=2765
2025-08-07 17:41:30,752 - INFO - 添加起点场景: scene_id=2765, 时长=1.64秒, 累计时长=1.64秒
2025-08-07 17:41:30,752 - INFO - 起点场景时长不足，需要延伸填充 2.28秒
2025-08-07 17:41:30,752 - INFO - 起点场景在原始列表中的索引: 2764
2025-08-07 17:41:30,752 - INFO - 延伸添加场景: scene_id=2766 (完整时长 1.16秒)
2025-08-07 17:41:30,752 - INFO - 累计时长: 2.80秒
2025-08-07 17:41:30,752 - INFO - 延伸添加场景: scene_id=2767 (完整时长 1.12秒)
2025-08-07 17:41:30,752 - INFO - 累计时长: 3.92秒
2025-08-07 17:41:30,752 - INFO - 延伸添加场景: scene_id=2768 (裁剪至 0.00秒)
2025-08-07 17:41:30,752 - INFO - 累计时长: 3.92秒
2025-08-07 17:41:30,752 - INFO - 字幕序号 #2520 场景匹配完成，共选择 4 个场景，总时长: 3.92秒
2025-08-07 17:41:30,752 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-08-07 17:41:30,752 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:30,752 - INFO - ========== 当前模式：为字幕 #55 生成 1 套场景方案 ==========
2025-08-07 17:41:30,752 - INFO - 开始查找字幕序号 [2513, 2520] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:30,752 - INFO - 找到related_overlap场景: scene_id=2760, 字幕#2513
2025-08-07 17:41:30,752 - INFO - 找到related_overlap场景: scene_id=2765, 字幕#2520
2025-08-07 17:41:30,752 - INFO - 找到related_overlap场景: scene_id=2766, 字幕#2520
2025-08-07 17:41:30,753 - INFO - 找到related_between场景: scene_id=2767, 字幕#2520
2025-08-07 17:41:30,753 - INFO - 找到related_between场景: scene_id=2768, 字幕#2520
2025-08-07 17:41:30,753 - INFO - 找到related_between场景: scene_id=2769, 字幕#2520
2025-08-07 17:41:30,753 - INFO - 字幕 #2513 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:30,753 - INFO - 字幕 #2520 找到 2 个overlap场景, 3 个between场景
2025-08-07 17:41:30,753 - INFO - 共收集 3 个未使用的overlap场景和 3 个未使用的between场景
2025-08-07 17:41:30,753 - INFO - 开始生成方案 #1
2025-08-07 17:41:30,753 - INFO - 方案 #1: 为字幕#2513选择初始化overlap场景id=2760
2025-08-07 17:41:30,753 - INFO - 方案 #1: 为字幕#2520选择初始化overlap场景id=2765
2025-08-07 17:41:30,753 - INFO - 方案 #1: 初始选择后，当前总时长=2.60秒
2025-08-07 17:41:30,753 - INFO - 方案 #1: 额外添加overlap场景id=2766, 当前总时长=3.76秒
2025-08-07 17:41:30,753 - INFO - 方案 #1: 额外between选择后，当前总时长=3.76秒
2025-08-07 17:41:30,753 - INFO - 方案 #1: 额外添加between场景id=2767, 当前总时长=4.88秒
2025-08-07 17:41:30,753 - INFO - 方案 #1: 场景总时长(4.88秒)大于音频时长(3.92秒)，需要裁剪
2025-08-07 17:41:30,753 - INFO - 调整前总时长: 4.88秒, 目标时长: 3.92秒
2025-08-07 17:41:30,753 - INFO - 需要裁剪 0.96秒
2025-08-07 17:41:30,753 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-08-07 17:41:30,753 - INFO - 裁剪场景ID=2765：从1.64秒裁剪至1.00秒
2025-08-07 17:41:30,753 - INFO - 裁剪场景ID=2766：从1.16秒裁剪至1.00秒
2025-08-07 17:41:30,753 - INFO - 裁剪场景ID=2767：从1.12秒裁剪至1.00秒
2025-08-07 17:41:30,753 - WARNING - 通过裁剪无法达到目标时长，需要移除场景，剩余需裁剪时长: 0.04秒
2025-08-07 17:41:30,753 - INFO - 移除场景ID=2760，时长=0.96秒
2025-08-07 17:41:30,753 - INFO - 调整后总时长: 3.00秒，与目标时长差异: 0.92秒
2025-08-07 17:41:30,753 - INFO - 方案 #1 调整/填充后最终总时长: 3.00秒
2025-08-07 17:41:30,753 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:30,753 - INFO - ========== 当前模式：字幕 #55 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:30,753 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:30,753 - INFO - ========== 新模式：字幕 #55 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:30,753 - INFO - 
----- 处理字幕 #55 的方案 #1 -----
2025-08-07 17:41:30,753 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\55_1.mp4
2025-08-07 17:41:30,754 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8q12uz3x
2025-08-07 17:41:30,754 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2760.mp4 (确认存在: True)
2025-08-07 17:41:30,754 - INFO - 添加场景ID=2760，时长=0.96秒，累计时长=0.96秒
2025-08-07 17:41:30,754 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2761.mp4 (确认存在: True)
2025-08-07 17:41:30,754 - INFO - 添加场景ID=2761，时长=1.56秒，累计时长=2.52秒
2025-08-07 17:41:30,754 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2762.mp4 (确认存在: True)
2025-08-07 17:41:30,754 - INFO - 添加场景ID=2762，时长=0.96秒，累计时长=3.48秒
2025-08-07 17:41:30,754 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2763.mp4 (确认存在: True)
2025-08-07 17:41:30,754 - INFO - 添加场景ID=2763，时长=1.40秒，累计时长=4.88秒
2025-08-07 17:41:30,754 - INFO - 准备合并 4 个场景文件，总时长约 4.88秒
2025-08-07 17:41:30,754 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2760.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2761.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2762.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2763.mp4'

2025-08-07 17:41:30,754 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp8q12uz3x\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp8q12uz3x\temp_combined.mp4
2025-08-07 17:41:30,913 - INFO - 合并后的视频时长: 4.97秒，目标音频时长: 3.92秒
2025-08-07 17:41:30,913 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp8q12uz3x\temp_combined.mp4 -ss 0 -to 3.921 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\55_1.mp4
2025-08-07 17:41:31,187 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:31,187 - INFO - 目标音频时长: 3.92秒
2025-08-07 17:41:31,187 - INFO - 实际视频时长: 3.98秒
2025-08-07 17:41:31,187 - INFO - 时长差异: 0.06秒 (1.58%)
2025-08-07 17:41:31,187 - INFO - ==========================================
2025-08-07 17:41:31,187 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:31,187 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\55_1.mp4
2025-08-07 17:41:31,187 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8q12uz3x
2025-08-07 17:41:31,229 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:31,229 - INFO -   - 音频时长: 3.92秒
2025-08-07 17:41:31,229 - INFO -   - 视频时长: 3.98秒
2025-08-07 17:41:31,229 - INFO -   - 时长差异: 0.06秒 (1.58%)
2025-08-07 17:41:31,229 - INFO - 
----- 处理字幕 #55 的方案 #2 -----
2025-08-07 17:41:31,229 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\55_2.mp4
2025-08-07 17:41:31,230 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpaqh0ejcp
2025-08-07 17:41:31,230 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2765.mp4 (确认存在: True)
2025-08-07 17:41:31,230 - INFO - 添加场景ID=2765，时长=1.64秒，累计时长=1.64秒
2025-08-07 17:41:31,230 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2766.mp4 (确认存在: True)
2025-08-07 17:41:31,230 - INFO - 添加场景ID=2766，时长=1.16秒，累计时长=2.80秒
2025-08-07 17:41:31,230 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2767.mp4 (确认存在: True)
2025-08-07 17:41:31,230 - INFO - 添加场景ID=2767，时长=1.12秒，累计时长=3.92秒
2025-08-07 17:41:31,231 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2768.mp4 (确认存在: True)
2025-08-07 17:41:31,231 - INFO - 添加场景ID=2768，时长=1.44秒，累计时长=5.36秒
2025-08-07 17:41:31,231 - INFO - 准备合并 4 个场景文件，总时长约 5.36秒
2025-08-07 17:41:31,231 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2765.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2766.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2767.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2768.mp4'

2025-08-07 17:41:31,231 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpaqh0ejcp\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpaqh0ejcp\temp_combined.mp4
2025-08-07 17:41:31,377 - INFO - 合并后的视频时长: 5.45秒，目标音频时长: 3.92秒
2025-08-07 17:41:31,378 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpaqh0ejcp\temp_combined.mp4 -ss 0 -to 3.921 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\55_2.mp4
2025-08-07 17:41:31,647 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:31,647 - INFO - 目标音频时长: 3.92秒
2025-08-07 17:41:31,647 - INFO - 实际视频时长: 3.98秒
2025-08-07 17:41:31,647 - INFO - 时长差异: 0.06秒 (1.58%)
2025-08-07 17:41:31,647 - INFO - ==========================================
2025-08-07 17:41:31,647 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:31,647 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\55_2.mp4
2025-08-07 17:41:31,648 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpaqh0ejcp
2025-08-07 17:41:31,691 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:31,691 - INFO -   - 音频时长: 3.92秒
2025-08-07 17:41:31,691 - INFO -   - 视频时长: 3.98秒
2025-08-07 17:41:31,691 - INFO -   - 时长差异: 0.06秒 (1.58%)
2025-08-07 17:41:31,691 - INFO - 
----- 处理字幕 #55 的方案 #3 -----
2025-08-07 17:41:31,692 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\55_3.mp4
2025-08-07 17:41:31,692 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpx45dqfdk
2025-08-07 17:41:31,692 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2765.mp4 (确认存在: True)
2025-08-07 17:41:31,692 - INFO - 添加场景ID=2765，时长=1.64秒，累计时长=1.64秒
2025-08-07 17:41:31,692 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2766.mp4 (确认存在: True)
2025-08-07 17:41:31,692 - INFO - 添加场景ID=2766，时长=1.16秒，累计时长=2.80秒
2025-08-07 17:41:31,693 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2767.mp4 (确认存在: True)
2025-08-07 17:41:31,693 - INFO - 添加场景ID=2767，时长=1.12秒，累计时长=3.92秒
2025-08-07 17:41:31,693 - INFO - 准备合并 3 个场景文件，总时长约 3.92秒
2025-08-07 17:41:31,693 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2765.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2766.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2767.mp4'

2025-08-07 17:41:31,693 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpx45dqfdk\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpx45dqfdk\temp_combined.mp4
2025-08-07 17:41:31,828 - INFO - 合并后的视频时长: 3.99秒，目标音频时长: 3.92秒
2025-08-07 17:41:31,828 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpx45dqfdk\temp_combined.mp4 -ss 0 -to 3.921 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\55_3.mp4
2025-08-07 17:41:32,094 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:32,094 - INFO - 目标音频时长: 3.92秒
2025-08-07 17:41:32,094 - INFO - 实际视频时长: 3.98秒
2025-08-07 17:41:32,094 - INFO - 时长差异: 0.06秒 (1.58%)
2025-08-07 17:41:32,094 - INFO - ==========================================
2025-08-07 17:41:32,095 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:32,095 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\55_3.mp4
2025-08-07 17:41:32,095 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpx45dqfdk
2025-08-07 17:41:32,140 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:32,140 - INFO -   - 音频时长: 3.92秒
2025-08-07 17:41:32,140 - INFO -   - 视频时长: 3.98秒
2025-08-07 17:41:32,140 - INFO -   - 时长差异: 0.06秒 (1.58%)
2025-08-07 17:41:32,140 - INFO - 
字幕 #55 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:32,140 - INFO - 生成的视频文件:
2025-08-07 17:41:32,140 - INFO -   1. F:/github/aicut_auto/newcut_ai\55_1.mp4
2025-08-07 17:41:32,140 - INFO -   2. F:/github/aicut_auto/newcut_ai\55_2.mp4
2025-08-07 17:41:32,140 - INFO -   3. F:/github/aicut_auto/newcut_ai\55_3.mp4
2025-08-07 17:41:32,140 - INFO - ========== 字幕 #55 处理结束 ==========

