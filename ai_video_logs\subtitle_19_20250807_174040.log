2025-08-07 17:40:40,329 - INFO - ========== 字幕 #19 处理开始 ==========
2025-08-07 17:40:40,329 - INFO - 字幕内容: 二弟见状愈发嚣张，当众羞辱他们父女，一个残废配一个骗子，也敢来争继承人之位。
2025-08-07 17:40:40,329 - INFO - 字幕序号: [486, 499]
2025-08-07 17:40:40,330 - INFO - 音频文件详情:
2025-08-07 17:40:40,330 - INFO -   - 路径: output\19.wav
2025-08-07 17:40:40,330 - INFO -   - 时长: 5.46秒
2025-08-07 17:40:40,330 - INFO -   - 验证音频时长: 5.46秒
2025-08-07 17:40:40,330 - INFO - 字幕时间戳信息:
2025-08-07 17:40:40,330 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:40,330 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:40,330 - INFO -   - 根据生成的音频时长(5.46秒)已调整字幕时间戳
2025-08-07 17:40:40,330 - INFO - ========== 新模式：为字幕 #19 生成4套场景方案 ==========
2025-08-07 17:40:40,330 - INFO - 字幕序号列表: [486, 499]
2025-08-07 17:40:40,330 - INFO - 
--- 生成方案 #1：基于字幕序号 #486 ---
2025-08-07 17:40:40,330 - INFO - 开始为单个字幕序号 #486 匹配场景，目标时长: 5.46秒
2025-08-07 17:40:40,330 - INFO - 开始查找字幕序号 [486] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:40,330 - INFO - 找到related_overlap场景: scene_id=648, 字幕#486
2025-08-07 17:40:40,332 - INFO - 字幕 #486 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:40,332 - INFO - 字幕序号 #486 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:40,332 - INFO - 选择第一个overlap场景作为起点: scene_id=648
2025-08-07 17:40:40,332 - INFO - 添加起点场景: scene_id=648, 时长=2.24秒, 累计时长=2.24秒
2025-08-07 17:40:40,332 - INFO - 起点场景时长不足，需要延伸填充 3.22秒
2025-08-07 17:40:40,332 - INFO - 起点场景在原始列表中的索引: 647
2025-08-07 17:40:40,332 - INFO - 延伸添加场景: scene_id=649 (完整时长 1.84秒)
2025-08-07 17:40:40,332 - INFO - 累计时长: 4.08秒
2025-08-07 17:40:40,332 - INFO - 延伸添加场景: scene_id=650 (裁剪至 1.38秒)
2025-08-07 17:40:40,332 - INFO - 累计时长: 5.46秒
2025-08-07 17:40:40,332 - INFO - 字幕序号 #486 场景匹配完成，共选择 3 个场景，总时长: 5.46秒
2025-08-07 17:40:40,332 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-08-07 17:40:40,332 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-08-07 17:40:40,332 - INFO - 
--- 生成方案 #2：基于字幕序号 #499 ---
2025-08-07 17:40:40,332 - INFO - 开始为单个字幕序号 #499 匹配场景，目标时长: 5.46秒
2025-08-07 17:40:40,332 - INFO - 开始查找字幕序号 [499] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:40,332 - INFO - 找到related_overlap场景: scene_id=657, 字幕#499
2025-08-07 17:40:40,333 - INFO - 找到related_between场景: scene_id=658, 字幕#499
2025-08-07 17:40:40,333 - INFO - 找到related_between场景: scene_id=659, 字幕#499
2025-08-07 17:40:40,333 - INFO - 字幕 #499 找到 1 个overlap场景, 2 个between场景
2025-08-07 17:40:40,333 - INFO - 字幕序号 #499 找到 1 个可用overlap场景, 2 个可用between场景
2025-08-07 17:40:40,333 - INFO - 选择第一个overlap场景作为起点: scene_id=657
2025-08-07 17:40:40,333 - INFO - 添加起点场景: scene_id=657, 时长=1.04秒, 累计时长=1.04秒
2025-08-07 17:40:40,333 - INFO - 起点场景时长不足，需要延伸填充 4.42秒
2025-08-07 17:40:40,333 - INFO - 起点场景在原始列表中的索引: 656
2025-08-07 17:40:40,333 - INFO - 延伸添加场景: scene_id=658 (完整时长 2.28秒)
2025-08-07 17:40:40,333 - INFO - 累计时长: 3.32秒
2025-08-07 17:40:40,333 - INFO - 延伸添加场景: scene_id=659 (完整时长 1.88秒)
2025-08-07 17:40:40,333 - INFO - 累计时长: 5.20秒
2025-08-07 17:40:40,333 - INFO - 延伸添加场景: scene_id=660 (裁剪至 0.27秒)
2025-08-07 17:40:40,333 - INFO - 累计时长: 5.46秒
2025-08-07 17:40:40,333 - INFO - 字幕序号 #499 场景匹配完成，共选择 4 个场景，总时长: 5.46秒
2025-08-07 17:40:40,333 - INFO - 方案 #2 生成成功，包含 4 个场景
2025-08-07 17:40:40,333 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:40,333 - INFO - ========== 当前模式：为字幕 #19 生成 1 套场景方案 ==========
2025-08-07 17:40:40,333 - INFO - 开始查找字幕序号 [486, 499] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:40,334 - INFO - 找到related_overlap场景: scene_id=648, 字幕#486
2025-08-07 17:40:40,334 - INFO - 找到related_overlap场景: scene_id=657, 字幕#499
2025-08-07 17:40:40,334 - INFO - 找到related_between场景: scene_id=658, 字幕#499
2025-08-07 17:40:40,334 - INFO - 找到related_between场景: scene_id=659, 字幕#499
2025-08-07 17:40:40,334 - INFO - 字幕 #486 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:40,335 - INFO - 字幕 #499 找到 1 个overlap场景, 2 个between场景
2025-08-07 17:40:40,335 - INFO - 共收集 2 个未使用的overlap场景和 2 个未使用的between场景
2025-08-07 17:40:40,335 - INFO - 开始生成方案 #1
2025-08-07 17:40:40,335 - INFO - 方案 #1: 为字幕#486选择初始化overlap场景id=648
2025-08-07 17:40:40,335 - INFO - 方案 #1: 为字幕#499选择初始化overlap场景id=657
2025-08-07 17:40:40,335 - INFO - 方案 #1: 初始选择后，当前总时长=3.28秒
2025-08-07 17:40:40,335 - INFO - 方案 #1: 额外between选择后，当前总时长=3.28秒
2025-08-07 17:40:40,335 - INFO - 方案 #1: 额外添加between场景id=658, 当前总时长=5.56秒
2025-08-07 17:40:40,335 - INFO - 方案 #1: 场景总时长(5.56秒)大于音频时长(5.46秒)，需要裁剪
2025-08-07 17:40:40,335 - INFO - 调整前总时长: 5.56秒, 目标时长: 5.46秒
2025-08-07 17:40:40,335 - INFO - 需要裁剪 0.09秒
2025-08-07 17:40:40,335 - INFO - 裁剪最长场景ID=658：从2.28秒裁剪至2.18秒
2025-08-07 17:40:40,335 - INFO - 调整后总时长: 5.46秒，与目标时长差异: 0.00秒
2025-08-07 17:40:40,335 - INFO - 方案 #1 调整/填充后最终总时长: 5.46秒
2025-08-07 17:40:40,335 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:40,335 - INFO - ========== 当前模式：字幕 #19 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:40,335 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:40,335 - INFO - ========== 新模式：字幕 #19 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:40,335 - INFO - 
----- 处理字幕 #19 的方案 #1 -----
2025-08-07 17:40:40,335 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\19_1.mp4
2025-08-07 17:40:40,335 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpo7i428ch
2025-08-07 17:40:40,335 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\648.mp4 (确认存在: True)
2025-08-07 17:40:40,335 - INFO - 添加场景ID=648，时长=2.24秒，累计时长=2.24秒
2025-08-07 17:40:40,335 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\649.mp4 (确认存在: True)
2025-08-07 17:40:40,335 - INFO - 添加场景ID=649，时长=1.84秒，累计时长=4.08秒
2025-08-07 17:40:40,335 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\650.mp4 (确认存在: True)
2025-08-07 17:40:40,335 - INFO - 添加场景ID=650，时长=2.00秒，累计时长=6.08秒
2025-08-07 17:40:40,336 - INFO - 准备合并 3 个场景文件，总时长约 6.08秒
2025-08-07 17:40:40,336 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/648.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/649.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/650.mp4'

2025-08-07 17:40:40,336 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpo7i428ch\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpo7i428ch\temp_combined.mp4
2025-08-07 17:40:40,470 - INFO - 合并后的视频时长: 6.15秒，目标音频时长: 5.46秒
2025-08-07 17:40:40,470 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpo7i428ch\temp_combined.mp4 -ss 0 -to 5.463 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\19_1.mp4
2025-08-07 17:40:40,777 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:40,777 - INFO - 目标音频时长: 5.46秒
2025-08-07 17:40:40,777 - INFO - 实际视频时长: 5.50秒
2025-08-07 17:40:40,777 - INFO - 时长差异: 0.04秒 (0.73%)
2025-08-07 17:40:40,777 - INFO - ==========================================
2025-08-07 17:40:40,777 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:40,777 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\19_1.mp4
2025-08-07 17:40:40,777 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpo7i428ch
2025-08-07 17:40:40,824 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:40,824 - INFO -   - 音频时长: 5.46秒
2025-08-07 17:40:40,824 - INFO -   - 视频时长: 5.50秒
2025-08-07 17:40:40,824 - INFO -   - 时长差异: 0.04秒 (0.73%)
2025-08-07 17:40:40,824 - INFO - 
----- 处理字幕 #19 的方案 #2 -----
2025-08-07 17:40:40,824 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\19_2.mp4
2025-08-07 17:40:40,824 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6962pwue
2025-08-07 17:40:40,825 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\657.mp4 (确认存在: True)
2025-08-07 17:40:40,825 - INFO - 添加场景ID=657，时长=1.04秒，累计时长=1.04秒
2025-08-07 17:40:40,825 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\658.mp4 (确认存在: True)
2025-08-07 17:40:40,825 - INFO - 添加场景ID=658，时长=2.28秒，累计时长=3.32秒
2025-08-07 17:40:40,825 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\659.mp4 (确认存在: True)
2025-08-07 17:40:40,825 - INFO - 添加场景ID=659，时长=1.88秒，累计时长=5.20秒
2025-08-07 17:40:40,825 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\660.mp4 (确认存在: True)
2025-08-07 17:40:40,825 - INFO - 添加场景ID=660，时长=1.92秒，累计时长=7.12秒
2025-08-07 17:40:40,825 - INFO - 准备合并 4 个场景文件，总时长约 7.12秒
2025-08-07 17:40:40,825 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/657.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/658.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/659.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/660.mp4'

2025-08-07 17:40:40,825 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp6962pwue\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp6962pwue\temp_combined.mp4
2025-08-07 17:40:40,969 - INFO - 合并后的视频时长: 7.21秒，目标音频时长: 5.46秒
2025-08-07 17:40:40,969 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp6962pwue\temp_combined.mp4 -ss 0 -to 5.463 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\19_2.mp4
2025-08-07 17:40:41,278 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:41,278 - INFO - 目标音频时长: 5.46秒
2025-08-07 17:40:41,278 - INFO - 实际视频时长: 5.50秒
2025-08-07 17:40:41,278 - INFO - 时长差异: 0.04秒 (0.73%)
2025-08-07 17:40:41,278 - INFO - ==========================================
2025-08-07 17:40:41,278 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:41,278 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\19_2.mp4
2025-08-07 17:40:41,279 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp6962pwue
2025-08-07 17:40:41,322 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:41,322 - INFO -   - 音频时长: 5.46秒
2025-08-07 17:40:41,322 - INFO -   - 视频时长: 5.50秒
2025-08-07 17:40:41,322 - INFO -   - 时长差异: 0.04秒 (0.73%)
2025-08-07 17:40:41,322 - INFO - 
----- 处理字幕 #19 的方案 #3 -----
2025-08-07 17:40:41,322 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\19_3.mp4
2025-08-07 17:40:41,322 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8ffu5ybz
2025-08-07 17:40:41,323 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\648.mp4 (确认存在: True)
2025-08-07 17:40:41,323 - INFO - 添加场景ID=648，时长=2.24秒，累计时长=2.24秒
2025-08-07 17:40:41,323 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\657.mp4 (确认存在: True)
2025-08-07 17:40:41,323 - INFO - 添加场景ID=657，时长=1.04秒，累计时长=3.28秒
2025-08-07 17:40:41,323 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\658.mp4 (确认存在: True)
2025-08-07 17:40:41,323 - INFO - 添加场景ID=658，时长=2.28秒，累计时长=5.56秒
2025-08-07 17:40:41,323 - INFO - 准备合并 3 个场景文件，总时长约 5.56秒
2025-08-07 17:40:41,323 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/648.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/657.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/658.mp4'

2025-08-07 17:40:41,323 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp8ffu5ybz\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp8ffu5ybz\temp_combined.mp4
2025-08-07 17:40:41,470 - INFO - 合并后的视频时长: 5.63秒，目标音频时长: 5.46秒
2025-08-07 17:40:41,470 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp8ffu5ybz\temp_combined.mp4 -ss 0 -to 5.463 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\19_3.mp4
2025-08-07 17:40:41,797 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:41,797 - INFO - 目标音频时长: 5.46秒
2025-08-07 17:40:41,797 - INFO - 实际视频时长: 5.50秒
2025-08-07 17:40:41,797 - INFO - 时长差异: 0.04秒 (0.73%)
2025-08-07 17:40:41,797 - INFO - ==========================================
2025-08-07 17:40:41,797 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:41,797 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\19_3.mp4
2025-08-07 17:40:41,798 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp8ffu5ybz
2025-08-07 17:40:41,845 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:41,845 - INFO -   - 音频时长: 5.46秒
2025-08-07 17:40:41,845 - INFO -   - 视频时长: 5.50秒
2025-08-07 17:40:41,845 - INFO -   - 时长差异: 0.04秒 (0.73%)
2025-08-07 17:40:41,845 - INFO - 
字幕 #19 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:41,845 - INFO - 生成的视频文件:
2025-08-07 17:40:41,845 - INFO -   1. F:/github/aicut_auto/newcut_ai\19_1.mp4
2025-08-07 17:40:41,845 - INFO -   2. F:/github/aicut_auto/newcut_ai\19_2.mp4
2025-08-07 17:40:41,845 - INFO -   3. F:/github/aicut_auto/newcut_ai\19_3.mp4
2025-08-07 17:40:41,846 - INFO - ========== 字幕 #19 处理结束 ==========

