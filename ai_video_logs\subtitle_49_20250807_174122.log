2025-08-07 17:41:22,193 - INFO - ========== 字幕 #49 处理开始 ==========
2025-08-07 17:41:22,193 - INFO - 字幕内容: 这番谎话让女孩啼笑皆非，她当场揭穿谎言，宣布自己才是真正的清音医仙。
2025-08-07 17:41:22,193 - INFO - 字幕序号: [2122, 2162]
2025-08-07 17:41:22,193 - INFO - 音频文件详情:
2025-08-07 17:41:22,193 - INFO -   - 路径: output\49.wav
2025-08-07 17:41:22,193 - INFO -   - 时长: 4.75秒
2025-08-07 17:41:22,193 - INFO -   - 验证音频时长: 4.75秒
2025-08-07 17:41:22,193 - INFO - 字幕时间戳信息:
2025-08-07 17:41:22,194 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:41:22,194 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:41:22,194 - INFO -   - 根据生成的音频时长(4.75秒)已调整字幕时间戳
2025-08-07 17:41:22,194 - INFO - ========== 新模式：为字幕 #49 生成4套场景方案 ==========
2025-08-07 17:41:22,194 - INFO - 字幕序号列表: [2122, 2162]
2025-08-07 17:41:22,194 - INFO - 
--- 生成方案 #1：基于字幕序号 #2122 ---
2025-08-07 17:41:22,194 - INFO - 开始为单个字幕序号 #2122 匹配场景，目标时长: 4.75秒
2025-08-07 17:41:22,194 - INFO - 开始查找字幕序号 [2122] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:22,194 - INFO - 找到related_overlap场景: scene_id=2384, 字幕#2122
2025-08-07 17:41:22,195 - INFO - 字幕 #2122 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:22,195 - INFO - 字幕序号 #2122 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:22,195 - INFO - 选择第一个overlap场景作为起点: scene_id=2384
2025-08-07 17:41:22,195 - INFO - 添加起点场景: scene_id=2384, 时长=4.12秒, 累计时长=4.12秒
2025-08-07 17:41:22,195 - INFO - 起点场景时长不足，需要延伸填充 0.63秒
2025-08-07 17:41:22,195 - INFO - 起点场景在原始列表中的索引: 2383
2025-08-07 17:41:22,195 - INFO - 延伸添加场景: scene_id=2385 (裁剪至 0.63秒)
2025-08-07 17:41:22,195 - INFO - 累计时长: 4.75秒
2025-08-07 17:41:22,195 - INFO - 字幕序号 #2122 场景匹配完成，共选择 2 个场景，总时长: 4.75秒
2025-08-07 17:41:22,195 - INFO - 方案 #1 生成成功，包含 2 个场景
2025-08-07 17:41:22,195 - INFO - 新模式：第1套方案的 2 个场景已加入全局已使用集合
2025-08-07 17:41:22,195 - INFO - 
--- 生成方案 #2：基于字幕序号 #2162 ---
2025-08-07 17:41:22,195 - INFO - 开始为单个字幕序号 #2162 匹配场景，目标时长: 4.75秒
2025-08-07 17:41:22,195 - INFO - 开始查找字幕序号 [2162] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:22,195 - INFO - 找到related_overlap场景: scene_id=2425, 字幕#2162
2025-08-07 17:41:22,196 - INFO - 字幕 #2162 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:22,196 - INFO - 字幕序号 #2162 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:41:22,196 - INFO - 选择第一个overlap场景作为起点: scene_id=2425
2025-08-07 17:41:22,196 - INFO - 添加起点场景: scene_id=2425, 时长=3.36秒, 累计时长=3.36秒
2025-08-07 17:41:22,196 - INFO - 起点场景时长不足，需要延伸填充 1.39秒
2025-08-07 17:41:22,196 - INFO - 起点场景在原始列表中的索引: 2424
2025-08-07 17:41:22,196 - INFO - 延伸添加场景: scene_id=2426 (裁剪至 1.39秒)
2025-08-07 17:41:22,196 - INFO - 累计时长: 4.75秒
2025-08-07 17:41:22,196 - INFO - 字幕序号 #2162 场景匹配完成，共选择 2 个场景，总时长: 4.75秒
2025-08-07 17:41:22,196 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-08-07 17:41:22,196 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:41:22,196 - INFO - ========== 当前模式：为字幕 #49 生成 1 套场景方案 ==========
2025-08-07 17:41:22,196 - INFO - 开始查找字幕序号 [2122, 2162] 对应的场景，共有 3480 个场景可选
2025-08-07 17:41:22,197 - INFO - 找到related_overlap场景: scene_id=2384, 字幕#2122
2025-08-07 17:41:22,197 - INFO - 找到related_overlap场景: scene_id=2425, 字幕#2162
2025-08-07 17:41:22,197 - INFO - 字幕 #2122 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:22,197 - INFO - 字幕 #2162 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:41:22,197 - INFO - 共收集 2 个未使用的overlap场景和 0 个未使用的between场景
2025-08-07 17:41:22,197 - INFO - 开始生成方案 #1
2025-08-07 17:41:22,197 - INFO - 方案 #1: 为字幕#2122选择初始化overlap场景id=2384
2025-08-07 17:41:22,197 - INFO - 方案 #1: 为字幕#2162选择初始化overlap场景id=2425
2025-08-07 17:41:22,197 - INFO - 方案 #1: 初始选择后，当前总时长=7.48秒
2025-08-07 17:41:22,197 - INFO - 方案 #1: 额外between选择后，当前总时长=7.48秒
2025-08-07 17:41:22,197 - INFO - 方案 #1: 场景总时长(7.48秒)大于音频时长(4.75秒)，需要裁剪
2025-08-07 17:41:22,197 - INFO - 调整前总时长: 7.48秒, 目标时长: 4.75秒
2025-08-07 17:41:22,197 - INFO - 需要裁剪 2.73秒
2025-08-07 17:41:22,197 - INFO - 裁剪最长场景ID=2384：从4.12秒裁剪至1.39秒
2025-08-07 17:41:22,197 - INFO - 调整后总时长: 4.75秒，与目标时长差异: 0.00秒
2025-08-07 17:41:22,198 - INFO - 方案 #1 调整/填充后最终总时长: 4.75秒
2025-08-07 17:41:22,198 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:41:22,198 - INFO - ========== 当前模式：字幕 #49 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:41:22,198 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:41:22,198 - INFO - ========== 新模式：字幕 #49 共生成 3 套有效场景方案 ==========
2025-08-07 17:41:22,198 - INFO - 
----- 处理字幕 #49 的方案 #1 -----
2025-08-07 17:41:22,198 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\49_1.mp4
2025-08-07 17:41:22,198 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpu14b8eg5
2025-08-07 17:41:22,198 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2384.mp4 (确认存在: True)
2025-08-07 17:41:22,198 - INFO - 添加场景ID=2384，时长=4.12秒，累计时长=4.12秒
2025-08-07 17:41:22,198 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2385.mp4 (确认存在: True)
2025-08-07 17:41:22,199 - INFO - 添加场景ID=2385，时长=1.12秒，累计时长=5.24秒
2025-08-07 17:41:22,199 - INFO - 准备合并 2 个场景文件，总时长约 5.24秒
2025-08-07 17:41:22,199 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2384.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2385.mp4'

2025-08-07 17:41:22,199 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpu14b8eg5\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpu14b8eg5\temp_combined.mp4
2025-08-07 17:41:22,321 - INFO - 合并后的视频时长: 5.29秒，目标音频时长: 4.75秒
2025-08-07 17:41:22,321 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpu14b8eg5\temp_combined.mp4 -ss 0 -to 4.75 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\49_1.mp4
2025-08-07 17:41:22,630 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:22,630 - INFO - 目标音频时长: 4.75秒
2025-08-07 17:41:22,630 - INFO - 实际视频时长: 4.78秒
2025-08-07 17:41:22,630 - INFO - 时长差异: 0.03秒 (0.69%)
2025-08-07 17:41:22,630 - INFO - ==========================================
2025-08-07 17:41:22,630 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:22,630 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\49_1.mp4
2025-08-07 17:41:22,631 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpu14b8eg5
2025-08-07 17:41:22,675 - INFO - 方案 #1 处理完成:
2025-08-07 17:41:22,675 - INFO -   - 音频时长: 4.75秒
2025-08-07 17:41:22,675 - INFO -   - 视频时长: 4.78秒
2025-08-07 17:41:22,675 - INFO -   - 时长差异: 0.03秒 (0.69%)
2025-08-07 17:41:22,675 - INFO - 
----- 处理字幕 #49 的方案 #2 -----
2025-08-07 17:41:22,675 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\49_2.mp4
2025-08-07 17:41:22,675 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsm7uqcph
2025-08-07 17:41:22,676 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2425.mp4 (确认存在: True)
2025-08-07 17:41:22,676 - INFO - 添加场景ID=2425，时长=3.36秒，累计时长=3.36秒
2025-08-07 17:41:22,676 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2426.mp4 (确认存在: True)
2025-08-07 17:41:22,676 - INFO - 添加场景ID=2426，时长=1.88秒，累计时长=5.24秒
2025-08-07 17:41:22,676 - INFO - 准备合并 2 个场景文件，总时长约 5.24秒
2025-08-07 17:41:22,676 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2425.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2426.mp4'

2025-08-07 17:41:22,676 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpsm7uqcph\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpsm7uqcph\temp_combined.mp4
2025-08-07 17:41:22,808 - INFO - 合并后的视频时长: 5.29秒，目标音频时长: 4.75秒
2025-08-07 17:41:22,808 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpsm7uqcph\temp_combined.mp4 -ss 0 -to 4.75 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\49_2.mp4
2025-08-07 17:41:23,099 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:23,099 - INFO - 目标音频时长: 4.75秒
2025-08-07 17:41:23,099 - INFO - 实际视频时长: 4.78秒
2025-08-07 17:41:23,099 - INFO - 时长差异: 0.03秒 (0.69%)
2025-08-07 17:41:23,099 - INFO - ==========================================
2025-08-07 17:41:23,099 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:23,099 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\49_2.mp4
2025-08-07 17:41:23,100 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpsm7uqcph
2025-08-07 17:41:23,146 - INFO - 方案 #2 处理完成:
2025-08-07 17:41:23,146 - INFO -   - 音频时长: 4.75秒
2025-08-07 17:41:23,146 - INFO -   - 视频时长: 4.78秒
2025-08-07 17:41:23,146 - INFO -   - 时长差异: 0.03秒 (0.69%)
2025-08-07 17:41:23,146 - INFO - 
----- 处理字幕 #49 的方案 #3 -----
2025-08-07 17:41:23,146 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\49_3.mp4
2025-08-07 17:41:23,147 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpt5viq550
2025-08-07 17:41:23,147 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2384.mp4 (确认存在: True)
2025-08-07 17:41:23,147 - INFO - 添加场景ID=2384，时长=4.12秒，累计时长=4.12秒
2025-08-07 17:41:23,147 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\2425.mp4 (确认存在: True)
2025-08-07 17:41:23,147 - INFO - 添加场景ID=2425，时长=3.36秒，累计时长=7.48秒
2025-08-07 17:41:23,147 - INFO - 场景总时长(7.48秒)已达到音频时长(4.75秒)的1.5倍，停止添加场景
2025-08-07 17:41:23,147 - INFO - 准备合并 2 个场景文件，总时长约 7.48秒
2025-08-07 17:41:23,148 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/2384.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/2425.mp4'

2025-08-07 17:41:23,148 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpt5viq550\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpt5viq550\temp_combined.mp4
2025-08-07 17:41:23,278 - INFO - 合并后的视频时长: 7.53秒，目标音频时长: 4.75秒
2025-08-07 17:41:23,278 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpt5viq550\temp_combined.mp4 -ss 0 -to 4.75 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\49_3.mp4
2025-08-07 17:41:23,581 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:41:23,581 - INFO - 目标音频时长: 4.75秒
2025-08-07 17:41:23,581 - INFO - 实际视频时长: 4.78秒
2025-08-07 17:41:23,581 - INFO - 时长差异: 0.03秒 (0.69%)
2025-08-07 17:41:23,581 - INFO - ==========================================
2025-08-07 17:41:23,581 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:41:23,581 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\49_3.mp4
2025-08-07 17:41:23,582 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpt5viq550
2025-08-07 17:41:23,624 - INFO - 方案 #3 处理完成:
2025-08-07 17:41:23,624 - INFO -   - 音频时长: 4.75秒
2025-08-07 17:41:23,625 - INFO -   - 视频时长: 4.78秒
2025-08-07 17:41:23,625 - INFO -   - 时长差异: 0.03秒 (0.69%)
2025-08-07 17:41:23,625 - INFO - 
字幕 #49 处理完成，成功生成 3/3 套方案
2025-08-07 17:41:23,625 - INFO - 生成的视频文件:
2025-08-07 17:41:23,625 - INFO -   1. F:/github/aicut_auto/newcut_ai\49_1.mp4
2025-08-07 17:41:23,625 - INFO -   2. F:/github/aicut_auto/newcut_ai\49_2.mp4
2025-08-07 17:41:23,625 - INFO -   3. F:/github/aicut_auto/newcut_ai\49_3.mp4
2025-08-07 17:41:23,625 - INFO - ========== 字幕 #49 处理结束 ==========

