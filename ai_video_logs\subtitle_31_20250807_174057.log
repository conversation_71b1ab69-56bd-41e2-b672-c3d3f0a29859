2025-08-07 17:40:57,577 - INFO - ========== 字幕 #31 处理开始 ==========
2025-08-07 17:40:57,577 - INFO - 字幕内容: 寿宴结束后，丧心病狂的二弟竟打算开车撞死男人和女孩，制造一场意外事故。
2025-08-07 17:40:57,577 - INFO - 字幕序号: [943, 955]
2025-08-07 17:40:57,577 - INFO - 音频文件详情:
2025-08-07 17:40:57,577 - INFO -   - 路径: output\31.wav
2025-08-07 17:40:57,577 - INFO -   - 时长: 4.31秒
2025-08-07 17:40:57,577 - INFO -   - 验证音频时长: 4.31秒
2025-08-07 17:40:57,577 - INFO - 字幕时间戳信息:
2025-08-07 17:40:57,577 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:57,577 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:57,577 - INFO -   - 根据生成的音频时长(4.31秒)已调整字幕时间戳
2025-08-07 17:40:57,577 - INFO - ========== 新模式：为字幕 #31 生成4套场景方案 ==========
2025-08-07 17:40:57,577 - INFO - 字幕序号列表: [943, 955]
2025-08-07 17:40:57,578 - INFO - 
--- 生成方案 #1：基于字幕序号 #943 ---
2025-08-07 17:40:57,578 - INFO - 开始为单个字幕序号 #943 匹配场景，目标时长: 4.31秒
2025-08-07 17:40:57,578 - INFO - 开始查找字幕序号 [943] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:57,578 - INFO - 找到related_overlap场景: scene_id=1149, 字幕#943
2025-08-07 17:40:57,578 - INFO - 找到related_between场景: scene_id=1146, 字幕#943
2025-08-07 17:40:57,579 - INFO - 找到related_between场景: scene_id=1147, 字幕#943
2025-08-07 17:40:57,579 - INFO - 找到related_between场景: scene_id=1148, 字幕#943
2025-08-07 17:40:57,579 - INFO - 字幕 #943 找到 1 个overlap场景, 3 个between场景
2025-08-07 17:40:57,579 - INFO - 字幕序号 #943 找到 1 个可用overlap场景, 3 个可用between场景
2025-08-07 17:40:57,579 - INFO - 选择第一个overlap场景作为起点: scene_id=1149
2025-08-07 17:40:57,579 - INFO - 添加起点场景: scene_id=1149, 时长=0.92秒, 累计时长=0.92秒
2025-08-07 17:40:57,579 - INFO - 起点场景时长不足，需要延伸填充 3.39秒
2025-08-07 17:40:57,579 - INFO - 起点场景在原始列表中的索引: 1148
2025-08-07 17:40:57,579 - INFO - 延伸添加场景: scene_id=1150 (完整时长 1.48秒)
2025-08-07 17:40:57,579 - INFO - 累计时长: 2.40秒
2025-08-07 17:40:57,579 - INFO - 延伸添加场景: scene_id=1151 (完整时长 1.68秒)
2025-08-07 17:40:57,579 - INFO - 累计时长: 4.08秒
2025-08-07 17:40:57,579 - INFO - 延伸添加场景: scene_id=1152 (裁剪至 0.23秒)
2025-08-07 17:40:57,579 - INFO - 累计时长: 4.31秒
2025-08-07 17:40:57,579 - INFO - 字幕序号 #943 场景匹配完成，共选择 4 个场景，总时长: 4.31秒
2025-08-07 17:40:57,579 - INFO - 方案 #1 生成成功，包含 4 个场景
2025-08-07 17:40:57,579 - INFO - 新模式：第1套方案的 4 个场景已加入全局已使用集合
2025-08-07 17:40:57,579 - INFO - 
--- 生成方案 #2：基于字幕序号 #955 ---
2025-08-07 17:40:57,579 - INFO - 开始为单个字幕序号 #955 匹配场景，目标时长: 4.31秒
2025-08-07 17:40:57,579 - INFO - 开始查找字幕序号 [955] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:57,579 - INFO - 找到related_overlap场景: scene_id=1159, 字幕#955
2025-08-07 17:40:57,580 - INFO - 找到related_between场景: scene_id=1160, 字幕#955
2025-08-07 17:40:57,580 - INFO - 找到related_between场景: scene_id=1161, 字幕#955
2025-08-07 17:40:57,580 - INFO - 字幕 #955 找到 1 个overlap场景, 2 个between场景
2025-08-07 17:40:57,580 - INFO - 字幕序号 #955 找到 1 个可用overlap场景, 2 个可用between场景
2025-08-07 17:40:57,580 - INFO - 选择第一个overlap场景作为起点: scene_id=1159
2025-08-07 17:40:57,580 - INFO - 添加起点场景: scene_id=1159, 时长=3.20秒, 累计时长=3.20秒
2025-08-07 17:40:57,580 - INFO - 起点场景时长不足，需要延伸填充 1.11秒
2025-08-07 17:40:57,580 - INFO - 起点场景在原始列表中的索引: 1158
2025-08-07 17:40:57,580 - INFO - 延伸添加场景: scene_id=1160 (裁剪至 1.11秒)
2025-08-07 17:40:57,580 - INFO - 累计时长: 4.31秒
2025-08-07 17:40:57,580 - INFO - 字幕序号 #955 场景匹配完成，共选择 2 个场景，总时长: 4.31秒
2025-08-07 17:40:57,580 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-08-07 17:40:57,580 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:57,581 - INFO - ========== 当前模式：为字幕 #31 生成 1 套场景方案 ==========
2025-08-07 17:40:57,581 - INFO - 开始查找字幕序号 [943, 955] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:57,581 - INFO - 找到related_overlap场景: scene_id=1149, 字幕#943
2025-08-07 17:40:57,581 - INFO - 找到related_overlap场景: scene_id=1159, 字幕#955
2025-08-07 17:40:57,581 - INFO - 找到related_between场景: scene_id=1146, 字幕#943
2025-08-07 17:40:57,581 - INFO - 找到related_between场景: scene_id=1147, 字幕#943
2025-08-07 17:40:57,581 - INFO - 找到related_between场景: scene_id=1148, 字幕#943
2025-08-07 17:40:57,581 - INFO - 找到related_between场景: scene_id=1160, 字幕#955
2025-08-07 17:40:57,581 - INFO - 找到related_between场景: scene_id=1161, 字幕#955
2025-08-07 17:40:57,582 - INFO - 字幕 #943 找到 1 个overlap场景, 3 个between场景
2025-08-07 17:40:57,582 - INFO - 字幕 #955 找到 1 个overlap场景, 2 个between场景
2025-08-07 17:40:57,582 - INFO - 共收集 2 个未使用的overlap场景和 5 个未使用的between场景
2025-08-07 17:40:57,582 - INFO - 开始生成方案 #1
2025-08-07 17:40:57,582 - INFO - 方案 #1: 为字幕#943选择初始化overlap场景id=1149
2025-08-07 17:40:57,582 - INFO - 方案 #1: 为字幕#955选择初始化overlap场景id=1159
2025-08-07 17:40:57,582 - INFO - 方案 #1: 初始选择后，当前总时长=4.12秒
2025-08-07 17:40:57,582 - INFO - 方案 #1: 额外between选择后，当前总时长=4.12秒
2025-08-07 17:40:57,582 - INFO - 方案 #1: 额外添加between场景id=1147, 当前总时长=5.72秒
2025-08-07 17:40:57,582 - INFO - 方案 #1: 场景总时长(5.72秒)大于音频时长(4.31秒)，需要裁剪
2025-08-07 17:40:57,582 - INFO - 调整前总时长: 5.72秒, 目标时长: 4.31秒
2025-08-07 17:40:57,582 - INFO - 需要裁剪 1.41秒
2025-08-07 17:40:57,582 - INFO - 裁剪最长场景ID=1159：从3.20秒裁剪至1.79秒
2025-08-07 17:40:57,582 - INFO - 调整后总时长: 4.31秒，与目标时长差异: 0.00秒
2025-08-07 17:40:57,582 - INFO - 方案 #1 调整/填充后最终总时长: 4.31秒
2025-08-07 17:40:57,582 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:57,582 - INFO - ========== 当前模式：字幕 #31 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:57,582 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:57,582 - INFO - ========== 新模式：字幕 #31 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:57,582 - INFO - 
----- 处理字幕 #31 的方案 #1 -----
2025-08-07 17:40:57,582 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\31_1.mp4
2025-08-07 17:40:57,582 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5sv56_8o
2025-08-07 17:40:57,583 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1149.mp4 (确认存在: True)
2025-08-07 17:40:57,583 - INFO - 添加场景ID=1149，时长=0.92秒，累计时长=0.92秒
2025-08-07 17:40:57,583 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1150.mp4 (确认存在: True)
2025-08-07 17:40:57,583 - INFO - 添加场景ID=1150，时长=1.48秒，累计时长=2.40秒
2025-08-07 17:40:57,583 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1151.mp4 (确认存在: True)
2025-08-07 17:40:57,583 - INFO - 添加场景ID=1151，时长=1.68秒，累计时长=4.08秒
2025-08-07 17:40:57,583 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1152.mp4 (确认存在: True)
2025-08-07 17:40:57,583 - INFO - 添加场景ID=1152，时长=2.12秒，累计时长=6.20秒
2025-08-07 17:40:57,583 - INFO - 准备合并 4 个场景文件，总时长约 6.20秒
2025-08-07 17:40:57,583 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1149.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1150.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1151.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1152.mp4'

2025-08-07 17:40:57,583 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp5sv56_8o\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp5sv56_8o\temp_combined.mp4
2025-08-07 17:40:57,731 - INFO - 合并后的视频时长: 6.29秒，目标音频时长: 4.31秒
2025-08-07 17:40:57,731 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp5sv56_8o\temp_combined.mp4 -ss 0 -to 4.306 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\31_1.mp4
2025-08-07 17:40:58,027 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:58,027 - INFO - 目标音频时长: 4.31秒
2025-08-07 17:40:58,027 - INFO - 实际视频时长: 4.34秒
2025-08-07 17:40:58,027 - INFO - 时长差异: 0.04秒 (0.86%)
2025-08-07 17:40:58,027 - INFO - ==========================================
2025-08-07 17:40:58,027 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:58,027 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\31_1.mp4
2025-08-07 17:40:58,027 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5sv56_8o
2025-08-07 17:40:58,081 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:58,081 - INFO -   - 音频时长: 4.31秒
2025-08-07 17:40:58,081 - INFO -   - 视频时长: 4.34秒
2025-08-07 17:40:58,081 - INFO -   - 时长差异: 0.04秒 (0.86%)
2025-08-07 17:40:58,081 - INFO - 
----- 处理字幕 #31 的方案 #2 -----
2025-08-07 17:40:58,081 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\31_2.mp4
2025-08-07 17:40:58,081 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp9z378ix3
2025-08-07 17:40:58,082 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1159.mp4 (确认存在: True)
2025-08-07 17:40:58,082 - INFO - 添加场景ID=1159，时长=3.20秒，累计时长=3.20秒
2025-08-07 17:40:58,082 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1160.mp4 (确认存在: True)
2025-08-07 17:40:58,082 - INFO - 添加场景ID=1160，时长=1.68秒，累计时长=4.88秒
2025-08-07 17:40:58,082 - INFO - 准备合并 2 个场景文件，总时长约 4.88秒
2025-08-07 17:40:58,082 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1159.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1160.mp4'

2025-08-07 17:40:58,082 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp9z378ix3\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp9z378ix3\temp_combined.mp4
2025-08-07 17:40:58,207 - INFO - 合并后的视频时长: 4.93秒，目标音频时长: 4.31秒
2025-08-07 17:40:58,207 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp9z378ix3\temp_combined.mp4 -ss 0 -to 4.306 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\31_2.mp4
2025-08-07 17:40:58,484 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:58,484 - INFO - 目标音频时长: 4.31秒
2025-08-07 17:40:58,484 - INFO - 实际视频时长: 4.34秒
2025-08-07 17:40:58,484 - INFO - 时长差异: 0.04秒 (0.86%)
2025-08-07 17:40:58,484 - INFO - ==========================================
2025-08-07 17:40:58,484 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:58,484 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\31_2.mp4
2025-08-07 17:40:58,485 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp9z378ix3
2025-08-07 17:40:58,528 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:58,528 - INFO -   - 音频时长: 4.31秒
2025-08-07 17:40:58,528 - INFO -   - 视频时长: 4.34秒
2025-08-07 17:40:58,528 - INFO -   - 时长差异: 0.04秒 (0.86%)
2025-08-07 17:40:58,528 - INFO - 
----- 处理字幕 #31 的方案 #3 -----
2025-08-07 17:40:58,528 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\31_3.mp4
2025-08-07 17:40:58,529 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpzswc792w
2025-08-07 17:40:58,529 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1149.mp4 (确认存在: True)
2025-08-07 17:40:58,529 - INFO - 添加场景ID=1149，时长=0.92秒，累计时长=0.92秒
2025-08-07 17:40:58,529 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1159.mp4 (确认存在: True)
2025-08-07 17:40:58,529 - INFO - 添加场景ID=1159，时长=3.20秒，累计时长=4.12秒
2025-08-07 17:40:58,529 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\1147.mp4 (确认存在: True)
2025-08-07 17:40:58,529 - INFO - 添加场景ID=1147，时长=1.60秒，累计时长=5.72秒
2025-08-07 17:40:58,529 - INFO - 准备合并 3 个场景文件，总时长约 5.72秒
2025-08-07 17:40:58,530 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/1149.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1159.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/1147.mp4'

2025-08-07 17:40:58,530 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpzswc792w\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpzswc792w\temp_combined.mp4
2025-08-07 17:40:58,655 - INFO - 合并后的视频时长: 5.79秒，目标音频时长: 4.31秒
2025-08-07 17:40:58,655 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpzswc792w\temp_combined.mp4 -ss 0 -to 4.306 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\31_3.mp4
2025-08-07 17:40:58,930 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:58,930 - INFO - 目标音频时长: 4.31秒
2025-08-07 17:40:58,930 - INFO - 实际视频时长: 4.34秒
2025-08-07 17:40:58,930 - INFO - 时长差异: 0.04秒 (0.86%)
2025-08-07 17:40:58,930 - INFO - ==========================================
2025-08-07 17:40:58,930 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:58,930 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\31_3.mp4
2025-08-07 17:40:58,931 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpzswc792w
2025-08-07 17:40:58,975 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:58,975 - INFO -   - 音频时长: 4.31秒
2025-08-07 17:40:58,975 - INFO -   - 视频时长: 4.34秒
2025-08-07 17:40:58,975 - INFO -   - 时长差异: 0.04秒 (0.86%)
2025-08-07 17:40:58,975 - INFO - 
字幕 #31 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:58,975 - INFO - 生成的视频文件:
2025-08-07 17:40:58,975 - INFO -   1. F:/github/aicut_auto/newcut_ai\31_1.mp4
2025-08-07 17:40:58,975 - INFO -   2. F:/github/aicut_auto/newcut_ai\31_2.mp4
2025-08-07 17:40:58,975 - INFO -   3. F:/github/aicut_auto/newcut_ai\31_3.mp4
2025-08-07 17:40:58,975 - INFO - ========== 字幕 #31 处理结束 ==========

