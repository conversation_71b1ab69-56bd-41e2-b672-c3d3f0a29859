2025-08-07 17:40:50,879 - INFO - ========== 字幕 #26 处理开始 ==========
2025-08-07 17:40:50,879 - INFO - 字幕内容: 女孩却一眼看穿，这血书是伪造的，她回溯过去，看到了二弟求股东签名被拒后自己伪造的画面。
2025-08-07 17:40:50,879 - INFO - 字幕序号: [715, 723]
2025-08-07 17:40:50,879 - INFO - 音频文件详情:
2025-08-07 17:40:50,879 - INFO -   - 路径: output\26.wav
2025-08-07 17:40:50,879 - INFO -   - 时长: 5.30秒
2025-08-07 17:40:50,880 - INFO -   - 验证音频时长: 5.30秒
2025-08-07 17:40:50,880 - INFO - 字幕时间戳信息:
2025-08-07 17:40:50,880 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:50,880 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:50,880 - INFO -   - 根据生成的音频时长(5.30秒)已调整字幕时间戳
2025-08-07 17:40:50,880 - INFO - ========== 新模式：为字幕 #26 生成4套场景方案 ==========
2025-08-07 17:40:50,880 - INFO - 字幕序号列表: [715, 723]
2025-08-07 17:40:50,880 - INFO - 
--- 生成方案 #1：基于字幕序号 #715 ---
2025-08-07 17:40:50,880 - INFO - 开始为单个字幕序号 #715 匹配场景，目标时长: 5.30秒
2025-08-07 17:40:50,880 - INFO - 开始查找字幕序号 [715] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:50,880 - INFO - 找到related_overlap场景: scene_id=882, 字幕#715
2025-08-07 17:40:50,881 - INFO - 字幕 #715 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:50,881 - INFO - 字幕序号 #715 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:50,881 - INFO - 选择第一个overlap场景作为起点: scene_id=882
2025-08-07 17:40:50,881 - INFO - 添加起点场景: scene_id=882, 时长=3.40秒, 累计时长=3.40秒
2025-08-07 17:40:50,881 - INFO - 起点场景时长不足，需要延伸填充 1.90秒
2025-08-07 17:40:50,881 - INFO - 起点场景在原始列表中的索引: 881
2025-08-07 17:40:50,881 - INFO - 延伸添加场景: scene_id=883 (完整时长 1.44秒)
2025-08-07 17:40:50,881 - INFO - 累计时长: 4.84秒
2025-08-07 17:40:50,882 - INFO - 延伸添加场景: scene_id=884 (裁剪至 0.46秒)
2025-08-07 17:40:50,882 - INFO - 累计时长: 5.30秒
2025-08-07 17:40:50,882 - INFO - 字幕序号 #715 场景匹配完成，共选择 3 个场景，总时长: 5.30秒
2025-08-07 17:40:50,882 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-08-07 17:40:50,882 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-08-07 17:40:50,882 - INFO - 
--- 生成方案 #2：基于字幕序号 #723 ---
2025-08-07 17:40:50,882 - INFO - 开始为单个字幕序号 #723 匹配场景，目标时长: 5.30秒
2025-08-07 17:40:50,882 - INFO - 开始查找字幕序号 [723] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:50,882 - INFO - 找到related_overlap场景: scene_id=888, 字幕#723
2025-08-07 17:40:50,882 - INFO - 找到related_between场景: scene_id=889, 字幕#723
2025-08-07 17:40:50,882 - INFO - 找到related_between场景: scene_id=890, 字幕#723
2025-08-07 17:40:50,882 - INFO - 找到related_between场景: scene_id=891, 字幕#723
2025-08-07 17:40:50,883 - INFO - 字幕 #723 找到 1 个overlap场景, 3 个between场景
2025-08-07 17:40:50,883 - INFO - 字幕序号 #723 找到 1 个可用overlap场景, 3 个可用between场景
2025-08-07 17:40:50,883 - INFO - 选择第一个overlap场景作为起点: scene_id=888
2025-08-07 17:40:50,883 - INFO - 添加起点场景: scene_id=888, 时长=2.20秒, 累计时长=2.20秒
2025-08-07 17:40:50,883 - INFO - 起点场景时长不足，需要延伸填充 3.10秒
2025-08-07 17:40:50,883 - INFO - 起点场景在原始列表中的索引: 887
2025-08-07 17:40:50,883 - INFO - 延伸添加场景: scene_id=889 (完整时长 2.08秒)
2025-08-07 17:40:50,883 - INFO - 累计时长: 4.28秒
2025-08-07 17:40:50,883 - INFO - 延伸添加场景: scene_id=890 (裁剪至 1.02秒)
2025-08-07 17:40:50,883 - INFO - 累计时长: 5.30秒
2025-08-07 17:40:50,883 - INFO - 字幕序号 #723 场景匹配完成，共选择 3 个场景，总时长: 5.30秒
2025-08-07 17:40:50,883 - INFO - 方案 #2 生成成功，包含 3 个场景
2025-08-07 17:40:50,883 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:50,883 - INFO - ========== 当前模式：为字幕 #26 生成 1 套场景方案 ==========
2025-08-07 17:40:50,883 - INFO - 开始查找字幕序号 [715, 723] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:50,883 - INFO - 找到related_overlap场景: scene_id=882, 字幕#715
2025-08-07 17:40:50,883 - INFO - 找到related_overlap场景: scene_id=888, 字幕#723
2025-08-07 17:40:50,884 - INFO - 找到related_between场景: scene_id=889, 字幕#723
2025-08-07 17:40:50,884 - INFO - 找到related_between场景: scene_id=890, 字幕#723
2025-08-07 17:40:50,884 - INFO - 找到related_between场景: scene_id=891, 字幕#723
2025-08-07 17:40:50,884 - INFO - 字幕 #715 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:50,884 - INFO - 字幕 #723 找到 1 个overlap场景, 3 个between场景
2025-08-07 17:40:50,884 - INFO - 共收集 2 个未使用的overlap场景和 3 个未使用的between场景
2025-08-07 17:40:50,884 - INFO - 开始生成方案 #1
2025-08-07 17:40:50,884 - INFO - 方案 #1: 为字幕#715选择初始化overlap场景id=882
2025-08-07 17:40:50,884 - INFO - 方案 #1: 为字幕#723选择初始化overlap场景id=888
2025-08-07 17:40:50,884 - INFO - 方案 #1: 初始选择后，当前总时长=5.60秒
2025-08-07 17:40:50,884 - INFO - 方案 #1: 额外between选择后，当前总时长=5.60秒
2025-08-07 17:40:50,884 - INFO - 方案 #1: 场景总时长(5.60秒)大于音频时长(5.30秒)，需要裁剪
2025-08-07 17:40:50,884 - INFO - 调整前总时长: 5.60秒, 目标时长: 5.30秒
2025-08-07 17:40:50,884 - INFO - 需要裁剪 0.30秒
2025-08-07 17:40:50,884 - INFO - 裁剪最长场景ID=882：从3.40秒裁剪至3.10秒
2025-08-07 17:40:50,884 - INFO - 调整后总时长: 5.30秒，与目标时长差异: 0.00秒
2025-08-07 17:40:50,884 - INFO - 方案 #1 调整/填充后最终总时长: 5.30秒
2025-08-07 17:40:50,884 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:50,884 - INFO - ========== 当前模式：字幕 #26 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:50,884 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:50,884 - INFO - ========== 新模式：字幕 #26 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:50,885 - INFO - 
----- 处理字幕 #26 的方案 #1 -----
2025-08-07 17:40:50,885 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\26_1.mp4
2025-08-07 17:40:50,885 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp20j6ebge
2025-08-07 17:40:50,885 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\882.mp4 (确认存在: True)
2025-08-07 17:40:50,885 - INFO - 添加场景ID=882，时长=3.40秒，累计时长=3.40秒
2025-08-07 17:40:50,886 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\883.mp4 (确认存在: True)
2025-08-07 17:40:50,886 - INFO - 添加场景ID=883，时长=1.44秒，累计时长=4.84秒
2025-08-07 17:40:50,886 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\884.mp4 (确认存在: True)
2025-08-07 17:40:50,886 - INFO - 添加场景ID=884，时长=1.40秒，累计时长=6.24秒
2025-08-07 17:40:50,886 - INFO - 准备合并 3 个场景文件，总时长约 6.24秒
2025-08-07 17:40:50,886 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/882.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/883.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/884.mp4'

2025-08-07 17:40:50,886 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp20j6ebge\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp20j6ebge\temp_combined.mp4
2025-08-07 17:40:51,021 - INFO - 合并后的视频时长: 6.31秒，目标音频时长: 5.30秒
2025-08-07 17:40:51,021 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp20j6ebge\temp_combined.mp4 -ss 0 -to 5.3 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\26_1.mp4
2025-08-07 17:40:51,356 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:51,356 - INFO - 目标音频时长: 5.30秒
2025-08-07 17:40:51,356 - INFO - 实际视频时长: 5.34秒
2025-08-07 17:40:51,356 - INFO - 时长差异: 0.04秒 (0.81%)
2025-08-07 17:40:51,356 - INFO - ==========================================
2025-08-07 17:40:51,356 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:51,356 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\26_1.mp4
2025-08-07 17:40:51,358 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp20j6ebge
2025-08-07 17:40:51,401 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:51,401 - INFO -   - 音频时长: 5.30秒
2025-08-07 17:40:51,401 - INFO -   - 视频时长: 5.34秒
2025-08-07 17:40:51,401 - INFO -   - 时长差异: 0.04秒 (0.81%)
2025-08-07 17:40:51,402 - INFO - 
----- 处理字幕 #26 的方案 #2 -----
2025-08-07 17:40:51,402 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\26_2.mp4
2025-08-07 17:40:51,402 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5ayy3nbv
2025-08-07 17:40:51,402 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\888.mp4 (确认存在: True)
2025-08-07 17:40:51,402 - INFO - 添加场景ID=888，时长=2.20秒，累计时长=2.20秒
2025-08-07 17:40:51,402 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\889.mp4 (确认存在: True)
2025-08-07 17:40:51,403 - INFO - 添加场景ID=889，时长=2.08秒，累计时长=4.28秒
2025-08-07 17:40:51,403 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\890.mp4 (确认存在: True)
2025-08-07 17:40:51,403 - INFO - 添加场景ID=890，时长=1.84秒，累计时长=6.12秒
2025-08-07 17:40:51,403 - INFO - 准备合并 3 个场景文件，总时长约 6.12秒
2025-08-07 17:40:51,403 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/888.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/889.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/890.mp4'

2025-08-07 17:40:51,403 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmp5ayy3nbv\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmp5ayy3nbv\temp_combined.mp4
2025-08-07 17:40:51,532 - INFO - 合并后的视频时长: 6.19秒，目标音频时长: 5.30秒
2025-08-07 17:40:51,532 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmp5ayy3nbv\temp_combined.mp4 -ss 0 -to 5.3 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\26_2.mp4
2025-08-07 17:40:51,835 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:51,835 - INFO - 目标音频时长: 5.30秒
2025-08-07 17:40:51,835 - INFO - 实际视频时长: 5.34秒
2025-08-07 17:40:51,835 - INFO - 时长差异: 0.04秒 (0.81%)
2025-08-07 17:40:51,835 - INFO - ==========================================
2025-08-07 17:40:51,835 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:51,835 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\26_2.mp4
2025-08-07 17:40:51,835 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp5ayy3nbv
2025-08-07 17:40:51,880 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:51,880 - INFO -   - 音频时长: 5.30秒
2025-08-07 17:40:51,880 - INFO -   - 视频时长: 5.34秒
2025-08-07 17:40:51,880 - INFO -   - 时长差异: 0.04秒 (0.81%)
2025-08-07 17:40:51,880 - INFO - 
----- 处理字幕 #26 的方案 #3 -----
2025-08-07 17:40:51,880 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\26_3.mp4
2025-08-07 17:40:51,880 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpd_3_yp20
2025-08-07 17:40:51,881 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\882.mp4 (确认存在: True)
2025-08-07 17:40:51,881 - INFO - 添加场景ID=882，时长=3.40秒，累计时长=3.40秒
2025-08-07 17:40:51,881 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\888.mp4 (确认存在: True)
2025-08-07 17:40:51,881 - INFO - 添加场景ID=888，时长=2.20秒，累计时长=5.60秒
2025-08-07 17:40:51,881 - INFO - 准备合并 2 个场景文件，总时长约 5.60秒
2025-08-07 17:40:51,881 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/882.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/888.mp4'

2025-08-07 17:40:51,881 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpd_3_yp20\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpd_3_yp20\temp_combined.mp4
2025-08-07 17:40:52,003 - INFO - 合并后的视频时长: 5.65秒，目标音频时长: 5.30秒
2025-08-07 17:40:52,003 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpd_3_yp20\temp_combined.mp4 -ss 0 -to 5.3 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\26_3.mp4
2025-08-07 17:40:52,347 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:52,347 - INFO - 目标音频时长: 5.30秒
2025-08-07 17:40:52,347 - INFO - 实际视频时长: 5.34秒
2025-08-07 17:40:52,347 - INFO - 时长差异: 0.04秒 (0.81%)
2025-08-07 17:40:52,347 - INFO - ==========================================
2025-08-07 17:40:52,347 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:52,347 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\26_3.mp4
2025-08-07 17:40:52,347 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpd_3_yp20
2025-08-07 17:40:52,392 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:52,392 - INFO -   - 音频时长: 5.30秒
2025-08-07 17:40:52,392 - INFO -   - 视频时长: 5.34秒
2025-08-07 17:40:52,392 - INFO -   - 时长差异: 0.04秒 (0.81%)
2025-08-07 17:40:52,392 - INFO - 
字幕 #26 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:52,392 - INFO - 生成的视频文件:
2025-08-07 17:40:52,392 - INFO -   1. F:/github/aicut_auto/newcut_ai\26_1.mp4
2025-08-07 17:40:52,392 - INFO -   2. F:/github/aicut_auto/newcut_ai\26_2.mp4
2025-08-07 17:40:52,392 - INFO -   3. F:/github/aicut_auto/newcut_ai\26_3.mp4
2025-08-07 17:40:52,392 - INFO - ========== 字幕 #26 处理结束 ==========

