2025-08-07 17:40:22,381 - INFO - ========== 字幕 #7 处理开始 ==========
2025-08-07 17:40:22,381 - INFO - 字幕内容: 就在众人围攻男人时，女孩却一语道破荒谬之处：一个四肢健全的女人，怎么会被一个残废欺负？
2025-08-07 17:40:22,381 - INFO - 字幕序号: [81, 86]
2025-08-07 17:40:22,381 - INFO - 音频文件详情:
2025-08-07 17:40:22,381 - INFO -   - 路径: output\7.wav
2025-08-07 17:40:22,381 - INFO -   - 时长: 4.98秒
2025-08-07 17:40:22,381 - INFO -   - 验证音频时长: 4.98秒
2025-08-07 17:40:22,381 - INFO - 字幕时间戳信息:
2025-08-07 17:40:22,382 - INFO -   - 中文字幕文件: F:\github\aicut_auto\newcut\combined.srt
2025-08-07 17:40:22,382 - INFO -   - 英文字幕文件: F:\github\aicut_auto\en.srt
2025-08-07 17:40:22,382 - INFO -   - 根据生成的音频时长(4.98秒)已调整字幕时间戳
2025-08-07 17:40:22,382 - INFO - ========== 新模式：为字幕 #7 生成4套场景方案 ==========
2025-08-07 17:40:22,382 - INFO - 字幕序号列表: [81, 86]
2025-08-07 17:40:22,382 - INFO - 
--- 生成方案 #1：基于字幕序号 #81 ---
2025-08-07 17:40:22,382 - INFO - 开始为单个字幕序号 #81 匹配场景，目标时长: 4.98秒
2025-08-07 17:40:22,382 - INFO - 开始查找字幕序号 [81] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:22,382 - INFO - 找到related_overlap场景: scene_id=126, 字幕#81
2025-08-07 17:40:22,382 - INFO - 找到related_overlap场景: scene_id=128, 字幕#81
2025-08-07 17:40:22,383 - INFO - 字幕 #81 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:40:22,383 - INFO - 字幕序号 #81 找到 2 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:22,383 - INFO - 选择第一个overlap场景作为起点: scene_id=126
2025-08-07 17:40:22,383 - INFO - 添加起点场景: scene_id=126, 时长=1.68秒, 累计时长=1.68秒
2025-08-07 17:40:22,383 - INFO - 起点场景时长不足，需要延伸填充 3.30秒
2025-08-07 17:40:22,383 - INFO - 起点场景在原始列表中的索引: 125
2025-08-07 17:40:22,383 - INFO - 延伸添加场景: scene_id=127 (完整时长 1.92秒)
2025-08-07 17:40:22,383 - INFO - 累计时长: 3.60秒
2025-08-07 17:40:22,383 - INFO - 延伸添加场景: scene_id=128 (裁剪至 1.38秒)
2025-08-07 17:40:22,383 - INFO - 累计时长: 4.98秒
2025-08-07 17:40:22,383 - INFO - 字幕序号 #81 场景匹配完成，共选择 3 个场景，总时长: 4.98秒
2025-08-07 17:40:22,383 - INFO - 方案 #1 生成成功，包含 3 个场景
2025-08-07 17:40:22,383 - INFO - 新模式：第1套方案的 3 个场景已加入全局已使用集合
2025-08-07 17:40:22,383 - INFO - 
--- 生成方案 #2：基于字幕序号 #86 ---
2025-08-07 17:40:22,383 - INFO - 开始为单个字幕序号 #86 匹配场景，目标时长: 4.98秒
2025-08-07 17:40:22,383 - INFO - 开始查找字幕序号 [86] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:22,383 - INFO - 找到related_overlap场景: scene_id=131, 字幕#86
2025-08-07 17:40:22,384 - INFO - 字幕 #86 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:22,385 - INFO - 字幕序号 #86 找到 1 个可用overlap场景, 0 个可用between场景
2025-08-07 17:40:22,385 - INFO - 选择第一个overlap场景作为起点: scene_id=131
2025-08-07 17:40:22,385 - INFO - 添加起点场景: scene_id=131, 时长=2.52秒, 累计时长=2.52秒
2025-08-07 17:40:22,385 - INFO - 起点场景时长不足，需要延伸填充 2.46秒
2025-08-07 17:40:22,385 - INFO - 起点场景在原始列表中的索引: 130
2025-08-07 17:40:22,385 - INFO - 延伸添加场景: scene_id=132 (裁剪至 2.46秒)
2025-08-07 17:40:22,385 - INFO - 累计时长: 4.98秒
2025-08-07 17:40:22,385 - INFO - 字幕序号 #86 场景匹配完成，共选择 2 个场景，总时长: 4.98秒
2025-08-07 17:40:22,385 - INFO - 方案 #2 生成成功，包含 2 个场景
2025-08-07 17:40:22,385 - INFO - 
--- 生成方案 #3：使用传统模式 ---
2025-08-07 17:40:22,385 - INFO - ========== 当前模式：为字幕 #7 生成 1 套场景方案 ==========
2025-08-07 17:40:22,385 - INFO - 开始查找字幕序号 [81, 86] 对应的场景，共有 3480 个场景可选
2025-08-07 17:40:22,385 - INFO - 找到related_overlap场景: scene_id=126, 字幕#81
2025-08-07 17:40:22,385 - INFO - 找到related_overlap场景: scene_id=128, 字幕#81
2025-08-07 17:40:22,385 - INFO - 找到related_overlap场景: scene_id=131, 字幕#86
2025-08-07 17:40:22,386 - INFO - 字幕 #81 找到 2 个overlap场景, 0 个between场景
2025-08-07 17:40:22,386 - INFO - 字幕 #86 找到 1 个overlap场景, 0 个between场景
2025-08-07 17:40:22,386 - INFO - 共收集 3 个未使用的overlap场景和 0 个未使用的between场景
2025-08-07 17:40:22,386 - INFO - 开始生成方案 #1
2025-08-07 17:40:22,386 - INFO - 方案 #1: 为字幕#81选择初始化overlap场景id=126
2025-08-07 17:40:22,386 - INFO - 方案 #1: 为字幕#86选择初始化overlap场景id=131
2025-08-07 17:40:22,386 - INFO - 方案 #1: 初始选择后，当前总时长=4.20秒
2025-08-07 17:40:22,386 - INFO - 方案 #1: 额外添加overlap场景id=128, 当前总时长=6.60秒
2025-08-07 17:40:22,386 - INFO - 方案 #1: 额外between选择后，当前总时长=6.60秒
2025-08-07 17:40:22,386 - INFO - 方案 #1: 场景总时长(6.60秒)大于音频时长(4.98秒)，需要裁剪
2025-08-07 17:40:22,386 - INFO - 调整前总时长: 6.60秒, 目标时长: 4.98秒
2025-08-07 17:40:22,386 - INFO - 需要裁剪 1.62秒
2025-08-07 17:40:22,386 - INFO - 裁剪单个场景会导致时长过短，尝试裁剪多个场景
2025-08-07 17:40:22,386 - INFO - 裁剪场景ID=131：从2.52秒裁剪至1.00秒
2025-08-07 17:40:22,386 - INFO - 裁剪场景ID=128：从2.40秒裁剪至2.30秒
2025-08-07 17:40:22,386 - INFO - 调整后总时长: 4.98秒，与目标时长差异: 0.00秒
2025-08-07 17:40:22,386 - INFO - 方案 #1 调整/填充后最终总时长: 4.98秒
2025-08-07 17:40:22,386 - INFO - 方案 #1 添加到方案列表
2025-08-07 17:40:22,386 - INFO - ========== 当前模式：字幕 #7 的 1 套有效场景方案生成完成 ==========
2025-08-07 17:40:22,386 - INFO - 方案 #3 (传统模式) 生成成功
2025-08-07 17:40:22,386 - INFO - ========== 新模式：字幕 #7 共生成 3 套有效场景方案 ==========
2025-08-07 17:40:22,386 - INFO - 
----- 处理字幕 #7 的方案 #1 -----
2025-08-07 17:40:22,386 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\7_1.mp4
2025-08-07 17:40:22,387 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplus39bo1
2025-08-07 17:40:22,387 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\126.mp4 (确认存在: True)
2025-08-07 17:40:22,387 - INFO - 添加场景ID=126，时长=1.68秒，累计时长=1.68秒
2025-08-07 17:40:22,387 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\127.mp4 (确认存在: True)
2025-08-07 17:40:22,387 - INFO - 添加场景ID=127，时长=1.92秒，累计时长=3.60秒
2025-08-07 17:40:22,387 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\128.mp4 (确认存在: True)
2025-08-07 17:40:22,387 - INFO - 添加场景ID=128，时长=2.40秒，累计时长=6.00秒
2025-08-07 17:40:22,387 - INFO - 准备合并 3 个场景文件，总时长约 6.00秒
2025-08-07 17:40:22,387 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/126.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/127.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/128.mp4'

2025-08-07 17:40:22,387 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmplus39bo1\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmplus39bo1\temp_combined.mp4
2025-08-07 17:40:22,550 - INFO - 合并后的视频时长: 6.07秒，目标音频时长: 4.98秒
2025-08-07 17:40:22,550 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmplus39bo1\temp_combined.mp4 -ss 0 -to 4.982 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\7_1.mp4
2025-08-07 17:40:22,877 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:22,877 - INFO - 目标音频时长: 4.98秒
2025-08-07 17:40:22,877 - INFO - 实际视频时长: 5.02秒
2025-08-07 17:40:22,877 - INFO - 时长差异: 0.04秒 (0.82%)
2025-08-07 17:40:22,878 - INFO - ==========================================
2025-08-07 17:40:22,878 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:22,878 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\7_1.mp4
2025-08-07 17:40:22,878 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmplus39bo1
2025-08-07 17:40:22,923 - INFO - 方案 #1 处理完成:
2025-08-07 17:40:22,923 - INFO -   - 音频时长: 4.98秒
2025-08-07 17:40:22,923 - INFO -   - 视频时长: 5.02秒
2025-08-07 17:40:22,923 - INFO -   - 时长差异: 0.04秒 (0.82%)
2025-08-07 17:40:22,924 - INFO - 
----- 处理字幕 #7 的方案 #2 -----
2025-08-07 17:40:22,924 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\7_2.mp4
2025-08-07 17:40:22,924 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpfpwrabtq
2025-08-07 17:40:22,924 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\131.mp4 (确认存在: True)
2025-08-07 17:40:22,924 - INFO - 添加场景ID=131，时长=2.52秒，累计时长=2.52秒
2025-08-07 17:40:22,924 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\132.mp4 (确认存在: True)
2025-08-07 17:40:22,925 - INFO - 添加场景ID=132，时长=2.48秒，累计时长=5.00秒
2025-08-07 17:40:22,925 - INFO - 准备合并 2 个场景文件，总时长约 5.00秒
2025-08-07 17:40:22,925 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/131.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/132.mp4'

2025-08-07 17:40:22,925 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpfpwrabtq\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpfpwrabtq\temp_combined.mp4
2025-08-07 17:40:23,054 - INFO - 合并后的视频时长: 5.05秒，目标音频时长: 4.98秒
2025-08-07 17:40:23,054 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpfpwrabtq\temp_combined.mp4 -ss 0 -to 4.982 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\7_2.mp4
2025-08-07 17:40:23,332 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:23,332 - INFO - 目标音频时长: 4.98秒
2025-08-07 17:40:23,332 - INFO - 实际视频时长: 5.02秒
2025-08-07 17:40:23,333 - INFO - 时长差异: 0.04秒 (0.82%)
2025-08-07 17:40:23,333 - INFO - ==========================================
2025-08-07 17:40:23,333 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:23,333 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\7_2.mp4
2025-08-07 17:40:23,333 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpfpwrabtq
2025-08-07 17:40:23,377 - INFO - 方案 #2 处理完成:
2025-08-07 17:40:23,377 - INFO -   - 音频时长: 4.98秒
2025-08-07 17:40:23,377 - INFO -   - 视频时长: 5.02秒
2025-08-07 17:40:23,377 - INFO -   - 时长差异: 0.04秒 (0.82%)
2025-08-07 17:40:23,377 - INFO - 
----- 处理字幕 #7 的方案 #3 -----
2025-08-07 17:40:23,377 - INFO - 目标视频文件: F:/github/aicut_auto/newcut_ai\7_3.mp4
2025-08-07 17:40:23,377 - INFO - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpc2sq4m6m
2025-08-07 17:40:23,378 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\126.mp4 (确认存在: True)
2025-08-07 17:40:23,378 - INFO - 添加场景ID=126，时长=1.68秒，累计时长=1.68秒
2025-08-07 17:40:23,378 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\131.mp4 (确认存在: True)
2025-08-07 17:40:23,378 - INFO - 添加场景ID=131，时长=2.52秒，累计时长=4.20秒
2025-08-07 17:40:23,378 - INFO - 添加场景文件: F:\github\aicut_auto\ai-video-splitter\128.mp4 (确认存在: True)
2025-08-07 17:40:23,378 - INFO - 添加场景ID=128，时长=2.40秒，累计时长=6.60秒
2025-08-07 17:40:23,378 - INFO - 准备合并 3 个场景文件，总时长约 6.60秒
2025-08-07 17:40:23,378 - INFO - 生成的文件列表内容:
file 'F:/github/aicut_auto/ai-video-splitter/126.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/131.mp4'
file 'F:/github/aicut_auto/ai-video-splitter/128.mp4'

2025-08-07 17:40:23,378 - INFO - 合并命令: ffmpeg -y -f concat -safe 0 -i C:\Users\<USER>\AppData\Local\Temp\tmpc2sq4m6m\file_list.txt -c copy C:\Users\<USER>\AppData\Local\Temp\tmpc2sq4m6m\temp_combined.mp4
2025-08-07 17:40:23,532 - INFO - 合并后的视频时长: 6.67秒，目标音频时长: 4.98秒
2025-08-07 17:40:23,532 - INFO - 精确裁剪命令: ffmpeg -y -i C:\Users\<USER>\AppData\Local\Temp\tmpc2sq4m6m\temp_combined.mp4 -ss 0 -to 4.982 -c:v libx264 -crf 18 -preset ultrafast -avoid_negative_ts 1 -video_track_timescale 90000 F:/github/aicut_auto/newcut_ai\7_3.mp4
2025-08-07 17:40:23,831 - INFO - ============ 视频与音频时长比较 ============
2025-08-07 17:40:23,831 - INFO - 目标音频时长: 4.98秒
2025-08-07 17:40:23,831 - INFO - 实际视频时长: 5.02秒
2025-08-07 17:40:23,831 - INFO - 时长差异: 0.04秒 (0.82%)
2025-08-07 17:40:23,831 - INFO - ==========================================
2025-08-07 17:40:23,831 - INFO - 成功: 输出视频时长与目标时长匹配良好，误差在允许范围内
2025-08-07 17:40:23,831 - INFO - 视频合并和裁剪完成: F:/github/aicut_auto/newcut_ai\7_3.mp4
2025-08-07 17:40:23,832 - INFO - 清理临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpc2sq4m6m
2025-08-07 17:40:23,876 - INFO - 方案 #3 处理完成:
2025-08-07 17:40:23,876 - INFO -   - 音频时长: 4.98秒
2025-08-07 17:40:23,876 - INFO -   - 视频时长: 5.02秒
2025-08-07 17:40:23,876 - INFO -   - 时长差异: 0.04秒 (0.82%)
2025-08-07 17:40:23,876 - INFO - 
字幕 #7 处理完成，成功生成 3/3 套方案
2025-08-07 17:40:23,876 - INFO - 生成的视频文件:
2025-08-07 17:40:23,876 - INFO -   1. F:/github/aicut_auto/newcut_ai\7_1.mp4
2025-08-07 17:40:23,876 - INFO -   2. F:/github/aicut_auto/newcut_ai\7_2.mp4
2025-08-07 17:40:23,876 - INFO -   3. F:/github/aicut_auto/newcut_ai\7_3.mp4
2025-08-07 17:40:23,876 - INFO - ========== 字幕 #7 处理结束 ==========

